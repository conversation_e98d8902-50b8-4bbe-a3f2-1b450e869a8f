<?php
/**
 * 创建用户地址表
 */

header('Content-Type: application/json; charset=utf-8');

try {
    // 加载数据库
    require_once __DIR__ . '/api/core/Database.php';
    
    $db = Database::getInstance();
    
    // 创建用户地址表的SQL
    $sql = "
    CREATE TABLE IF NOT EXISTS `user_addresses` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `user_id` int(11) NOT NULL COMMENT '用户ID',
      `name` varchar(50) NOT NULL COMMENT '收货人姓名',
      `phone` varchar(20) NOT NULL COMMENT '收货人电话',
      `province` varchar(50) NOT NULL COMMENT '省份',
      `city` varchar(50) NOT NULL COMMENT '城市',
      `district` varchar(50) NOT NULL COMMENT '区县',
      `detail` varchar(200) NOT NULL COMMENT '详细地址',
      `is_default` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否默认地址',
      `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
      `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
      PRIMARY KEY (`id`),
      KEY `idx_user_id` (`user_id`),
      KEY `idx_is_default` (`is_default`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户地址表'
    ";
    
    // 执行SQL
    $db->query($sql);
    
    echo json_encode([
        'status' => 'success',
        'message' => 'user_addresses 表创建成功'
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ], JSON_UNESCAPED_UNICODE);
}
?>
