{"version": 3, "file": "index.js", "sources": ["api/index.js"], "sourcesContent": ["import { get, post, put, del } from '@/utils/request.js'\n\n// 认证相关API\nexport const authAPI = {\n\t// 微信登录\n\tlogin: (data) => post('/auth/login', data),\n\n\t// 获取用户信息\n\tgetUserInfo: () => get('/auth/me'),\n\n\t// 验证token\n\tverifyToken: (token) => post('/auth/verify', { token }),\n\n\t// 刷新token\n\trefreshToken: () => post('/auth/refresh'),\n\n\t// 退出登录\n\tlogout: (token) => post('/auth/logout', { token })\n}\n\n// 用户相关API\nexport const userAPI = {\n\t// 获取用户详情\n\tgetProfile: () => get('/user/profile'),\n\n\t// 更新用户信息\n\tupdateProfile: (data) => put('/user/profile', data),\n\n\t// 获取用户统计\n\tgetStats: () => get('/user/stats')\n}\n\n// 积分相关API\nexport const pointsAPI = {\n\t// 获取积分余额\n\tgetBalance: () => get('/points/balance'),\n\n\t// 获取积分记录\n\tgetRecords: (params) => get('/points/records', params),\n\n\t// 积分兑换\n\texchange: (data) => post('/points/exchange', data),\n\n\t// 获取积分规则\n\tgetRules: () => get('/points/rules'),\n\n\t// 签到\n\tsignIn: () => post('/points/sign'),\n\n\t// 获取签到状态\n\tgetSignStatus: () => get('/points/sign/status')\n}\n\n// 商品相关API\nexport const productAPI = {\n\t// 获取商品列表\n\tgetList: (params) => get('/products', params),\n\n\t// 获取商品详情\n\tgetDetail: (id) => get(`/products/${id}`),\n\n\t// 获取商品分类\n\tgetCategories: () => get('/categories'),\n\n\t// 搜索商品\n\tsearch: (keyword, params) => get('/products/search', { keyword, ...params })\n}\n\n// 订单相关API\nexport const orderAPI = {\n\t// 创建订单\n\tcreate: (data) => post('/orders', data),\n\n\t// 获取订单列表\n\tgetList: (params) => get('/orders', params),\n\n\t// 获取订单详情\n\tgetDetail: (id) => get(`/orders/${id}`),\n\n\t// 取消订单\n\tcancel: (id) => put(`/orders/${id}/cancel`),\n\n\t// 确认收货\n\tconfirm: (id) => put(`/orders/${id}/confirm`),\n\n\t// 提交公域订单（积分奖励）\n\tsubmitPublicOrder: (data) => post('/orders/public-submit', data),\n\n\t// 获取公域订单列表\n\tgetPublicOrders: (params) => get('/orders/public', params)\n}\n\n// 活动相关API\nexport const activityAPI = {\n\t// 获取活动列表\n\tgetList: () => get('/activities'),\n\n\t// 参与签到\n\tsignIn: () => post('/activities/sign'),\n\n\t// 获取签到记录\n\tgetSignRecords: () => get('/activities/sign/records'),\n\n\t// 转盘抽奖\n\tlottery: () => post('/activities/lottery'),\n\n\t// 获取抽奖记录\n\tgetLotteryRecords: () => get('/activities/lottery/records'),\n\n\t// 获取任务列表\n\tgetTasks: () => get('/activities/tasks'),\n\n\t// 完成任务\n\tcompleteTask: (taskId) => post(`/activities/tasks/${taskId}/complete`)\n}\n\n// 地址相关API\nexport const addressAPI = {\n\t// 获取地址列表\n\tgetList: () => get('/addresses'),\n\n\t// 获取地址详情\n\tgetDetail: (id) => get(`/addresses/${id}`),\n\n\t// 添加地址\n\tcreate: (data) => post('/addresses', data),\n\n\t// 更新地址\n\tupdate: (id, data) => put(`/addresses/${id}`, data),\n\n\t// 删除地址\n\tdelete: (id) => del(`/addresses/${id}`),\n\n\t// 设置默认地址\n\tsetDefault: (id) => put(`/addresses/${id}/default`),\n\n\t// 获取默认地址\n\tgetDefault: () => get('/addresses/default')\n}\n\n// 收藏相关API\nexport const favoriteAPI = {\n\t// 获取收藏列表\n\tgetList: (params) => get('/favorites', params),\n\n\t// 添加收藏\n\tadd: (productId) => post('/favorites', { product_id: productId }),\n\n\t// 取消收藏\n\tremove: (productId) => del(`/favorites/${productId}`)\n}\n\n// 优惠券相关API\nexport const couponAPI = {\n\t// 获取优惠券列表\n\tgetList: (params) => get('/coupons', params),\n\n\t// 领取优惠券\n\treceive: (couponId) => post(`/coupons/${couponId}/receive`),\n\n\t// 获取我的优惠券\n\tgetMyCoupons: (params) => get('/coupons/my', params)\n}\n\n// 系统相关API\nexport const systemAPI = {\n\t// 获取系统配置\n\tgetConfig: () => get('/system/config'),\n\n\t// 获取应用配置（包含LOGO等）\n\tgetAppConfig: () => get('/system/app-config'),\n\n\t// 获取轮播图\n\tgetBanners: () => get('/system/banners'),\n\n\t// 获取活动配置\n\tgetActivities: () => get('/system/activities'),\n\n\t// 意见反馈\n\tfeedback: (data) => post('/system/feedback', data),\n\n\t// 获取帮助文档\n\tgetHelp: () => get('/system/help'),\n\n\t// 检查更新\n\tcheckUpdate: () => get('/system/update')\n}\n\n// 统计相关API\nexport const statsAPI = {\n\t// 上报页面访问\n\treportPageView: (page) => post('/stats/page-view', { page }),\n\n\t// 上报用户行为\n\treportAction: (action, data) => post('/stats/action', { action, data })\n}\n\nexport default {\n\tauthAPI,\n\tuserAPI,\n\tpointsAPI,\n\tproductAPI,\n\torderAPI,\n\tactivityAPI,\n\taddressAPI,\n\tfavoriteAPI,\n\tcouponAPI,\n\tsystemAPI,\n\tstatsAPI\n}\n"], "names": ["post", "get", "put", "del"], "mappings": ";;AAGY,MAAC,UAAU;AAAA;AAAA,EAEtB,OAAO,CAAC,SAASA,mBAAK,eAAe,IAAI;AAAA;AAAA,EAGzC,aAAa,MAAMC,cAAG,IAAC,UAAU;AAAA;AAAA,EAGjC,aAAa,CAAC,UAAUD,cAAAA,KAAK,gBAAgB,EAAE,MAAK,CAAE;AAAA;AAAA,EAGtD,cAAc,MAAMA,cAAI,KAAC,eAAe;AAAA;AAAA,EAGxC,QAAQ,CAAC,UAAUA,cAAAA,KAAK,gBAAgB,EAAE,MAAK,CAAE;AAClD;AAGY,MAAC,UAAU;AAAA;AAAA,EAEtB,YAAY,MAAMC,cAAG,IAAC,eAAe;AAAA;AAAA,EAGrC,eAAe,CAAC,SAASC,kBAAI,iBAAiB,IAAI;AAAA;AAAA,EAGlD,UAAU,MAAMD,cAAG,IAAC,aAAa;AAClC;AAGY,MAAC,YAAY;AAAA;AAAA,EAExB,YAAY,MAAMA,cAAG,IAAC,iBAAiB;AAAA;AAAA,EAGvC,YAAY,CAAC,WAAWA,kBAAI,mBAAmB,MAAM;AAAA;AAAA,EAGrD,UAAU,CAAC,SAASD,mBAAK,oBAAoB,IAAI;AAAA;AAAA,EAGjD,UAAU,MAAMC,cAAG,IAAC,eAAe;AAAA;AAAA,EAGnC,QAAQ,MAAMD,cAAI,KAAC,cAAc;AAAA;AAAA,EAGjC,eAAe,MAAMC,cAAG,IAAC,qBAAqB;AAC/C;AAGY,MAAC,aAAa;AAAA;AAAA,EAEzB,SAAS,CAAC,WAAWA,kBAAI,aAAa,MAAM;AAAA;AAAA,EAG5C,WAAW,CAAC,OAAOA,cAAAA,IAAI,aAAa,EAAE,EAAE;AAAA;AAAA,EAGxC,eAAe,MAAMA,cAAG,IAAC,aAAa;AAAA;AAAA,EAGtC,QAAQ,CAAC,SAAS,WAAWA,cAAAA,IAAI,oBAAoB,EAAE,SAAS,GAAG,QAAQ;AAC5E;AAGY,MAAC,WAAW;AAAA;AAAA,EAEvB,QAAQ,CAAC,SAASD,mBAAK,WAAW,IAAI;AAAA;AAAA,EAGtC,SAAS,CAAC,WAAWC,kBAAI,WAAW,MAAM;AAAA;AAAA,EAG1C,WAAW,CAAC,OAAOA,cAAAA,IAAI,WAAW,EAAE,EAAE;AAAA;AAAA,EAGtC,QAAQ,CAAC,OAAOC,cAAG,IAAC,WAAW,EAAE,SAAS;AAAA;AAAA,EAG1C,SAAS,CAAC,OAAOA,cAAG,IAAC,WAAW,EAAE,UAAU;AAAA;AAAA,EAG5C,mBAAmB,CAAC,SAASF,mBAAK,yBAAyB,IAAI;AAAA;AAAA,EAG/D,iBAAiB,CAAC,WAAWC,kBAAI,kBAAkB,MAAM;AAC1D;AA2BY,MAAC,aAAa;AAAA;AAAA,EAEzB,SAAS,MAAMA,cAAG,IAAC,YAAY;AAAA;AAAA,EAG/B,WAAW,CAAC,OAAOA,cAAAA,IAAI,cAAc,EAAE,EAAE;AAAA;AAAA,EAGzC,QAAQ,CAAC,SAASD,mBAAK,cAAc,IAAI;AAAA;AAAA,EAGzC,QAAQ,CAAC,IAAI,SAASE,cAAAA,IAAI,cAAc,EAAE,IAAI,IAAI;AAAA;AAAA,EAGlD,QAAQ,CAAC,OAAOC,cAAAA,IAAI,cAAc,EAAE,EAAE;AAAA;AAAA,EAGtC,YAAY,CAAC,OAAOD,cAAG,IAAC,cAAc,EAAE,UAAU;AAAA;AAAA,EAGlD,YAAY,MAAMD,cAAG,IAAC,oBAAoB;AAC3C;AA2BY,MAAC,YAAY;AAAA;AAAA,EAExB,WAAW,MAAMA,cAAG,IAAC,gBAAgB;AAAA;AAAA,EAGrC,cAAc,MAAMA,cAAG,IAAC,oBAAoB;AAAA;AAAA,EAG5C,YAAY,MAAMA,cAAG,IAAC,iBAAiB;AAAA;AAAA,EAGvC,eAAe,MAAMA,cAAG,IAAC,oBAAoB;AAAA;AAAA,EAG7C,UAAU,CAAC,SAASD,mBAAK,oBAAoB,IAAI;AAAA;AAAA,EAGjD,SAAS,MAAMC,cAAG,IAAC,cAAc;AAAA;AAAA,EAGjC,aAAa,MAAMA,cAAG,IAAC,gBAAgB;AACxC;;;;;;;;"}