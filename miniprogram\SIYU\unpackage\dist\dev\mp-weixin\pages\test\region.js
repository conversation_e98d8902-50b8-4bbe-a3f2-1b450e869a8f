"use strict";
const common_vendor = require("../../common/vendor.js");
const RegionPicker = () => "../../components/region-picker/region-picker.js";
const _sfc_main = {
  components: {
    RegionPicker
  },
  data() {
    return {
      pickerVisible: false,
      selectedRegion: {
        province: "",
        city: "",
        district: ""
      }
    };
  },
  methods: {
    showPicker() {
      this.pickerVisible = true;
    },
    onRegionConfirm(region) {
      this.selectedRegion = {
        province: region.province,
        city: region.city,
        district: region.district
      };
      common_vendor.index.__f__("log", "at pages/test/region.vue:72", "选择的地区:", region);
    }
  }
};
if (!Array) {
  const _easycom_region_picker2 = common_vendor.resolveComponent("region-picker");
  _easycom_region_picker2();
}
const _easycom_region_picker = () => "../../components/region-picker/region-picker.js";
if (!Math) {
  _easycom_region_picker();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.selectedRegion.province
  }, $data.selectedRegion.province ? {
    b: common_vendor.t($data.selectedRegion.province),
    c: common_vendor.t($data.selectedRegion.city),
    d: common_vendor.t($data.selectedRegion.district)
  } : {}, {
    e: common_vendor.o((...args) => $options.showPicker && $options.showPicker(...args)),
    f: $data.selectedRegion.province
  }, $data.selectedRegion.province ? {
    g: common_vendor.t($data.selectedRegion.province),
    h: common_vendor.t($data.selectedRegion.city),
    i: common_vendor.t($data.selectedRegion.district)
  } : {}, {
    j: common_vendor.o($options.onRegionConfirm),
    k: common_vendor.o(($event) => $data.pickerVisible = false),
    l: common_vendor.p({
      visible: $data.pickerVisible,
      value: $data.selectedRegion
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-527ac849"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/test/region.js.map
