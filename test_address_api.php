<?php
/**
 * 测试地址API
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');

try {
    // 加载必要的文件
    require_once __DIR__ . '/api/core/Database.php';
    require_once __DIR__ . '/api/core/Response.php';
    require_once __DIR__ . '/api/middleware/AuthMiddleware.php';
    require_once __DIR__ . '/api/controllers/api/AddressController.php';
    
    echo "✅ 所有文件加载成功\n";
    
    // 创建控制器实例
    $controller = new AddressController();
    echo "✅ AddressController 实例创建成功\n";
    
    // 测试数据库连接
    $db = Database::getInstance();
    echo "✅ 数据库连接成功\n";
    
    echo "🎉 地址API测试通过！\n";
    
} catch (Exception $e) {
    // 输出错误信息
    echo "❌ 错误: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
    echo "堆栈: " . $e->getTraceAsString() . "\n";
}
?>
