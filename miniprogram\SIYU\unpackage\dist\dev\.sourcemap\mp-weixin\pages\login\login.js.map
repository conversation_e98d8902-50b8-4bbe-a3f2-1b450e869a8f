{"version": 3, "file": "login.js", "sources": ["pages/login/login.vue", "../../../Program Files/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvbG9naW4vbG9naW4udnVl"], "sourcesContent": ["<template>\n\t<view class=\"login-container\">\n\t\t<!-- 顶部导航 -->\n\t\t<view class=\"nav-bar\">\n\t\t\t<view class=\"nav-left\" @click=\"goBack\">\n\t\t\t\t<uni-icons type=\"left\" size=\"20\" color=\"#333\"></uni-icons>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 主要内容区域 -->\n\t\t<view class=\"main-content\">\n\t\t\t<!-- Logo区域 -->\n\t\t\t<view class=\"logo-section\">\n\t\t\t\t<view class=\"logo-container\">\n\t\t\t\t\t<view class=\"logo-bg\">\n\t\t\t\t\t\t<text class=\"logo-text\">思语</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"logo-decoration\">\n\t\t\t\t\t\t<view class=\"decoration-left\"></view>\n\t\t\t\t\t\t<view class=\"decoration-right\"></view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 协议区域 -->\n\t\t\t<view class=\"agreement-section\">\n\t\t\t\t<view class=\"agreement-checkbox\" @click=\"toggleAgreement\">\n\t\t\t\t\t<view class=\"checkbox-square\" :class=\"{ 'checked': agreedToTerms }\">\n\t\t\t\t\t\t<view v-if=\"agreedToTerms\" class=\"checkbox-mark\"></view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<text class=\"agreement-text\">同意并接受</text>\n\t\t\t\t<text class=\"agreement-link\" @click=\"showServiceAgreement\">《服务协议》</text>\n\t\t\t\t<text class=\"agreement-link\" @click=\"showPrivacyPolicy\">《隐私政策》</text>\n\t\t\t</view>\n\n\t\t\t<!-- 登录按钮 -->\n\t\t\t<button\n\t\t\t\tclass=\"wechat-login-btn\"\n\t\t\t\t:class=\"{ 'disabled': !agreedToTerms || isLoading }\"\n\t\t\t\t:disabled=\"!agreedToTerms || isLoading\"\n\t\t\t\t@click=\"handleWechatLogin\"\n\t\t\t\topen-type=\"getPhoneNumber\"\n\t\t\t\t@getphonenumber=\"onGetPhoneNumber\"\n\t\t\t>\n\t\t\t\t<text v-if=\"isLoading\">登录中...</text>\n\t\t\t\t<text v-else>免费开通会员</text>\n\t\t\t</button>\n\n\t\t\t<!-- 先去逛逛 -->\n\t\t\t<button class=\"browse-btn\" @click=\"browseMall\">\n\t\t\t\t先去逛逛\n\t\t\t</button>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nimport { authAPI } from '@/api/index.js'\nimport AuthUtils from '@/utils/auth.js'\n\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tisLoading: false,\n\t\t\tagreedToTerms: false\n\t\t}\n\t},\n\n\tmethods: {\n\t\t// 返回上一页\n\t\tgoBack() {\n\t\t\tuni.navigateBack({\n\t\t\t\tdelta: 1\n\t\t\t})\n\t\t},\n\n\t\t// 切换协议同意状态\n\t\ttoggleAgreement() {\n\t\t\tthis.agreedToTerms = !this.agreedToTerms\n\t\t},\n\n\t\t// 显示服务协议\n\t\tshowServiceAgreement() {\n\t\t\tuni.showModal({\n\t\t\t\ttitle: '服务协议',\n\t\t\t\tcontent: '这里是服务协议的内容...',\n\t\t\t\tshowCancel: false\n\t\t\t})\n\t\t},\n\n\t\t// 显示隐私政策\n\t\tshowPrivacyPolicy() {\n\t\t\tuni.showModal({\n\t\t\t\ttitle: '隐私政策',\n\t\t\t\tcontent: '这里是隐私政策的内容...',\n\t\t\t\tshowCancel: false\n\t\t\t})\n\t\t},\n\n\t\t// 处理微信登录\n\t\tasync handleWechatLogin() {\n\t\t\tif (!this.agreedToTerms) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请先同意协议',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t})\n\t\t\t\treturn\n\t\t\t}\n\n\t\t\t// 先获取用户信息授权\n\t\t\ttry {\n\t\t\t\tconst userProfile = await uni.getUserProfile({\n\t\t\t\t\tdesc: '用于完善用户资料'\n\t\t\t\t})\n\n\t\t\t\tconsole.log('获取用户信息成功:', userProfile)\n\n\t\t\t\t// 保存用户信息，等待手机号授权后一起提交\n\t\t\t\tthis.tempUserInfo = userProfile.userInfo\n\n\t\t\t} catch (error) {\n\t\t\t\tconsole.log('用户取消授权用户信息')\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '需要授权用户信息才能登录',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t})\n\t\t\t\treturn\n\t\t\t}\n\n\t\t\t// 继续触发 getPhoneNumber 事件\n\t\t},\n\n\t\t// 获取手机号回调\n\t\tasync onGetPhoneNumber(e) {\n\t\t\tconsole.log('获取手机号结果:', e.detail)\n\n\t\t\tif (e.detail.errMsg === 'getPhoneNumber:ok') {\n\t\t\t\tthis.isLoading = true\n\n\t\t\t\ttry {\n\t\t\t\t\t// 获取微信登录code\n\t\t\t\t\tconst loginRes = await uni.login()\n\n\t\t\t\t\tif (loginRes.code) {\n\t\t\t\t\t\t// 调用后端API进行登录\n\t\t\t\t\t\tconst response = await authAPI.login({\n\t\t\t\t\t\t\tcode: loginRes.code,\n\t\t\t\t\t\t\tencryptedData: e.detail.encryptedData,\n\t\t\t\t\t\t\tiv: e.detail.iv\n\t\t\t\t\t\t})\n\n\t\t\t\t\t\tif (response.code === 200) {\n\t\t\t\t\t\t\t// 保存登录信息\n\t\t\t\t\t\t\tuni.setStorageSync('token', response.data.token)\n\t\t\t\t\t\t\tuni.setStorageSync('userInfo', response.data.user)\n\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: response.data.is_new_user ? '注册成功' : '登录成功',\n\t\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t\t})\n\n\t\t\t\t\t\t\t// 跳转到首页\n\t\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\t\tuni.switchTab({\n\t\t\t\t\t\t\t\t\turl: '/pages/index/index'\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t}, 1500)\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tthrow new Error(response.message || '登录失败')\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthrow new Error('获取微信登录code失败')\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('登录失败:', error)\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: error.message || '登录失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t} finally {\n\t\t\t\t\tthis.isLoading = false\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '需要授权手机号才能登录',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t})\n\t\t\t}\n\t\t},\n\n\t\t// 先去逛逛\n\t\tbrowseMall() {\n\t\t\tuni.switchTab({\n\t\t\t\turl: '/pages/index/index'\n\t\t\t})\n\t\t}\n\t}\n}\n</script>\n\n<style scoped>\n.login-container {\n\tmin-height: 100vh;\n\tbackground: #f8f9fa;\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n/* 顶部导航 */\n.nav-bar {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tpadding: 44rpx 40rpx 20rpx;\n\tbackground: #fff;\n}\n\n.nav-left {\n\twidth: 60rpx;\n\theight: 60rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.nav-title {\n\tfont-size: 36rpx;\n\tfont-weight: 500;\n\tcolor: #333;\n}\n\n.nav-right {\n\twidth: 60rpx; /* 保持布局平衡 */\n}\n\n/* 主要内容 */\n.main-content {\n\tflex: 1;\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tpadding: 0 60rpx;\n}\n\n/* Logo区域 */\n.logo-section {\n\tflex: 1;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tmargin-top: 100rpx;\n}\n\n.logo-container {\n\tposition: relative;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.logo-bg {\n\twidth: 200rpx;\n\theight: 120rpx;\n\tbackground: #ff4757;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tborder-radius: 8rpx;\n\tz-index: 2;\n\tposition: relative;\n}\n\n.logo-text {\n\tfont-size: 48rpx;\n\tfont-weight: bold;\n\tcolor: white;\n}\n\n.logo-decoration {\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tbottom: 0;\n\tz-index: 1;\n}\n\n.decoration-left,\n.decoration-right {\n\tposition: absolute;\n\twidth: 120rpx;\n\theight: 60rpx;\n\tborder: 6rpx solid #ddd;\n\tborder-radius: 30rpx;\n}\n\n.decoration-left {\n\ttop: -20rpx;\n\tleft: -80rpx;\n\ttransform: rotate(-30deg);\n}\n\n.decoration-right {\n\tbottom: -20rpx;\n\tright: -80rpx;\n\ttransform: rotate(30deg);\n}\n\n/* 协议区域 */\n.agreement-section {\n\tdisplay: flex;\n\talign-items: center;\n\tmargin-bottom: 60rpx;\n\tflex-wrap: wrap;\n\tjustify-content: center;\n}\n\n.agreement-checkbox {\n\tmargin-right: 16rpx;\n}\n\n.checkbox-square {\n\twidth: 32rpx;\n\theight: 32rpx;\n\tborder: 2rpx solid #ff4757;\n\tborder-radius: 4rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tbackground: white;\n}\n\n.checkbox-square.checked {\n\tbackground: #ff4757;\n}\n\n.checkbox-mark {\n\twidth: 16rpx;\n\theight: 16rpx;\n\tbackground: white;\n\tborder-radius: 2rpx;\n}\n\n.agreement-text {\n\tfont-size: 28rpx;\n\tcolor: #666;\n\tmargin-right: 8rpx;\n}\n\n.agreement-link {\n\tfont-size: 28rpx;\n\tcolor: #007aff;\n\tmargin-right: 8rpx;\n}\n\n/* 登录按钮 */\n.wechat-login-btn {\n\twidth: 100%;\n\theight: 96rpx;\n\tbackground: #ff4757;\n\tborder-radius: 48rpx;\n\tborder: none;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tmargin-bottom: 40rpx;\n\tfont-size: 32rpx;\n\tfont-weight: 500;\n\tcolor: white;\n}\n\n.wechat-login-btn.disabled {\n\tbackground: #ccc;\n\tcolor: #999;\n}\n\n/* 先去逛逛按钮 */\n.browse-btn {\n\twidth: 100%;\n\theight: 96rpx;\n\tbackground: transparent;\n\tborder: 2rpx solid #ddd;\n\tborder-radius: 48rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tmargin-bottom: 60rpx;\n\tfont-size: 32rpx;\n\tcolor: #666;\n}\n</style>\n", "import MiniProgramPage from 'D:/app/miniprogram/SIYU/pages/login/login.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "authAPI"], "mappings": ";;;AA6DA,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,WAAW;AAAA,MACX,eAAe;AAAA,IAChB;AAAA,EACA;AAAA,EAED,SAAS;AAAA;AAAA,IAER,SAAS;AACRA,oBAAAA,MAAI,aAAa;AAAA,QAChB,OAAO;AAAA,OACP;AAAA,IACD;AAAA;AAAA,IAGD,kBAAkB;AACjB,WAAK,gBAAgB,CAAC,KAAK;AAAA,IAC3B;AAAA;AAAA,IAGD,uBAAuB;AACtBA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,OACZ;AAAA,IACD;AAAA;AAAA,IAGD,oBAAoB;AACnBA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,OACZ;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,oBAAoB;AACzB,UAAI,CAAC,KAAK,eAAe;AACxBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,SACN;AACD;AAAA,MACD;AAGA,UAAI;AACH,cAAM,cAAc,MAAMA,cAAG,MAAC,eAAe;AAAA,UAC5C,MAAM;AAAA,SACN;AAEDA,sBAAAA,MAAA,MAAA,OAAA,gCAAY,aAAa,WAAW;AAGpC,aAAK,eAAe,YAAY;AAAA,MAE/B,SAAO,OAAO;AACfA,sBAAAA,MAAY,MAAA,OAAA,gCAAA,YAAY;AACxBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,SACN;AACD;AAAA,MACD;AAAA,IAGA;AAAA;AAAA,IAGD,MAAM,iBAAiB,GAAG;AACzBA,oBAAY,MAAA,MAAA,OAAA,gCAAA,YAAY,EAAE,MAAM;AAEhC,UAAI,EAAE,OAAO,WAAW,qBAAqB;AAC5C,aAAK,YAAY;AAEjB,YAAI;AAEH,gBAAM,WAAW,MAAMA,cAAG,MAAC,MAAM;AAEjC,cAAI,SAAS,MAAM;AAElB,kBAAM,WAAW,MAAMC,UAAO,QAAC,MAAM;AAAA,cACpC,MAAM,SAAS;AAAA,cACf,eAAe,EAAE,OAAO;AAAA,cACxB,IAAI,EAAE,OAAO;AAAA,aACb;AAED,gBAAI,SAAS,SAAS,KAAK;AAE1BD,4BAAAA,MAAI,eAAe,SAAS,SAAS,KAAK,KAAK;AAC/CA,4BAAAA,MAAI,eAAe,YAAY,SAAS,KAAK,IAAI;AAEjDA,4BAAAA,MAAI,UAAU;AAAA,gBACb,OAAO,SAAS,KAAK,cAAc,SAAS;AAAA,gBAC5C,MAAM;AAAA,eACN;AAGD,yBAAW,MAAM;AAChBA,8BAAAA,MAAI,UAAU;AAAA,kBACb,KAAK;AAAA,iBACL;AAAA,cACD,GAAE,IAAI;AAAA,mBACD;AACN,oBAAM,IAAI,MAAM,SAAS,WAAW,MAAM;AAAA,YAC3C;AAAA,iBACM;AACN,kBAAM,IAAI,MAAM,cAAc;AAAA,UAC/B;AAAA,QACC,SAAO,OAAO;AACfA,wBAAAA,MAAc,MAAA,SAAA,gCAAA,SAAS,KAAK;AAC5BA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO,MAAM,WAAW;AAAA,YACxB,MAAM;AAAA,WACN;AAAA,QACF,UAAU;AACT,eAAK,YAAY;AAAA,QAClB;AAAA,aACM;AACNA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,SACN;AAAA,MACF;AAAA,IACA;AAAA;AAAA,IAGD,aAAa;AACZA,oBAAAA,MAAI,UAAU;AAAA,QACb,KAAK;AAAA,OACL;AAAA,IACF;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrMA,GAAG,WAAW,eAAe;"}