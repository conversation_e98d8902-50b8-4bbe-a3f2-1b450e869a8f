{"version": 3, "file": "earn.js", "sources": ["pages/points/earn.vue", "../../../Program Files/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcG9pbnRzL2Vhcm4udnVl"], "sourcesContent": ["<template>\n\t<view class=\"earn-container\">\n\t\t<!-- 页面头部 -->\n\t\t<view class=\"header\">\n\t\t\t<view class=\"header-bg\"></view>\n\t\t\t<view class=\"header-content\">\n\t\t\t\t<text class=\"title\">赚积分</text>\n\t\t\t\t<text class=\"subtitle\">多种方式轻松获得积分奖励</text>\n\t\t\t\t<view class=\"points-info\">\n\t\t\t\t\t<text class=\"current-points\">当前积分：{{ userPoints }}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 核心功能 - 订单转换 -->\n\t\t<view class=\"main-feature\">\n\t\t\t<view class=\"feature-card highlight\" @click=\"goToOrderSubmit\">\n\t\t\t\t<view class=\"feature-icon\">\n\t\t\t\t\t<text class=\"icon-emoji\">🛒</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"feature-content\">\n\t\t\t\t\t<text class=\"feature-title\">订单积分转换</text>\n\t\t\t\t\t<text class=\"feature-desc\">上传购物订单，自动验证获得积分</text>\n\t\t\t\t\t<text class=\"feature-rate\">1元 = 1积分</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"feature-arrow\">\n\t\t\t\t\t<text class=\"arrow\">›</text>\n\t\t\t\t\t<text class=\"hot-badge\">热门</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 其他赚积分方式 -->\n\t\t<view class=\"activities-section\">\n\t\t\t<view class=\"section-title\">\n\t\t\t\t<text class=\"title-text\">其他活动</text>\n\t\t\t\t<text class=\"title-desc\">每日参与，积分不断</text>\n\t\t\t</view>\n\n\t\t\t<view class=\"activities-grid\">\n\t\t\t\t<!-- 每日签到 -->\n\t\t\t\t<view v-if=\"activities.daily_sign?.enabled\" class=\"activity-card\" @click=\"goToSign\">\n\t\t\t\t\t<view class=\"activity-icon\">\n\t\t\t\t\t\t<text class=\"icon-emoji\">📅</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<text class=\"activity-title\">每日签到</text>\n\t\t\t\t\t<text class=\"activity-reward\">+5积分/天</text>\n\t\t\t\t\t<text class=\"activity-status\">{{ signStatus }}</text>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 幸运转盘 -->\n\t\t\t\t<view v-if=\"activities.lucky_wheel?.enabled\" class=\"activity-card\" @click=\"goToWheel\">\n\t\t\t\t\t<view class=\"activity-icon\">\n\t\t\t\t\t\t<text class=\"icon-emoji\">🎰</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<text class=\"activity-title\">幸运转盘</text>\n\t\t\t\t\t<text class=\"activity-reward\">最高500积分</text>\n\t\t\t\t\t<text class=\"activity-status\">每日3次</text>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 完善资料 -->\n\t\t\t\t<view class=\"activity-card\" @click=\"goToProfile\">\n\t\t\t\t\t<view class=\"activity-icon\">\n\t\t\t\t\t\t<text class=\"icon-emoji\">📝</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<text class=\"activity-title\">完善资料</text>\n\t\t\t\t\t<text class=\"activity-reward\">+50积分</text>\n\t\t\t\t\t<text class=\"activity-status\">{{ profileStatus }}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 积分使用提醒 -->\n\t\t<view class=\"usage-section\">\n\t\t\t<view class=\"usage-card\" @click=\"goToMall\">\n\t\t\t\t<view class=\"usage-icon\">🛍️</view>\n\t\t\t\t<view class=\"usage-content\">\n\t\t\t\t\t<text class=\"usage-title\">积分商城</text>\n\t\t\t\t\t<text class=\"usage-desc\">用积分兑换心仪商品</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"usage-arrow\">›</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport { pointsAPI, authAPI, systemAPI } from '@/api/index.js'\n\timport AuthUtils from '@/utils/auth.js'\n\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tuserPoints: 0,\n\t\t\t\tsignStatus: '未签到',\n\t\t\t\tprofileStatus: '未完成',\n\t\t\t\tactivities: {}\n\t\t\t}\n\t\t},\n\t\tonLoad() {\n\t\t\tthis.loadActivities()\n\t\t\tthis.loadUserPoints()\n\t\t\tthis.checkSignStatus()\n\t\t\tthis.checkProfileStatus()\n\t\t},\n\t\tonShow() {\n\t\t\tthis.loadUserPoints()\n\t\t},\n\t\tmethods: {\n\t\t\t// 加载活动配置\n\t\t\tasync loadActivities() {\n\t\t\t\ttry {\n\t\t\t\t\tconst result = await systemAPI.getActivities()\n\t\t\t\t\tthis.activities = result.data || {}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('获取活动配置失败:', error)\n\t\t\t\t\t// 使用默认配置\n\t\t\t\t\tthis.activities = {\n\t\t\t\t\t\tdaily_sign: { enabled: true },\n\t\t\t\t\t\tlucky_wheel: { enabled: false }\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 加载用户积分\n\t\t\tasync loadUserPoints() {\n\t\t\t\ttry {\n\t\t\t\t\tif (AuthUtils.isLoggedIn()) {\n\t\t\t\t\t\tconst result = await pointsAPI.getBalance()\n\t\t\t\t\t\tthis.userPoints = result.data.available_points || 0\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('获取积分失败:', error)\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 检查签到状态\n\t\t\tasync checkSignStatus() {\n\t\t\t\ttry {\n\t\t\t\t\t// 这里可以调用签到状态API\n\t\t\t\t\tthis.signStatus = '未签到'\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('检查签到状态失败:', error)\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 检查资料完善状态\n\t\t\tasync checkProfileStatus() {\n\t\t\t\ttry {\n\t\t\t\t\tif (AuthUtils.isLoggedIn()) {\n\t\t\t\t\t\tconst userInfo = AuthUtils.getCurrentUser()\n\t\t\t\t\t\tif (userInfo && userInfo.phone) {\n\t\t\t\t\t\t\tthis.profileStatus = '已完成'\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('检查资料状态失败:', error)\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 跳转到订单提交\n\t\t\tgoToOrderSubmit() {\n\t\t\t\tif (!AuthUtils.checkLoginAndRedirect()) return\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/order/submit'\n\t\t\t\t})\n\t\t\t},\n\n\t\t\t// 跳转到签到\n\t\t\tgoToSign() {\n\t\t\t\tif (!AuthUtils.checkLoginAndRedirect()) return\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/activities/sign'\n\t\t\t\t})\n\t\t\t},\n\n\t\t\t// 跳转到转盘\n\t\t\tgoToWheel() {\n\t\t\t\tif (!AuthUtils.checkLoginAndRedirect()) return\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/activities/wheel'\n\t\t\t\t})\n\t\t\t},\n\n\t\t\t// 跳转到资料页面\n\t\t\tgoToProfile() {\n\t\t\t\tif (!AuthUtils.checkLoginAndRedirect()) return\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/user/profile'\n\t\t\t\t})\n\t\t\t},\n\n\t\t\t// 跳转到商城\n\t\t\tgoToMall() {\n\t\t\t\tuni.switchTab({\n\t\t\t\t\turl: '/pages/mall/index'\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style scoped>\n\t.earn-container {\n\t\tbackground: #f8f8f8;\n\t\tmin-height: 100vh;\n\t\tpadding-bottom: env(safe-area-inset-bottom);\n\t}\n\n\t.header {\n\t\tposition: relative;\n\t\theight: 200px;\n\t\toverflow: hidden;\n\t}\n\n\t.header-bg {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n\t}\n\n\t.header-content {\n\t\tposition: relative;\n\t\tz-index: 2;\n\t\tpadding: 60px 20px 20px;\n\t\tcolor: white;\n\t}\n\n\t.title {\n\t\tfont-size: 28px;\n\t\tfont-weight: bold;\n\t\tdisplay: block;\n\t\tmargin-bottom: 8px;\n\t}\n\n\t.subtitle {\n\t\tfont-size: 16px;\n\t\topacity: 0.9;\n\t\tdisplay: block;\n\t\tmargin-bottom: 20px;\n\t}\n\n\t.points-info {\n\t\tbackground: rgba(255, 255, 255, 0.2);\n\t\tpadding: 12px 16px;\n\t\tborder-radius: 20px;\n\t\tdisplay: inline-block;\n\t}\n\n\t.current-points {\n\t\tfont-size: 16px;\n\t\tfont-weight: bold;\n\t}\n\n\t.main-feature {\n\t\tmargin: -30px 20px 20px;\n\t\tposition: relative;\n\t\tz-index: 3;\n\t}\n\n\t.feature-card {\n\t\tbackground: white;\n\t\tborder-radius: 16px;\n\t\tpadding: 20px;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tbox-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);\n\t}\n\n\t.feature-card.highlight {\n\t\tbackground: linear-gradient(135deg, #ff6b35 0%, #ff8c42 100%);\n\t\tcolor: white;\n\t}\n\n\t.feature-icon {\n\t\twidth: 60px;\n\t\theight: 60px;\n\t\tborder-radius: 30px;\n\t\tbackground: rgba(255, 255, 255, 0.2);\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tmargin-right: 16px;\n\t}\n\n\t.icon-emoji {\n\t\tfont-size: 28px;\n\t}\n\n\t.feature-content {\n\t\tflex: 1;\n\t}\n\n\t.feature-title {\n\t\tfont-size: 18px;\n\t\tfont-weight: bold;\n\t\tdisplay: block;\n\t\tmargin-bottom: 4px;\n\t}\n\n\t.feature-desc {\n\t\tfont-size: 14px;\n\t\topacity: 0.8;\n\t\tdisplay: block;\n\t\tmargin-bottom: 4px;\n\t}\n\n\t.feature-rate {\n\t\tfont-size: 16px;\n\t\tfont-weight: bold;\n\t}\n\n\t.feature-arrow {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t}\n\n\t.arrow {\n\t\tfont-size: 24px;\n\t\topacity: 0.8;\n\t}\n\n\t.hot-badge {\n\t\tbackground: #ff4757;\n\t\tcolor: white;\n\t\tfont-size: 10px;\n\t\tpadding: 2px 6px;\n\t\tborder-radius: 8px;\n\t\tmargin-top: 4px;\n\t}\n\n\t.activities-section {\n\t\tpadding: 20px;\n\t}\n\n\t.section-title {\n\t\tmargin-bottom: 20px;\n\t}\n\n\t.title-text {\n\t\tfont-size: 20px;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t\tdisplay: block;\n\t\tmargin-bottom: 4px;\n\t}\n\n\t.title-desc {\n\t\tfont-size: 14px;\n\t\tcolor: #666;\n\t}\n\n\t.activities-grid {\n\t\tdisplay: grid;\n\t\tgrid-template-columns: repeat(2, 1fr);\n\t\tgap: 15px;\n\t}\n\n\t.activity-card {\n\t\tbackground: white;\n\t\tborder-radius: 12px;\n\t\tpadding: 20px 16px;\n\t\ttext-align: center;\n\t\tbox-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n\t}\n\n\t.activity-icon {\n\t\twidth: 50px;\n\t\theight: 50px;\n\t\tborder-radius: 25px;\n\t\tbackground: #f0f0f0;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tmargin: 0 auto 12px;\n\t}\n\n\t.activity-title {\n\t\tfont-size: 16px;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t\tdisplay: block;\n\t\tmargin-bottom: 4px;\n\t}\n\n\t.activity-reward {\n\t\tfont-size: 14px;\n\t\tcolor: #ff6b35;\n\t\tfont-weight: bold;\n\t\tdisplay: block;\n\t\tmargin-bottom: 4px;\n\t}\n\n\t.activity-status {\n\t\tfont-size: 12px;\n\t\tcolor: #999;\n\t}\n\n\t.usage-section {\n\t\tpadding: 0 20px 40px;\n\t}\n\n\t.usage-card {\n\t\tbackground: white;\n\t\tborder-radius: 12px;\n\t\tpadding: 16px 20px;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tbox-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n\t}\n\n\t.usage-icon {\n\t\tfont-size: 24px;\n\t\tmargin-right: 12px;\n\t}\n\n\t.usage-content {\n\t\tflex: 1;\n\t}\n\n\t.usage-title {\n\t\tfont-size: 16px;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t\tdisplay: block;\n\t\tmargin-bottom: 2px;\n\t}\n\n\t.usage-desc {\n\t\tfont-size: 14px;\n\t\tcolor: #666;\n\t}\n\n\t.usage-arrow {\n\t\tfont-size: 18px;\n\t\tcolor: #999;\n\t}\n</style>\n", "import MiniProgramPage from 'D:/app/miniprogram/SIYU/pages/points/earn.vue'\nwx.createPage(MiniProgramPage)"], "names": ["systemAPI", "uni", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pointsAPI"], "mappings": ";;;;AA0FC,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,eAAe;AAAA,MACf,YAAY,CAAC;AAAA,IACd;AAAA,EACA;AAAA,EACD,SAAS;AACR,SAAK,eAAe;AACpB,SAAK,eAAe;AACpB,SAAK,gBAAgB;AACrB,SAAK,mBAAmB;AAAA,EACxB;AAAA,EACD,SAAS;AACR,SAAK,eAAe;AAAA,EACpB;AAAA,EACD,SAAS;AAAA;AAAA,IAER,MAAM,iBAAiB;AACtB,UAAI;AACH,cAAM,SAAS,MAAMA,UAAS,UAAC,cAAc;AAC7C,aAAK,aAAa,OAAO,QAAQ,CAAC;AAAA,MACjC,SAAO,OAAO;AACfC,sBAAAA,qDAAc,aAAa,KAAK;AAEhC,aAAK,aAAa;AAAA,UACjB,YAAY,EAAE,SAAS,KAAM;AAAA,UAC7B,aAAa,EAAE,SAAS,MAAM;AAAA,QAC/B;AAAA,MACD;AAAA,IACA;AAAA;AAAA,IAGD,MAAM,iBAAiB;AACtB,UAAI;AACH,YAAIC,WAAAA,UAAU,cAAc;AAC3B,gBAAM,SAAS,MAAMC,UAAS,UAAC,WAAW;AAC1C,eAAK,aAAa,OAAO,KAAK,oBAAoB;AAAA,QACnD;AAAA,MACC,SAAO,OAAO;AACfF,sBAAAA,qDAAc,WAAW,KAAK;AAAA,MAC/B;AAAA,IACA;AAAA;AAAA,IAGD,MAAM,kBAAkB;AACvB,UAAI;AAEH,aAAK,aAAa;AAAA,MACjB,SAAO,OAAO;AACfA,sBAAAA,qDAAc,aAAa,KAAK;AAAA,MACjC;AAAA,IACA;AAAA;AAAA,IAGD,MAAM,qBAAqB;AAC1B,UAAI;AACH,YAAIC,WAAAA,UAAU,cAAc;AAC3B,gBAAM,WAAWA,WAAS,UAAC,eAAe;AAC1C,cAAI,YAAY,SAAS,OAAO;AAC/B,iBAAK,gBAAgB;AAAA,UACtB;AAAA,QACD;AAAA,MACC,SAAO,OAAO;AACfD,sBAAAA,qDAAc,aAAa,KAAK;AAAA,MACjC;AAAA,IACA;AAAA;AAAA,IAGD,kBAAkB;AACjB,UAAI,CAACC,WAAAA,UAAU,sBAAqB;AAAI;AACxCD,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,OACL;AAAA,IACD;AAAA;AAAA,IAGD,WAAW;AACV,UAAI,CAACC,WAAAA,UAAU,sBAAqB;AAAI;AACxCD,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,OACL;AAAA,IACD;AAAA;AAAA,IAGD,YAAY;AACX,UAAI,CAACC,WAAAA,UAAU,sBAAqB;AAAI;AACxCD,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,OACL;AAAA,IACD;AAAA;AAAA,IAGD,cAAc;AACb,UAAI,CAACC,WAAAA,UAAU,sBAAqB;AAAI;AACxCD,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,OACL;AAAA,IACD;AAAA;AAAA,IAGD,WAAW;AACVA,oBAAAA,MAAI,UAAU;AAAA,QACb,KAAK;AAAA,OACL;AAAA,IACF;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;ACtMD,GAAG,WAAW,eAAe;"}