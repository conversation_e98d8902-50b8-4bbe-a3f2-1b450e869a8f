"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const common_vendor = require("./common/vendor.js");
const api_index = require("./api/index.js");
if (!Math) {
  "./pages/index/index.js";
  "./pages/login/login.js";
  "./pages/mall/index.js";
  "./pages/mall/detail.js";
  "./pages/points/index.js";
  "./pages/points/earn.js";
  "./pages/user/index.js";
  "./pages/user/orders.js";
  "./pages/user/favorites.js";
  "./pages/user/help.js";
  "./pages/order/submit.js";
  "./pages/order/confirm.js";
  "./pages/address/list.js";
  "./pages/address/edit.js";
  "./pages/test/region.js";
  "./pages/activities/sign.js";
}
const _sfc_main = {
  onLaunch: function() {
    common_vendor.index.__f__("log", "at App.vue:6", "App Launch");
    this.checkLoginStatus();
  },
  onShow: function() {
    common_vendor.index.__f__("log", "at App.vue:11", "App Show");
  },
  onHide: function() {
    common_vendor.index.__f__("log", "at App.vue:14", "App Hide");
  },
  methods: {
    // 检查登录状态
    async checkLoginStatus() {
      const token = common_vendor.index.getStorageSync("token");
      const userInfo = common_vendor.index.getStorageSync("userInfo");
      if (!token || !userInfo) {
        common_vendor.index.__f__("log", "at App.vue:25", "用户未登录，可以浏览但功能受限");
        return;
      }
      try {
        const result = await api_index.authAPI.verifyToken(token);
        if (result.data && result.data.valid) {
          common_vendor.index.__f__("log", "at App.vue:33", "用户已登录:", userInfo.nickname || userInfo.nickName);
          if (result.data.user) {
            common_vendor.index.setStorageSync("userInfo", result.data.user);
          }
        } else {
          common_vendor.index.__f__("log", "at App.vue:39", "token无效，清除本地数据");
          this.clearLoginData();
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at App.vue:43", "验证token失败:", error);
        common_vendor.index.__f__("log", "at App.vue:45", "网络异常，保持本地登录状态");
      }
    },
    // 清除登录数据
    clearLoginData() {
      common_vendor.index.removeStorageSync("token");
      common_vendor.index.removeStorageSync("userInfo");
    }
  }
};
function createApp() {
  const app = common_vendor.createSSRApp(_sfc_main);
  return {
    app
  };
}
createApp().app.mount("#app");
exports.createApp = createApp;
//# sourceMappingURL=../.sourcemap/mp-weixin/app.js.map
