{"version": 3, "file": "orders.js", "sources": ["pages/user/orders.vue", "../../../Program Files/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvdXNlci9vcmRlcnMudnVl"], "sourcesContent": ["<template>\n\t<view class=\"orders-container\">\n\t\t<!-- 状态筛选 -->\n\t\t<view class=\"status-tabs\">\n\t\t\t<view\n\t\t\t\tclass=\"tab-item\"\n\t\t\t\t:class=\"{ 'active': currentStatus === item.value }\"\n\t\t\t\tv-for=\"item in statusTabs\"\n\t\t\t\t:key=\"item.value\"\n\t\t\t\t@click=\"switchStatus(item.value)\"\n\t\t\t>\n\t\t\t\t<text class=\"tab-text\">{{ item.label }}</text>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 订单列表 -->\n\t\t<view class=\"orders-list\">\n\t\t\t<view class=\"order-item\" v-for=\"order in orderList\" :key=\"order.id\" @click=\"goToOrderDetail(order.id)\">\n\t\t\t\t<view class=\"order-header\">\n\t\t\t\t\t<text class=\"order-number\">订单号：{{ order.order_no }}</text>\n\t\t\t\t\t<text class=\"order-status\" :class=\"'status-' + order.status\">{{ getStatusText(order.status) }}</text>\n\t\t\t\t</view>\n\n\t\t\t\t<view class=\"order-products\">\n\t\t\t\t\t<view class=\"product-item\" v-for=\"product in order.products\" :key=\"product.id\">\n\t\t\t\t\t\t<view class=\"product-image\">\n\t\t\t\t\t\t\t<text class=\"product-emoji\">{{ product.emoji }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"product-info\">\n\t\t\t\t\t\t\t<text class=\"product-name\">{{ product.name }}</text>\n\t\t\t\t\t\t\t<text class=\"product-spec\">{{ product.spec || '默认规格' }}</text>\n\t\t\t\t\t\t\t<text class=\"product-quantity\">x{{ product.quantity }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"product-price\">\n\t\t\t\t\t\t\t<text class=\"points-price\">{{ product.points_price }}积分</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<view class=\"order-footer\">\n\t\t\t\t\t<view class=\"order-total\">\n\t\t\t\t\t\t<text class=\"total-text\">共{{ order.total_quantity }}件商品，合计：{{ order.total_points }}积分</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"order-actions\">\n\t\t\t\t\t\t<button v-if=\"order.status === 'pending'\" class=\"action-btn cancel-btn\" @click.stop=\"cancelOrder(order.id)\">取消订单</button>\n\t\t\t\t\t\t<button v-if=\"order.status === 'shipped'\" class=\"action-btn confirm-btn\" @click.stop=\"confirmOrder(order.id)\">确认收货</button>\n\t\t\t\t\t\t<button v-if=\"order.status === 'completed'\" class=\"action-btn review-btn\" @click.stop=\"reviewOrder(order.id)\">评价</button>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 空状态 -->\n\t\t\t<view v-if=\"orderList.length === 0\" class=\"empty-state\">\n\t\t\t\t<text class=\"empty-emoji\">📦</text>\n\t\t\t\t<text class=\"empty-text\">暂无订单</text>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nimport { orderAPI } from '@/api/index.js'\n\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tcurrentStatus: '',\n\t\t\tstatusTabs: [\n\t\t\t\t{ label: '全部', value: '' },\n\t\t\t\t{ label: '待付款', value: 'pending' },\n\t\t\t\t{ label: '处理中', value: 'processing' },\n\t\t\t\t{ label: '已发货', value: 'shipped' },\n\t\t\t\t{ label: '已完成', value: 'completed' }\n\t\t\t],\n\t\t\torderList: []\n\t\t}\n\t},\n\tonLoad(options) {\n\t\tif (options.status) {\n\t\t\tthis.currentStatus = options.status\n\t\t}\n\t\tthis.loadOrders()\n\t},\n\tmethods: {\n\t\t// 切换状态\n\t\tswitchStatus(status) {\n\t\t\tthis.currentStatus = status\n\t\t\tthis.loadOrders()\n\t\t},\n\n\t\t// 加载订单列表\n\t\tasync loadOrders() {\n\t\t\ttry {\n\t\t\t\t// 检查是否已登录\n\t\t\t\tconst token = uni.getStorageSync('token')\n\t\t\t\tif (!token) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请先登录',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl: '/pages/login/login'\n\t\t\t\t\t})\n\t\t\t\t\treturn\n\t\t\t\t}\n\n\t\t\t\tconst response = await orderAPI.getList({\n\t\t\t\t\tstatus: this.currentStatus,\n\t\t\t\t\tpage: 1,\n\t\t\t\t\tlimit: 20\n\t\t\t\t})\n\t\t\t\tthis.orderList = response.data.list || []\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('获取订单列表失败:', error)\n\t\t\t\t// 使用模拟数据作为降级方案\n\t\t\t\tthis.orderList = this.generateMockOrders()\n\t\t\t}\n\t\t},\n\n\t\t// 生成模拟订单数据\n\t\tgenerateMockOrders() {\n\t\t\tconst orders = []\n\t\t\tconst statuses = ['pending', 'processing', 'shipped', 'completed']\n\t\t\tconst emojis = ['☕', '🎧', '🔋', '📱', '🖱️', '⌨️']\n\t\t\tconst names = ['精美水杯', '蓝牙耳机', '充电宝', '手机支架', '无线鼠标', '键盘膜']\n\n\t\t\tfor (let i = 1; i <= 10; i++) {\n\t\t\t\tconst status = this.currentStatus || statuses[Math.floor(Math.random() * statuses.length)]\n\t\t\t\tconst productCount = Math.floor(Math.random() * 3) + 1\n\t\t\t\tconst products = []\n\t\t\t\tlet totalPoints = 0\n\t\t\t\tlet totalQuantity = 0\n\n\t\t\t\tfor (let j = 0; j < productCount; j++) {\n\t\t\t\t\tconst index = Math.floor(Math.random() * emojis.length)\n\t\t\t\t\tconst quantity = Math.floor(Math.random() * 2) + 1\n\t\t\t\t\tconst points = Math.floor(Math.random() * 500) + 100\n\n\t\t\t\t\tproducts.push({\n\t\t\t\t\t\tid: j + 1,\n\t\t\t\t\t\tname: names[index],\n\t\t\t\t\t\temoji: emojis[index],\n\t\t\t\t\t\tspec: '默认规格',\n\t\t\t\t\t\tquantity: quantity,\n\t\t\t\t\t\tpoints_price: points\n\t\t\t\t\t})\n\n\t\t\t\t\ttotalPoints += points * quantity\n\t\t\t\t\ttotalQuantity += quantity\n\t\t\t\t}\n\n\t\t\t\torders.push({\n\t\t\t\t\tid: i,\n\t\t\t\t\torder_no: 'ORD' + Date.now() + i,\n\t\t\t\t\tstatus: status,\n\t\t\t\t\tproducts: products,\n\t\t\t\t\ttotal_points: totalPoints,\n\t\t\t\t\ttotal_quantity: totalQuantity,\n\t\t\t\t\tcreated_at: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString()\n\t\t\t\t})\n\t\t\t}\n\n\t\t\treturn orders.filter(order => !this.currentStatus || order.status === this.currentStatus)\n\t\t},\n\n\t\t// 获取状态文本\n\t\tgetStatusText(status) {\n\t\t\tconst statusMap = {\n\t\t\t\t'pending': '待付款',\n\t\t\t\t'processing': '处理中',\n\t\t\t\t'shipped': '已发货',\n\t\t\t\t'completed': '已完成',\n\t\t\t\t'cancelled': '已取消'\n\t\t\t}\n\t\t\treturn statusMap[status] || '未知状态'\n\t\t},\n\n\t\t// 跳转到订单详情\n\t\tgoToOrderDetail(orderId) {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: `/pages/user/order-detail?id=${orderId}`\n\t\t\t})\n\t\t},\n\n\t\t// 取消订单\n\t\tasync cancelOrder(orderId) {\n\t\t\ttry {\n\t\t\t\tawait uni.showModal({\n\t\t\t\t\ttitle: '确认取消',\n\t\t\t\t\tcontent: '确定要取消这个订单吗？',\n\t\t\t\t\tconfirmText: '确认',\n\t\t\t\t\tcancelText: '取消'\n\t\t\t\t})\n\n\t\t\t\tawait orderAPI.cancel(orderId)\n\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '订单已取消',\n\t\t\t\t\ticon: 'success'\n\t\t\t\t})\n\n\t\t\t\t// 刷新列表\n\t\t\t\tthis.loadOrders()\n\t\t\t} catch (error) {\n\t\t\t\t// 用户取消或操作失败\n\t\t\t\tif (error.errMsg && error.errMsg.includes('cancel')) {\n\t\t\t\t\treturn // 用户取消操作\n\t\t\t\t}\n\t\t\t\tconsole.error('取消订单失败:', error)\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '取消失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t})\n\t\t\t}\n\t\t},\n\n\t\t// 确认收货\n\t\tasync confirmOrder(orderId) {\n\t\t\ttry {\n\t\t\t\tawait uni.showModal({\n\t\t\t\t\ttitle: '确认收货',\n\t\t\t\t\tcontent: '确认已收到商品吗？',\n\t\t\t\t\tconfirmText: '确认',\n\t\t\t\t\tcancelText: '取消'\n\t\t\t\t})\n\n\t\t\t\tawait orderAPI.confirm(orderId)\n\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '确认收货成功',\n\t\t\t\t\ticon: 'success'\n\t\t\t\t})\n\n\t\t\t\t// 刷新列表\n\t\t\t\tthis.loadOrders()\n\t\t\t} catch (error) {\n\t\t\t\t// 用户取消或操作失败\n\t\t\t\tif (error.errMsg && error.errMsg.includes('cancel')) {\n\t\t\t\t\treturn // 用户取消操作\n\t\t\t\t}\n\t\t\t\tconsole.error('确认收货失败:', error)\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '确认失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t})\n\t\t\t}\n\t\t},\n\n\t\t// 评价订单\n\t\treviewOrder(orderId) {\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '评价功能开发中',\n\t\t\t\ticon: 'none'\n\t\t\t})\n\t\t}\n\t}\n}\n</script>\n\n<style scoped>\n.orders-container {\n\tbackground: #f8f8f8;\n\tmin-height: 100vh;\n}\n\n.status-tabs {\n\tbackground: #ffffff;\n\tdisplay: flex;\n\tpadding: 0 15px;\n\tborder-bottom: 1px solid #f0f0f0;\n}\n\n.tab-item {\n\tflex: 1;\n\ttext-align: center;\n\tpadding: 15px 0;\n\tposition: relative;\n}\n\n.tab-item.active {\n\tcolor: #667eea;\n}\n\n.tab-item.active::after {\n\tcontent: '';\n\tposition: absolute;\n\tbottom: 0;\n\tleft: 50%;\n\ttransform: translateX(-50%);\n\twidth: 30px;\n\theight: 2px;\n\tbackground: #667eea;\n}\n\n.tab-text {\n\tfont-size: 14px;\n}\n\n.orders-list {\n\tpadding: 15px;\n}\n\n.order-item {\n\tbackground: #ffffff;\n\tborder-radius: 8px;\n\tmargin-bottom: 15px;\n\tpadding: 15px;\n}\n\n.order-header {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tmargin-bottom: 15px;\n\tpadding-bottom: 10px;\n\tborder-bottom: 1px solid #f5f5f5;\n}\n\n.order-number {\n\tfont-size: 14px;\n\tcolor: #666;\n}\n\n.order-status {\n\tfont-size: 14px;\n\tfont-weight: 500;\n}\n\n.status-pending { color: #f39c12; }\n.status-processing { color: #3498db; }\n.status-shipped { color: #9b59b6; }\n.status-completed { color: #27ae60; }\n.status-cancelled { color: #e74c3c; }\n\n.order-products {\n\tmargin-bottom: 15px;\n}\n\n.product-item {\n\tdisplay: flex;\n\talign-items: center;\n\tmargin-bottom: 10px;\n}\n\n.product-item:last-child {\n\tmargin-bottom: 0;\n}\n\n.product-image {\n\twidth: 60px;\n\theight: 60px;\n\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n\tborder-radius: 8px;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tmargin-right: 15px;\n}\n\n.product-emoji {\n\tfont-size: 24px;\n}\n\n.product-info {\n\tflex: 1;\n}\n\n.product-name {\n\tdisplay: block;\n\tfont-size: 14px;\n\tcolor: #333;\n\tmargin-bottom: 4px;\n}\n\n.product-spec {\n\tdisplay: block;\n\tfont-size: 12px;\n\tcolor: #999;\n\tmargin-bottom: 4px;\n}\n\n.product-quantity {\n\tdisplay: block;\n\tfont-size: 12px;\n\tcolor: #666;\n}\n\n.product-price {\n\ttext-align: right;\n}\n\n.points-price {\n\tfont-size: 14px;\n\tcolor: #667eea;\n\tfont-weight: 500;\n}\n\n.order-footer {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tpadding-top: 15px;\n\tborder-top: 1px solid #f5f5f5;\n}\n\n.total-text {\n\tfont-size: 14px;\n\tcolor: #333;\n}\n\n.order-actions {\n\tdisplay: flex;\n\tgap: 10px;\n}\n\n.action-btn {\n\tpadding: 6px 12px;\n\tborder-radius: 4px;\n\tfont-size: 12px;\n\tborder: 1px solid;\n}\n\n.cancel-btn {\n\tcolor: #e74c3c;\n\tborder-color: #e74c3c;\n\tbackground: transparent;\n}\n\n.confirm-btn {\n\tcolor: #667eea;\n\tborder-color: #667eea;\n\tbackground: transparent;\n}\n\n.review-btn {\n\tcolor: #27ae60;\n\tborder-color: #27ae60;\n\tbackground: transparent;\n}\n\n.empty-state {\n\ttext-align: center;\n\tpadding: 60px 20px;\n}\n\n.empty-emoji {\n\tdisplay: block;\n\tfont-size: 48px;\n\tmargin-bottom: 15px;\n}\n\n.empty-text {\n\tfont-size: 14px;\n\tcolor: #999;\n}\n</style>\n", "import MiniProgramPage from 'D:/app/miniprogram/SIYU/pages/user/orders.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "orderAPI"], "mappings": ";;;AA+DA,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,eAAe;AAAA,MACf,YAAY;AAAA,QACX,EAAE,OAAO,MAAM,OAAO,GAAI;AAAA,QAC1B,EAAE,OAAO,OAAO,OAAO,UAAW;AAAA,QAClC,EAAE,OAAO,OAAO,OAAO,aAAc;AAAA,QACrC,EAAE,OAAO,OAAO,OAAO,UAAW;AAAA,QAClC,EAAE,OAAO,OAAO,OAAO,YAAY;AAAA,MACnC;AAAA,MACD,WAAW,CAAC;AAAA,IACb;AAAA,EACA;AAAA,EACD,OAAO,SAAS;AACf,QAAI,QAAQ,QAAQ;AACnB,WAAK,gBAAgB,QAAQ;AAAA,IAC9B;AACA,SAAK,WAAW;AAAA,EAChB;AAAA,EACD,SAAS;AAAA;AAAA,IAER,aAAa,QAAQ;AACpB,WAAK,gBAAgB;AACrB,WAAK,WAAW;AAAA,IAChB;AAAA;AAAA,IAGD,MAAM,aAAa;AAClB,UAAI;AAEH,cAAM,QAAQA,cAAAA,MAAI,eAAe,OAAO;AACxC,YAAI,CAAC,OAAO;AACXA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,WACN;AACDA,wBAAAA,MAAI,WAAW;AAAA,YACd,KAAK;AAAA,WACL;AACD;AAAA,QACD;AAEA,cAAM,WAAW,MAAMC,UAAQ,SAAC,QAAQ;AAAA,UACvC,QAAQ,KAAK;AAAA,UACb,MAAM;AAAA,UACN,OAAO;AAAA,SACP;AACD,aAAK,YAAY,SAAS,KAAK,QAAQ,CAAC;AAAA,MACvC,SAAO,OAAO;AACfD,sBAAAA,MAAA,MAAA,SAAA,gCAAc,aAAa,KAAK;AAEhC,aAAK,YAAY,KAAK,mBAAmB;AAAA,MAC1C;AAAA,IACA;AAAA;AAAA,IAGD,qBAAqB;AACpB,YAAM,SAAS,CAAC;AAChB,YAAM,WAAW,CAAC,WAAW,cAAc,WAAW,WAAW;AACjE,YAAM,SAAS,CAAC,KAAK,MAAM,MAAM,MAAM,OAAO,IAAI;AAClD,YAAM,QAAQ,CAAC,QAAQ,QAAQ,OAAO,QAAQ,QAAQ,KAAK;AAE3D,eAAS,IAAI,GAAG,KAAK,IAAI,KAAK;AAC7B,cAAM,SAAS,KAAK,iBAAiB,SAAS,KAAK,MAAM,KAAK,OAAM,IAAK,SAAS,MAAM,CAAC;AACzF,cAAM,eAAe,KAAK,MAAM,KAAK,WAAW,CAAC,IAAI;AACrD,cAAM,WAAW,CAAC;AAClB,YAAI,cAAc;AAClB,YAAI,gBAAgB;AAEpB,iBAAS,IAAI,GAAG,IAAI,cAAc,KAAK;AACtC,gBAAM,QAAQ,KAAK,MAAM,KAAK,OAAO,IAAI,OAAO,MAAM;AACtD,gBAAM,WAAW,KAAK,MAAM,KAAK,WAAW,CAAC,IAAI;AACjD,gBAAM,SAAS,KAAK,MAAM,KAAK,OAAO,IAAI,GAAG,IAAI;AAEjD,mBAAS,KAAK;AAAA,YACb,IAAI,IAAI;AAAA,YACR,MAAM,MAAM,KAAK;AAAA,YACjB,OAAO,OAAO,KAAK;AAAA,YACnB,MAAM;AAAA,YACN;AAAA,YACA,cAAc;AAAA,WACd;AAED,yBAAe,SAAS;AACxB,2BAAiB;AAAA,QAClB;AAEA,eAAO,KAAK;AAAA,UACX,IAAI;AAAA,UACJ,UAAU,QAAQ,KAAK,IAAM,IAAE;AAAA,UAC/B;AAAA,UACA;AAAA,UACA,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,YAAY,IAAI,KAAK,KAAK,IAAG,IAAK,KAAK,WAAW,IAAI,KAAK,KAAK,KAAK,GAAI,EAAE,YAAY;AAAA,SACvF;AAAA,MACF;AAEA,aAAO,OAAO,OAAO,WAAS,CAAC,KAAK,iBAAiB,MAAM,WAAW,KAAK,aAAa;AAAA,IACxF;AAAA;AAAA,IAGD,cAAc,QAAQ;AACrB,YAAM,YAAY;AAAA,QACjB,WAAW;AAAA,QACX,cAAc;AAAA,QACd,WAAW;AAAA,QACX,aAAa;AAAA,QACb,aAAa;AAAA,MACd;AACA,aAAO,UAAU,MAAM,KAAK;AAAA,IAC5B;AAAA;AAAA,IAGD,gBAAgB,SAAS;AACxBA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK,+BAA+B,OAAO;AAAA,OAC3C;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,YAAY,SAAS;AAC1B,UAAI;AACH,cAAMA,cAAAA,MAAI,UAAU;AAAA,UACnB,OAAO;AAAA,UACP,SAAS;AAAA,UACT,aAAa;AAAA,UACb,YAAY;AAAA,SACZ;AAED,cAAMC,UAAQ,SAAC,OAAO,OAAO;AAE7BD,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,SACN;AAGD,aAAK,WAAW;AAAA,MACf,SAAO,OAAO;AAEf,YAAI,MAAM,UAAU,MAAM,OAAO,SAAS,QAAQ,GAAG;AACpD;AAAA,QACD;AACAA,sBAAAA,MAAA,MAAA,SAAA,gCAAc,WAAW,KAAK;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,SACN;AAAA,MACF;AAAA,IACA;AAAA;AAAA,IAGD,MAAM,aAAa,SAAS;AAC3B,UAAI;AACH,cAAMA,cAAAA,MAAI,UAAU;AAAA,UACnB,OAAO;AAAA,UACP,SAAS;AAAA,UACT,aAAa;AAAA,UACb,YAAY;AAAA,SACZ;AAED,cAAMC,UAAQ,SAAC,QAAQ,OAAO;AAE9BD,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,SACN;AAGD,aAAK,WAAW;AAAA,MACf,SAAO,OAAO;AAEf,YAAI,MAAM,UAAU,MAAM,OAAO,SAAS,QAAQ,GAAG;AACpD;AAAA,QACD;AACAA,sBAAAA,MAAA,MAAA,SAAA,gCAAc,WAAW,KAAK;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,SACN;AAAA,MACF;AAAA,IACA;AAAA;AAAA,IAGD,YAAY,SAAS;AACpBA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,OACN;AAAA,IACF;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/PA,GAAG,WAAW,eAAe;"}