<template>
	<view class="favorites-container">
		<!-- 空状态 -->
		<view v-if="favoritesList.length === 0" class="empty-state">
			<view class="empty-icon">💝</view>
			<text class="empty-text">暂无收藏商品</text>
			<text class="empty-desc">快去收藏你喜欢的商品吧</text>
			<button class="go-shopping-btn" @click="goShopping">去逛逛</button>
		</view>

		<!-- 收藏列表 -->
		<view v-else class="favorites-list">
			<view 
				v-for="item in favoritesList" 
				:key="item.id" 
				class="favorite-item"
				@click="goToProduct(item.product_id)"
			>
				<view class="product-image">
					<image 
						:src="item.product_image || '/static/images/placeholder.png'" 
						mode="aspectFill"
						class="image"
					/>
				</view>
				
				<view class="product-info">
					<text class="product-name">{{ item.product_name }}</text>
					<text class="product-desc">{{ item.product_desc || '精选好物，值得拥有' }}</text>
					<view class="product-price">
						<text class="price-text">{{ item.points_price }}积分</text>
					</view>
				</view>
				
				<view class="action-btn" @click.stop="removeFavorite(item.id)">
					<uni-icons type="heart-filled" size="20" color="#e74c3c"></uni-icons>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			favoritesList: []
		}
	},
	
	onLoad() {
		this.loadFavorites()
	},
	
	onShow() {
		this.loadFavorites()
	},
	
	methods: {
		// 加载收藏列表
		async loadFavorites() {
			try {
				// TODO: 调用收藏API
				// const response = await favoriteAPI.getFavorites()
				// this.favoritesList = response.data
				
				// 临时模拟数据
				this.favoritesList = []
			} catch (error) {
				console.error('加载收藏失败:', error)
				uni.showToast({
					title: '加载失败',
					icon: 'none'
				})
			}
		},
		
		// 移除收藏
		async removeFavorite(favoriteId) {
			try {
				uni.showModal({
					title: '提示',
					content: '确定要取消收藏吗？',
					success: async (res) => {
						if (res.confirm) {
							// TODO: 调用取消收藏API
							// await favoriteAPI.removeFavorite(favoriteId)
							
							// 临时处理
							this.favoritesList = this.favoritesList.filter(item => item.id !== favoriteId)
							
							uni.showToast({
								title: '已取消收藏',
								icon: 'success'
							})
						}
					}
				})
			} catch (error) {
				console.error('取消收藏失败:', error)
				uni.showToast({
					title: '操作失败',
					icon: 'none'
				})
			}
		},
		
		// 跳转到商品详情
		goToProduct(productId) {
			uni.navigateTo({
				url: `/pages/product/detail?id=${productId}`
			})
		},
		
		// 去购物
		goShopping() {
			uni.switchTab({
				url: '/pages/index/index'
			})
		}
	}
}
</script>

<style scoped>
.favorites-container {
	min-height: 100vh;
	background-color: #f5f5f5;
}

/* 空状态样式 */
.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 120rpx 40rpx;
	text-align: center;
}

.empty-icon {
	font-size: 120rpx;
	margin-bottom: 40rpx;
}

.empty-text {
	font-size: 32rpx;
	color: #333;
	margin-bottom: 20rpx;
	font-weight: 500;
}

.empty-desc {
	font-size: 28rpx;
	color: #999;
	margin-bottom: 60rpx;
}

.go-shopping-btn {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	border: none;
	border-radius: 50rpx;
	padding: 24rpx 60rpx;
	font-size: 28rpx;
}

/* 收藏列表样式 */
.favorites-list {
	padding: 20rpx;
}

.favorite-item {
	display: flex;
	align-items: center;
	background: white;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.product-image {
	width: 120rpx;
	height: 120rpx;
	border-radius: 16rpx;
	overflow: hidden;
	margin-right: 30rpx;
}

.image {
	width: 100%;
	height: 100%;
}

.product-info {
	flex: 1;
	display: flex;
	flex-direction: column;
}

.product-name {
	font-size: 30rpx;
	color: #333;
	font-weight: 500;
	margin-bottom: 10rpx;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.product-desc {
	font-size: 24rpx;
	color: #999;
	margin-bottom: 20rpx;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.product-price {
	display: flex;
	align-items: center;
}

.price-text {
	font-size: 28rpx;
	color: #e74c3c;
	font-weight: 600;
}

.action-btn {
	padding: 20rpx;
	margin-left: 20rpx;
}
</style>
