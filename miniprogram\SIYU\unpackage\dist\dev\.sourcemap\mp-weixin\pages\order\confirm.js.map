{"version": 3, "file": "confirm.js", "sources": ["pages/order/confirm.vue", "../../../Program Files/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvb3JkZXIvY29uZmlybS52dWU"], "sourcesContent": ["<template>\n\t<view class=\"confirm-container\">\n\t\t<!-- 收货地址 -->\n\t\t<view class=\"address-section\" @click=\"selectAddress\">\n\t\t\t<view class=\"section-header\">\n\t\t\t\t<text class=\"section-title\">收货地址</text>\n\t\t\t\t<text class=\"required\">*</text>\n\t\t\t</view>\n\t\t\t<view class=\"address-card\" v-if=\"selectedAddress\">\n\t\t\t\t<view class=\"address-info\">\n\t\t\t\t\t<view class=\"name-phone\">\n\t\t\t\t\t\t<text class=\"name\">{{ selectedAddress.name }}</text>\n\t\t\t\t\t\t<text class=\"phone\">{{ selectedAddress.phone }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<text class=\"address\">{{ selectedAddress.fullAddress }}</text>\n\t\t\t\t</view>\n\t\t\t\t<text class=\"change-btn\">更换</text>\n\t\t\t</view>\n\t\t\t<view class=\"address-empty\" v-else>\n\t\t\t\t<text class=\"empty-text\">请选择收货地址</text>\n\t\t\t\t<text class=\"arrow\">></text>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 商品信息 -->\n\t\t<view class=\"product-section\">\n\t\t\t<view class=\"section-title\">商品信息</view>\n\t\t\t<view class=\"product-card\">\n\t\t\t\t<image v-if=\"product.image\" :src=\"product.image\" class=\"product-image\" mode=\"aspectFill\" />\n\t\t\t\t<text v-else class=\"product-emoji\">{{ product.emoji || '🎁' }}</text>\n\t\t\t\t<view class=\"product-info\">\n\t\t\t\t\t<text class=\"product-name\">{{ product.name }}</text>\n\t\t\t\t\t<text class=\"product-spec\">默认规格</text>\n\t\t\t\t\t<view class=\"price-quantity\">\n\t\t\t\t\t\t<text class=\"points-price\">{{ product.points_price }}积分</text>\n\t\t\t\t\t\t<text class=\"quantity\">x{{ quantity }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 积分明细 -->\n\t\t<view class=\"points-section\">\n\t\t\t<view class=\"section-title\">积分明细</view>\n\t\t\t<view class=\"points-item\">\n\t\t\t\t<text class=\"points-label\">商品积分</text>\n\t\t\t\t<text class=\"points-value\">{{ totalPoints }}积分</text>\n\t\t\t</view>\n\t\t\t<view class=\"points-total\">\n\t\t\t\t<text class=\"total-label\">实付积分</text>\n\t\t\t\t<text class=\"total-value\">{{ totalPoints }}积分</text>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 底部提交 -->\n\t\t<view class=\"bottom-submit\">\n\t\t\t<view class=\"submit-info\">\n\t\t\t\t<text class=\"submit-label\">实付积分：</text>\n\t\t\t\t<text class=\"submit-points\">{{ totalPoints }}积分</text>\n\t\t\t</view>\n\t\t\t<button class=\"submit-btn\" :disabled=\"!canSubmit\" @click=\"submitOrder\">\n\t\t\t\t{{ submitting ? '提交中...' : '确认兑换' }}\n\t\t\t</button>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nimport { productAPI, orderAPI, addressAPI } from '@/api/index.js'\n\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tproductId: 0,\n\t\t\tquantity: 1,\n\t\t\ttype: 'exchange',\n\t\t\tproduct: {},\n\t\t\tselectedAddress: null,\n\t\t\tsubmitting: false\n\t\t}\n\t},\n\tcomputed: {\n\t\ttotalPoints() {\n\t\t\treturn this.product.points_price * this.quantity\n\t\t},\n\t\tcanSubmit() {\n\t\t\treturn this.selectedAddress && !this.submitting\n\t\t}\n\t},\n\tonLoad(options) {\n\t\tthis.productId = parseInt(options.productId)\n\t\tthis.quantity = parseInt(options.quantity) || 1\n\t\tthis.type = options.type || 'exchange'\n\n\t\tthis.loadProduct()\n\t\tthis.loadDefaultAddress()\n\t},\n\tmethods: {\n\t\t// 加载商品信息\n\t\tasync loadProduct() {\n\t\t\ttry {\n\t\t\t\tconst response = await productAPI.getDetail(this.productId)\n\t\t\t\tthis.product = response.data\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('获取商品信息失败:', error)\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '商品信息加载失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t})\n\t\t\t}\n\t\t},\n\n\t\t// 加载默认地址\n\t\tasync loadDefaultAddress() {\n\t\t\ttry {\n\t\t\t\tconst response = await addressAPI.getDefault()\n\t\t\t\tif (response.data) {\n\t\t\t\t\tthis.selectedAddress = {\n\t\t\t\t\t\tid: response.data.id,\n\t\t\t\t\t\tname: response.data.name,\n\t\t\t\t\t\tphone: response.data.phone,\n\t\t\t\t\t\tfullAddress: response.data.full_address\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.log('获取默认地址失败:', error)\n\t\t\t}\n\t\t},\n\n\t\t// 选择地址\n\t\tselectAddress() {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: '/pages/address/list?from=order'\n\t\t\t})\n\t\t},\n\n\t\t// 提交订单\n\t\tasync submitOrder() {\n\t\t\tif (!this.canSubmit) return\n\n\t\t\tthis.submitting = true\n\n\t\t\ttry {\n\t\t\t\tconst orderData = {\n\t\t\t\t\tproducts: [{\n\t\t\t\t\t\tproduct_id: this.productId,\n\t\t\t\t\t\tquantity: this.quantity\n\t\t\t\t\t}],\n\t\t\t\t\taddress_info: JSON.stringify(this.selectedAddress)\n\t\t\t\t}\n\n\t\t\t\tconst response = await orderAPI.create(orderData)\n\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '兑换成功',\n\t\t\t\t\ticon: 'success'\n\t\t\t\t})\n\n\t\t\t\t// 跳转到订单详情或订单列表\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tuni.redirectTo({\n\t\t\t\t\t\turl: '/pages/user/orders'\n\t\t\t\t\t})\n\t\t\t\t}, 1500)\n\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('提交订单失败:', error)\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: error.message || '兑换失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t})\n\t\t\t} finally {\n\t\t\t\tthis.submitting = false\n\t\t\t}\n\t\t}\n\t}\n}\n</script>\n\n<style scoped>\n.confirm-container {\n\tbackground: #f8f8f8;\n\tmin-height: 100vh;\n\tpadding-bottom: 80px;\n}\n\n.address-section, .product-section, .points-section {\n\tbackground: white;\n\tmargin-bottom: 10px;\n\tpadding: 15px;\n}\n\n.section-header {\n\tdisplay: flex;\n\talign-items: center;\n\tmargin-bottom: 15px;\n}\n\n.section-title {\n\tfont-size: 16px;\n\tfont-weight: bold;\n\tcolor: #333;\n}\n\n.required {\n\tcolor: #ff4757;\n\tmargin-left: 4px;\n}\n\n.address-card {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tpadding: 15px;\n\tbackground: #f8f9fa;\n\tborder-radius: 8px;\n}\n\n.address-info {\n\tflex: 1;\n}\n\n.name-phone {\n\tdisplay: flex;\n\talign-items: center;\n\tmargin-bottom: 8px;\n}\n\n.name {\n\tfont-size: 16px;\n\tfont-weight: bold;\n\tcolor: #333;\n\tmargin-right: 15px;\n}\n\n.phone {\n\tfont-size: 14px;\n\tcolor: #666;\n}\n\n.address {\n\tfont-size: 14px;\n\tcolor: #666;\n\tline-height: 1.4;\n}\n\n.change-btn {\n\tcolor: #667eea;\n\tfont-size: 14px;\n}\n\n.address-empty {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tpadding: 20px;\n\tbackground: #f8f9fa;\n\tborder-radius: 8px;\n\tborder: 2px dashed #ddd;\n}\n\n.empty-text {\n\tcolor: #999;\n}\n\n.arrow {\n\tcolor: #999;\n}\n\n.product-card {\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 15px 0;\n}\n\n.product-image {\n\twidth: 60px;\n\theight: 60px;\n\tborder-radius: 8px;\n\tmargin-right: 15px;\n}\n\n.product-emoji {\n\twidth: 60px;\n\theight: 60px;\n\tline-height: 60px;\n\ttext-align: center;\n\tfont-size: 30px;\n\tbackground: #f8f9fa;\n\tborder-radius: 8px;\n\tmargin-right: 15px;\n}\n\n.product-info {\n\tflex: 1;\n}\n\n.product-name {\n\tfont-size: 16px;\n\tcolor: #333;\n\tmargin-bottom: 5px;\n\tdisplay: block;\n}\n\n.product-spec {\n\tfont-size: 12px;\n\tcolor: #999;\n\tmargin-bottom: 8px;\n\tdisplay: block;\n}\n\n.price-quantity {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n}\n\n.points-price {\n\tcolor: #667eea;\n\tfont-weight: bold;\n}\n\n.quantity {\n\tcolor: #666;\n}\n\n.points-item {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tpadding: 10px 0;\n}\n\n.points-label {\n\tcolor: #666;\n}\n\n.points-value {\n\tcolor: #333;\n}\n\n.points-total {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tpadding: 15px 0;\n\tborder-top: 1px solid #f0f0f0;\n\tmargin-top: 10px;\n}\n\n.total-label {\n\tfont-size: 16px;\n\tfont-weight: bold;\n\tcolor: #333;\n}\n\n.total-value {\n\tfont-size: 18px;\n\tfont-weight: bold;\n\tcolor: #667eea;\n}\n\n.bottom-submit {\n\tposition: fixed;\n\tbottom: 0;\n\tleft: 0;\n\tright: 0;\n\tbackground: white;\n\tpadding: 15px;\n\tborder-top: 1px solid #f0f0f0;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n}\n\n.submit-info {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.submit-label {\n\tcolor: #666;\n\tmargin-right: 5px;\n}\n\n.submit-points {\n\tcolor: #667eea;\n\tfont-weight: bold;\n\tfont-size: 16px;\n}\n\n.submit-btn {\n\tbackground: #667eea;\n\tcolor: white;\n\tborder: none;\n\tborder-radius: 25px;\n\tpadding: 12px 30px;\n\tfont-size: 16px;\n\tfont-weight: bold;\n}\n\n.submit-btn:disabled {\n\tbackground: #ccc;\n}\n</style>\n", "import MiniProgramPage from 'D:/app/miniprogram/SIYU/pages/order/confirm.vue'\nwx.createPage(MiniProgramPage)"], "names": ["productAPI", "uni", "addressAPI", "orderAPI"], "mappings": ";;;AAsEA,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,WAAW;AAAA,MACX,UAAU;AAAA,MACV,MAAM;AAAA,MACN,SAAS,CAAE;AAAA,MACX,iBAAiB;AAAA,MACjB,YAAY;AAAA,IACb;AAAA,EACA;AAAA,EACD,UAAU;AAAA,IACT,cAAc;AACb,aAAO,KAAK,QAAQ,eAAe,KAAK;AAAA,IACxC;AAAA,IACD,YAAY;AACX,aAAO,KAAK,mBAAmB,CAAC,KAAK;AAAA,IACtC;AAAA,EACA;AAAA,EACD,OAAO,SAAS;AACf,SAAK,YAAY,SAAS,QAAQ,SAAS;AAC3C,SAAK,WAAW,SAAS,QAAQ,QAAQ,KAAK;AAC9C,SAAK,OAAO,QAAQ,QAAQ;AAE5B,SAAK,YAAY;AACjB,SAAK,mBAAmB;AAAA,EACxB;AAAA,EACD,SAAS;AAAA;AAAA,IAER,MAAM,cAAc;AACnB,UAAI;AACH,cAAM,WAAW,MAAMA,UAAAA,WAAW,UAAU,KAAK,SAAS;AAC1D,aAAK,UAAU,SAAS;AAAA,MACvB,SAAO,OAAO;AACfC,sBAAAA,MAAA,MAAA,SAAA,kCAAc,aAAa,KAAK;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,SACN;AAAA,MACF;AAAA,IACA;AAAA;AAAA,IAGD,MAAM,qBAAqB;AAC1B,UAAI;AACH,cAAM,WAAW,MAAMC,UAAU,WAAC,WAAW;AAC7C,YAAI,SAAS,MAAM;AAClB,eAAK,kBAAkB;AAAA,YACtB,IAAI,SAAS,KAAK;AAAA,YAClB,MAAM,SAAS,KAAK;AAAA,YACpB,OAAO,SAAS,KAAK;AAAA,YACrB,aAAa,SAAS,KAAK;AAAA,UAC5B;AAAA,QACD;AAAA,MACC,SAAO,OAAO;AACfD,sBAAAA,MAAA,MAAA,OAAA,kCAAY,aAAa,KAAK;AAAA,MAC/B;AAAA,IACA;AAAA;AAAA,IAGD,gBAAgB;AACfA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,OACL;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,cAAc;AACnB,UAAI,CAAC,KAAK;AAAW;AAErB,WAAK,aAAa;AAElB,UAAI;AACH,cAAM,YAAY;AAAA,UACjB,UAAU,CAAC;AAAA,YACV,YAAY,KAAK;AAAA,YACjB,UAAU,KAAK;AAAA,UAChB,CAAC;AAAA,UACD,cAAc,KAAK,UAAU,KAAK,eAAe;AAAA,QAClD;AAEA,cAAM,WAAW,MAAME,mBAAS,OAAO,SAAS;AAEhDF,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,SACN;AAGD,mBAAW,MAAM;AAChBA,wBAAAA,MAAI,WAAW;AAAA,YACd,KAAK;AAAA,WACL;AAAA,QACD,GAAE,IAAI;AAAA,MAEN,SAAO,OAAO;AACfA,sBAAAA,MAAA,MAAA,SAAA,kCAAc,WAAW,KAAK;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO,MAAM,WAAW;AAAA,UACxB,MAAM;AAAA,SACN;AAAA,MACF,UAAU;AACT,aAAK,aAAa;AAAA,MACnB;AAAA,IACD;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/KA,GAAG,WAAW,eAAe;"}