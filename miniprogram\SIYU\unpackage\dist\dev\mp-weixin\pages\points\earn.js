"use strict";
const common_vendor = require("../../common/vendor.js");
const api_index = require("../../api/index.js");
const utils_auth = require("../../utils/auth.js");
const _sfc_main = {
  data() {
    return {
      userPoints: 0,
      signStatus: "未签到",
      profileStatus: "未完成",
      activities: {}
    };
  },
  onLoad() {
    this.loadActivities();
    this.loadUserPoints();
    this.checkSignStatus();
    this.checkProfileStatus();
  },
  onShow() {
    this.loadUserPoints();
  },
  methods: {
    // 加载活动配置
    async loadActivities() {
      try {
        const result = await api_index.systemAPI.getActivities();
        this.activities = result.data || {};
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/points/earn.vue:116", "获取活动配置失败:", error);
        this.activities = {
          daily_sign: { enabled: true },
          lucky_wheel: { enabled: false }
        };
      }
    },
    // 加载用户积分
    async loadUserPoints() {
      try {
        if (utils_auth.AuthUtils.isLoggedIn()) {
          const result = await api_index.pointsAPI.getBalance();
          this.userPoints = result.data.available_points || 0;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/points/earn.vue:133", "获取积分失败:", error);
      }
    },
    // 检查签到状态
    async checkSignStatus() {
      try {
        this.signStatus = "未签到";
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/points/earn.vue:143", "检查签到状态失败:", error);
      }
    },
    // 检查资料完善状态
    async checkProfileStatus() {
      try {
        if (utils_auth.AuthUtils.isLoggedIn()) {
          const userInfo = utils_auth.AuthUtils.getCurrentUser();
          if (userInfo && userInfo.phone) {
            this.profileStatus = "已完成";
          }
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/points/earn.vue:157", "检查资料状态失败:", error);
      }
    },
    // 跳转到订单提交
    goToOrderSubmit() {
      if (!utils_auth.AuthUtils.checkLoginAndRedirect())
        return;
      common_vendor.index.navigateTo({
        url: "/pages/order/submit"
      });
    },
    // 跳转到签到
    goToSign() {
      if (!utils_auth.AuthUtils.checkLoginAndRedirect())
        return;
      common_vendor.index.navigateTo({
        url: "/pages/activities/sign"
      });
    },
    // 跳转到转盘
    goToWheel() {
      if (!utils_auth.AuthUtils.checkLoginAndRedirect())
        return;
      common_vendor.index.navigateTo({
        url: "/pages/activities/wheel"
      });
    },
    // 跳转到资料页面
    goToProfile() {
      if (!utils_auth.AuthUtils.checkLoginAndRedirect())
        return;
      common_vendor.index.navigateTo({
        url: "/pages/user/profile"
      });
    },
    // 跳转到商城
    goToMall() {
      common_vendor.index.switchTab({
        url: "/pages/mall/index"
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  var _a, _b, _c, _d;
  return common_vendor.e({
    a: common_vendor.t($data.userPoints),
    b: common_vendor.o((...args) => $options.goToOrderSubmit && $options.goToOrderSubmit(...args)),
    c: (_a = $data.activities.daily_sign) == null ? void 0 : _a.enabled
  }, ((_b = $data.activities.daily_sign) == null ? void 0 : _b.enabled) ? {
    d: common_vendor.t($data.signStatus),
    e: common_vendor.o((...args) => $options.goToSign && $options.goToSign(...args))
  } : {}, {
    f: (_c = $data.activities.lucky_wheel) == null ? void 0 : _c.enabled
  }, ((_d = $data.activities.lucky_wheel) == null ? void 0 : _d.enabled) ? {
    g: common_vendor.o((...args) => $options.goToWheel && $options.goToWheel(...args))
  } : {}, {
    h: common_vendor.t($data.profileStatus),
    i: common_vendor.o((...args) => $options.goToProfile && $options.goToProfile(...args)),
    j: common_vendor.o((...args) => $options.goToMall && $options.goToMall(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-281ae9f1"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/points/earn.js.map
