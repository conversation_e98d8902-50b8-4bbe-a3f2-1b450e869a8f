"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      favoritesList: []
    };
  },
  onLoad() {
    this.loadFavorites();
  },
  onShow() {
    this.loadFavorites();
  },
  methods: {
    // 加载收藏列表
    async loadFavorites() {
      try {
        this.favoritesList = [];
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/user/favorites.vue:70", "加载收藏失败:", error);
        common_vendor.index.showToast({
          title: "加载失败",
          icon: "none"
        });
      }
    },
    // 移除收藏
    async removeFavorite(favoriteId) {
      try {
        common_vendor.index.showModal({
          title: "提示",
          content: "确定要取消收藏吗？",
          success: async (res) => {
            if (res.confirm) {
              this.favoritesList = this.favoritesList.filter((item) => item.id !== favoriteId);
              common_vendor.index.showToast({
                title: "已取消收藏",
                icon: "success"
              });
            }
          }
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/user/favorites.vue:100", "取消收藏失败:", error);
        common_vendor.index.showToast({
          title: "操作失败",
          icon: "none"
        });
      }
    },
    // 跳转到商品详情
    goToProduct(productId) {
      common_vendor.index.navigateTo({
        url: `/pages/product/detail?id=${productId}`
      });
    },
    // 去购物
    goShopping() {
      common_vendor.index.switchTab({
        url: "/pages/index/index"
      });
    }
  }
};
if (!Array) {
  const _component_uni_icons = common_vendor.resolveComponent("uni-icons");
  _component_uni_icons();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.favoritesList.length === 0
  }, $data.favoritesList.length === 0 ? {
    b: common_vendor.o((...args) => $options.goShopping && $options.goShopping(...args))
  } : {
    c: common_vendor.f($data.favoritesList, (item, k0, i0) => {
      return {
        a: item.product_image || "/static/images/placeholder.png",
        b: common_vendor.t(item.product_name),
        c: common_vendor.t(item.product_desc || "精选好物，值得拥有"),
        d: common_vendor.t(item.points_price),
        e: "fef149ca-0-" + i0,
        f: common_vendor.o(($event) => $options.removeFavorite(item.id), item.id),
        g: item.id,
        h: common_vendor.o(($event) => $options.goToProduct(item.product_id), item.id)
      };
    }),
    d: common_vendor.p({
      type: "heart-filled",
      size: "20",
      color: "#e74c3c"
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-fef149ca"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/user/favorites.js.map
