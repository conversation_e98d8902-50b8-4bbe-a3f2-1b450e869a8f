{"version": 3, "file": "index.js", "sources": ["pages/points/index.vue", "../../../Program Files/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcG9pbnRzL2luZGV4LnZ1ZQ"], "sourcesContent": ["<template>\n\t<view class=\"points-container\">\n\t\t<!-- 积分余额卡片 -->\n\t\t<view class=\"balance-card\">\n\t\t\t<view class=\"card-bg\">\n\t\t\t\t<view class=\"balance-info\">\n\t\t\t\t\t<text class=\"balance-label\">我的积分</text>\n\t\t\t\t\t<text class=\"balance-amount\">{{ userPoints }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"card-actions\">\n\t\t\t\t\t<view class=\"action-btn\" @click=\"goToEarn\">\n\t\t\t\t\t\t<uni-icons type=\"plus\" size=\"20\" color=\"#fff\"></uni-icons>\n\t\t\t\t\t\t<text class=\"action-text\">赚积分</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"action-btn\" @click=\"goToExchange\">\n\t\t\t\t\t\t<uni-icons type=\"gift\" size=\"20\" color=\"#fff\"></uni-icons>\n\t\t\t\t\t\t<text class=\"action-text\">去兑换</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 功能入口 -->\n\t\t<view class=\"function-grid\">\n\t\t\t<view class=\"grid-item\" @click=\"goToSign\">\n\t\t\t\t<view class=\"grid-icon\">\n\t\t\t\t\t<svg-icon name=\"calendar\" :size=\"24\" color=\"#667eea\"></svg-icon>\n\t\t\t\t</view>\n\t\t\t\t<text class=\"grid-text\">每日签到</text>\n\t\t\t</view>\n\t\t\t<view class=\"grid-item\" @click=\"goToWheel\">\n\t\t\t\t<view class=\"grid-icon\">\n\t\t\t\t\t<svg-icon name=\"wheel\" :size=\"24\" color=\"#667eea\"></svg-icon>\n\t\t\t\t</view>\n\t\t\t\t<text class=\"grid-text\">幸运转盘</text>\n\t\t\t</view>\n\t\t\t<view class=\"grid-item\" @click=\"goToTasks\">\n\t\t\t\t<view class=\"grid-icon\">\n\t\t\t\t\t<svg-icon name=\"tasks\" :size=\"24\" color=\"#667eea\"></svg-icon>\n\t\t\t\t</view>\n\t\t\t\t<text class=\"grid-text\">任务中心</text>\n\t\t\t</view>\n\t\t\t<view class=\"grid-item\" @click=\"goToInvite\">\n\t\t\t\t<view class=\"grid-icon\">\n\t\t\t\t\t<svg-icon name=\"invite\" :size=\"24\" color=\"#667eea\"></svg-icon>\n\t\t\t\t</view>\n\t\t\t\t<text class=\"grid-text\">邀请好友</text>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 积分记录 -->\n\t\t<view class=\"records-section\">\n\t\t\t<view class=\"section-header\">\n\t\t\t\t<text class=\"section-title\">积分记录</text>\n\t\t\t\t<text class=\"more-btn\" @click=\"goToRecords\">查看全部</text>\n\t\t\t</view>\n\n\t\t\t<view class=\"records-list\">\n\t\t\t\t<view\n\t\t\t\t\tclass=\"record-item\"\n\t\t\t\t\tv-for=\"item in recentRecords\"\n\t\t\t\t\t:key=\"item.id\"\n\t\t\t\t>\n\t\t\t\t\t<view class=\"record-info\">\n\t\t\t\t\t\t<text class=\"record-title\">{{ item.title }}</text>\n\t\t\t\t\t\t<text class=\"record-time\">{{ item.created_at }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"record-points\" :class=\"{ positive: item.points > 0 }\">\n\t\t\t\t\t\t<text>{{ item.points > 0 ? '+' : '' }}{{ item.points }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nimport { pointsAPI } from '@/api/index.js'\nimport SvgIcon from '@/components/svg-icon/svg-icon.vue'\n\nexport default {\n\tcomponents: {\n\t\tSvgIcon\n\t},\n\tdata() {\n\t\treturn {\n\t\t\tuserPoints: 0,\n\t\t\trecentRecords: []\n\t\t}\n\t},\n\tonLoad() {\n\t\tthis.loadUserPoints();\n\t\tthis.loadRecentRecords();\n\t},\n\tonShow() {\n\t\t// 每次显示页面时刷新积分\n\t\tthis.loadUserPoints();\n\t},\n\tmethods: {\n\t\t// 加载用户积分\n\t\tasync loadUserPoints() {\n\t\t\ttry {\n\t\t\t\t// 检查是否已登录\n\t\t\t\tconst token = uni.getStorageSync('token')\n\t\t\t\tif (!token) {\n\t\t\t\t\tthis.userPoints = 0\n\t\t\t\t\treturn\n\t\t\t\t}\n\n\t\t\t\tconst response = await pointsAPI.getBalance()\n\t\t\t\tthis.userPoints = response.data.available_points || 0\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('获取积分余额失败:', error)\n\t\t\t\t// 使用模拟数据作为降级方案\n\t\t\t\tthis.userPoints = 1580\n\t\t\t}\n\t\t},\n\n\t\t// 加载最近积分记录\n\t\tasync loadRecentRecords() {\n\t\t\ttry {\n\t\t\t\t// 检查是否已登录\n\t\t\t\tconst token = uni.getStorageSync('token')\n\t\t\t\tif (!token) {\n\t\t\t\t\tthis.recentRecords = []\n\t\t\t\t\treturn\n\t\t\t\t}\n\n\t\t\t\tconst response = await pointsAPI.getRecords({\n\t\t\t\t\tpage: 1,\n\t\t\t\t\tlimit: 4\n\t\t\t\t})\n\n\t\t\t\t// 转换数据格式\n\t\t\t\tconst records = response.data.list || []\n\t\t\t\tthis.recentRecords = records.map(record => ({\n\t\t\t\t\tid: record.id,\n\t\t\t\t\ttitle: this.getRecordTitle(record),\n\t\t\t\t\tpoints: record.amount,\n\t\t\t\t\tcreated_at: record.created_at\n\t\t\t\t}))\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('获取积分记录失败:', error)\n\t\t\t\t// 使用模拟数据作为降级方案\n\t\t\t\tthis.recentRecords = [\n\t\t\t\t\t{\n\t\t\t\t\t\tid: 1,\n\t\t\t\t\t\ttitle: '每日签到',\n\t\t\t\t\t\tpoints: 10,\n\t\t\t\t\t\tcreated_at: '2024-01-15 09:30'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tid: 2,\n\t\t\t\t\t\ttitle: '商品兑换',\n\t\t\t\t\t\tpoints: -100,\n\t\t\t\t\t\tcreated_at: '2024-01-14 16:20'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tid: 3,\n\t\t\t\t\t\ttitle: '邀请好友',\n\t\t\t\t\t\tpoints: 50,\n\t\t\t\t\t\tcreated_at: '2024-01-14 14:15'\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tid: 4,\n\t\t\t\t\t\ttitle: '完成任务',\n\t\t\t\t\t\tpoints: 20,\n\t\t\t\t\t\tcreated_at: '2024-01-13 11:45'\n\t\t\t\t\t}\n\t\t\t\t]\n\t\t\t}\n\t\t},\n\n\t\t// 跳转到赚积分页面\n\t\tgoToEarn() {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: '/pages/points/earn'\n\t\t\t});\n\t\t},\n\n\t\t// 跳转到兑换页面\n\t\tgoToExchange() {\n\t\t\tuni.switchTab({\n\t\t\t\turl: '/pages/mall/index'\n\t\t\t});\n\t\t},\n\n\t\t// 跳转到签到页面\n\t\tgoToSign() {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: '/pages/activities/sign'\n\t\t\t});\n\t\t},\n\n\t\t// 跳转到转盘页面\n\t\tgoToWheel() {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: '/pages/activities/wheel'\n\t\t\t});\n\t\t},\n\n\t\t// 跳转到任务中心\n\t\tgoToTasks() {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: '/pages/points/tasks'\n\t\t\t});\n\t\t},\n\n\t\t// 跳转到邀请好友\n\t\tgoToInvite() {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: '/pages/points/invite'\n\t\t\t});\n\t\t},\n\n\t\t// 跳转到积分记录\n\t\tgoToRecords() {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: '/pages/points/records'\n\t\t\t});\n\t\t},\n\n\t\t// 根据记录类型生成标题\n\t\tgetRecordTitle(record) {\n\t\t\tconst sourceMap = {\n\t\t\t\t'admin_adjust': '管理员调整',\n\t\t\t\t'order_exchange': '订单兑换',\n\t\t\t\t'points_consume': '积分消费',\n\t\t\t\t'daily_sign': '每日签到',\n\t\t\t\t'lucky_wheel': '幸运转盘',\n\t\t\t\t'invite_reward': '邀请奖励',\n\t\t\t\t'task_reward': '任务奖励'\n\t\t\t}\n\n\t\t\t// 如果有描述，优先使用描述\n\t\t\tif (record.description) {\n\t\t\t\treturn record.description\n\t\t\t}\n\n\t\t\t// 否则根据来源生成标题\n\t\t\treturn sourceMap[record.source] || '积分变动'\n\t\t}\n\t}\n}\n</script>\n\n<style scoped>\n.points-container {\n\tbackground: #f8f8f8;\n\tmin-height: 100vh;\n}\n\n.balance-card {\n\tmargin: 15px;\n\tborder-radius: 12px;\n\toverflow: hidden;\n\tbox-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);\n}\n\n.card-bg {\n\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n\tpadding: 25px 20px;\n\tcolor: #ffffff;\n}\n\n.balance-info {\n\ttext-align: center;\n\tmargin-bottom: 25px;\n}\n\n.balance-label {\n\tdisplay: block;\n\tfont-size: 14px;\n\topacity: 0.9;\n\tmargin-bottom: 8px;\n}\n\n.balance-amount {\n\tdisplay: block;\n\tfont-size: 36px;\n\tfont-weight: bold;\n}\n\n.card-actions {\n\tdisplay: flex;\n\tjustify-content: space-around;\n}\n\n.action-btn {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tpadding: 10px 20px;\n\tbackground: rgba(255, 255, 255, 0.2);\n\tborder-radius: 8px;\n\tmin-width: 80px;\n}\n\n.action-text {\n\tfont-size: 12px;\n\tmargin-top: 5px;\n}\n\n.function-grid {\n\tdisplay: grid;\n\tgrid-template-columns: repeat(4, 1fr);\n\tgap: 15px;\n\tpadding: 20px 15px;\n\tbackground: #ffffff;\n\tmargin: 15px;\n\tborder-radius: 12px;\n}\n\n.grid-item {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tpadding: 15px 5px;\n}\n\n.grid-icon {\n\twidth: 50px;\n\theight: 50px;\n\tbackground: rgba(102, 126, 234, 0.1);\n\tborder-radius: 25px;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tmargin-bottom: 8px;\n}\n\n.grid-text {\n\tfont-size: 12px;\n\tcolor: #333;\n}\n\n.records-section {\n\tbackground: #ffffff;\n\tmargin: 15px;\n\tborder-radius: 12px;\n\tpadding: 20px;\n}\n\n.section-header {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tmargin-bottom: 15px;\n}\n\n.section-title {\n\tfont-size: 16px;\n\tfont-weight: 500;\n\tcolor: #333;\n}\n\n.more-btn {\n\tfont-size: 12px;\n\tcolor: #667eea;\n}\n\n.records-list {\n\t/* 记录列表样式 */\n}\n\n.record-item {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tpadding: 12px 0;\n\tborder-bottom: 1px solid #f5f5f5;\n}\n\n.record-item:last-child {\n\tborder-bottom: none;\n}\n\n.record-info {\n\tflex: 1;\n}\n\n.record-title {\n\tdisplay: block;\n\tfont-size: 14px;\n\tcolor: #333;\n\tmargin-bottom: 4px;\n}\n\n.record-time {\n\tdisplay: block;\n\tfont-size: 12px;\n\tcolor: #999;\n}\n\n.record-points {\n\tfont-size: 16px;\n\tfont-weight: 500;\n\tcolor: #f56c6c;\n}\n\n.record-points.positive {\n\tcolor: #67c23a;\n}\n</style>\n", "import MiniProgramPage from 'D:/app/miniprogram/SIYU/pages/points/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "pointsAPI"], "mappings": ";;;AA8EA,gBAAgB,MAAW;AAE3B,MAAK,YAAU;AAAA,EACd,YAAY;AAAA,IACX;AAAA,EACA;AAAA,EACD,OAAO;AACN,WAAO;AAAA,MACN,YAAY;AAAA,MACZ,eAAe,CAAC;AAAA,IACjB;AAAA,EACA;AAAA,EACD,SAAS;AACR,SAAK,eAAc;AACnB,SAAK,kBAAiB;AAAA,EACtB;AAAA,EACD,SAAS;AAER,SAAK,eAAc;AAAA,EACnB;AAAA,EACD,SAAS;AAAA;AAAA,IAER,MAAM,iBAAiB;AACtB,UAAI;AAEH,cAAM,QAAQA,cAAAA,MAAI,eAAe,OAAO;AACxC,YAAI,CAAC,OAAO;AACX,eAAK,aAAa;AAClB;AAAA,QACD;AAEA,cAAM,WAAW,MAAMC,UAAS,UAAC,WAAW;AAC5C,aAAK,aAAa,SAAS,KAAK,oBAAoB;AAAA,MACnD,SAAO,OAAO;AACfD,sBAAAA,MAAA,MAAA,SAAA,iCAAc,aAAa,KAAK;AAEhC,aAAK,aAAa;AAAA,MACnB;AAAA,IACA;AAAA;AAAA,IAGD,MAAM,oBAAoB;AACzB,UAAI;AAEH,cAAM,QAAQA,cAAAA,MAAI,eAAe,OAAO;AACxC,YAAI,CAAC,OAAO;AACX,eAAK,gBAAgB,CAAC;AACtB;AAAA,QACD;AAEA,cAAM,WAAW,MAAMC,UAAS,UAAC,WAAW;AAAA,UAC3C,MAAM;AAAA,UACN,OAAO;AAAA,SACP;AAGD,cAAM,UAAU,SAAS,KAAK,QAAQ,CAAC;AACvC,aAAK,gBAAgB,QAAQ,IAAI,aAAW;AAAA,UAC3C,IAAI,OAAO;AAAA,UACX,OAAO,KAAK,eAAe,MAAM;AAAA,UACjC,QAAQ,OAAO;AAAA,UACf,YAAY,OAAO;AAAA,QACpB,EAAE;AAAA,MACD,SAAO,OAAO;AACfD,sBAAAA,MAAA,MAAA,SAAA,iCAAc,aAAa,KAAK;AAEhC,aAAK,gBAAgB;AAAA,UACpB;AAAA,YACC,IAAI;AAAA,YACJ,OAAO;AAAA,YACP,QAAQ;AAAA,YACR,YAAY;AAAA,UACZ;AAAA,UACD;AAAA,YACC,IAAI;AAAA,YACJ,OAAO;AAAA,YACP,QAAQ;AAAA,YACR,YAAY;AAAA,UACZ;AAAA,UACD;AAAA,YACC,IAAI;AAAA,YACJ,OAAO;AAAA,YACP,QAAQ;AAAA,YACR,YAAY;AAAA,UACZ;AAAA,UACD;AAAA,YACC,IAAI;AAAA,YACJ,OAAO;AAAA,YACP,QAAQ;AAAA,YACR,YAAY;AAAA,UACb;AAAA,QACD;AAAA,MACD;AAAA,IACA;AAAA;AAAA,IAGD,WAAW;AACVA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACN,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,eAAe;AACdA,oBAAAA,MAAI,UAAU;AAAA,QACb,KAAK;AAAA,MACN,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,WAAW;AACVA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACN,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,YAAY;AACXA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACN,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,YAAY;AACXA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACN,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,aAAa;AACZA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACN,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,cAAc;AACbA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACN,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,eAAe,QAAQ;AACtB,YAAM,YAAY;AAAA,QACjB,gBAAgB;AAAA,QAChB,kBAAkB;AAAA,QAClB,kBAAkB;AAAA,QAClB,cAAc;AAAA,QACd,eAAe;AAAA,QACf,iBAAiB;AAAA,QACjB,eAAe;AAAA,MAChB;AAGA,UAAI,OAAO,aAAa;AACvB,eAAO,OAAO;AAAA,MACf;AAGA,aAAO,UAAU,OAAO,MAAM,KAAK;AAAA,IACpC;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClPA,GAAG,WAAW,eAAe;"}