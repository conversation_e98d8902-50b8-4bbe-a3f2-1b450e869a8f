"use strict";
const common_vendor = require("../../common/vendor.js");
const api_index = require("../../api/index.js");
const utils_request = require("../../utils/request.js");
const _sfc_main = {
  data() {
    return {
      searchKeyword: "",
      currentCategory: 0,
      categories: [
        { id: 0, name: "全部" }
      ],
      products: [],
      page: 1,
      pageSize: 10,
      hasMore: true,
      loadStatus: "more"
    };
  },
  onLoad() {
    this.loadCategories();
    this.loadProducts();
  },
  onReachBottom() {
    if (this.hasMore) {
      this.loadMore();
    }
  },
  onPullDownRefresh() {
    this.refreshProducts();
  },
  methods: {
    // 加载商品分类
    async loadCategories() {
      try {
        const response = await api_index.productAPI.getCategories();
        const categories = response.data || [];
        this.categories = [
          { id: 0, name: "全部" },
          ...categories
        ];
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/mall/index.vue:109", "获取商品分类失败:", error);
        this.categories = [
          { id: 0, name: "全部" },
          { id: 1, name: "数码产品" },
          { id: 2, name: "生活用品" },
          { id: 3, name: "办公用品" },
          { id: 4, name: "服装配饰" }
        ];
      }
    },
    // 搜索商品
    handleSearch() {
      this.page = 1;
      this.products = [];
      this.loadProducts();
    },
    // 选择分类
    selectCategory(categoryId) {
      this.currentCategory = categoryId;
      this.page = 1;
      this.products = [];
      this.loadProducts();
    },
    // 加载商品列表
    async loadProducts() {
      this.loadStatus = "loading";
      try {
        const params = {
          page: this.page,
          limit: this.pageSize
        };
        if (this.currentCategory) {
          params.category_id = this.currentCategory;
        }
        if (this.searchKeyword) {
          params.keyword = this.searchKeyword;
        }
        const response = await api_index.productAPI.getList(params);
        const productList = response.data.list || [];
        productList.forEach((product) => {
          if (product.image) {
            product.image = utils_request.getImageUrl(product.image);
          }
          if (product.images && Array.isArray(product.images)) {
            product.images = product.images.map((img) => utils_request.getImageUrl(img));
          }
        });
        if (this.page === 1) {
          this.products = productList;
        } else {
          this.products = [...this.products, ...productList];
        }
        this.hasMore = productList.length === this.pageSize;
        this.loadStatus = this.hasMore ? "more" : "noMore";
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/mall/index.vue:178", "获取商品列表失败:", error);
        const mockProducts = this.generateMockProducts();
        if (this.page === 1) {
          this.products = mockProducts;
        } else {
          this.products = [...this.products, ...mockProducts];
        }
        this.hasMore = mockProducts.length === this.pageSize;
        this.loadStatus = this.hasMore ? "more" : "noMore";
      }
      common_vendor.index.stopPullDownRefresh();
    },
    // 加载更多
    loadMore() {
      this.page++;
      this.loadProducts();
    },
    // 刷新商品
    refreshProducts() {
      this.page = 1;
      this.products = [];
      this.loadProducts();
    },
    // 生成模拟商品数据
    generateMockProducts() {
      const products = [];
      const emojis = ["☕", "🎧", "🔋", "📱", "🖱️", "⌨️", "💻", "📺", "🎮", "📷", "⌚", "🎒", "👕", "👟", "🧸", "📚"];
      const names = ["精美水杯", "蓝牙耳机", "充电宝", "手机支架", "无线鼠标", "键盘膜", "笔记本电脑", "智能电视", "游戏手柄", "数码相机", "智能手表", "时尚背包", "舒适T恤", "运动鞋", "毛绒玩具", "精装图书"];
      for (let i = 0; i < this.pageSize; i++) {
        const id = (this.page - 1) * this.pageSize + i + 1;
        const index = (id - 1) % emojis.length;
        products.push({
          id,
          name: names[index] || `商品${id}`,
          description: "这是一个很棒的商品，值得拥有",
          emoji: emojis[index],
          points_price: Math.floor(Math.random() * 1e3) + 100,
          market_price: (Math.random() * 100 + 10).toFixed(2),
          category_id: this.currentCategory || Math.floor(Math.random() * 4) + 1
        });
      }
      return products;
    },
    // 跳转到商品详情
    goToDetail(productId) {
      common_vendor.index.navigateTo({
        url: `/pages/mall/detail?id=${productId}`
      });
    }
  }
};
if (!Array) {
  const _component_uni_icons = common_vendor.resolveComponent("uni-icons");
  const _component_uni_load_more = common_vendor.resolveComponent("uni-load-more");
  (_component_uni_icons + _component_uni_load_more)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.p({
      type: "search",
      size: "18",
      color: "#999"
    }),
    b: common_vendor.o((...args) => $options.handleSearch && $options.handleSearch(...args)),
    c: $data.searchKeyword,
    d: common_vendor.o(($event) => $data.searchKeyword = $event.detail.value),
    e: common_vendor.f($data.categories, (item, k0, i0) => {
      return {
        a: common_vendor.t(item.name),
        b: $data.currentCategory === item.id ? 1 : "",
        c: item.id,
        d: common_vendor.o(($event) => $options.selectCategory(item.id), item.id)
      };
    }),
    f: common_vendor.f($data.products, (item, k0, i0) => {
      return common_vendor.e({
        a: item.image
      }, item.image ? {
        b: item.image
      } : {
        c: common_vendor.t(item.emoji)
      }, {
        d: common_vendor.t(item.name),
        e: common_vendor.t(item.description),
        f: common_vendor.t(item.points_price),
        g: item.market_price
      }, item.market_price ? {
        h: common_vendor.t(item.market_price)
      } : {}, {
        i: item.id,
        j: common_vendor.o(($event) => $options.goToDetail(item.id), item.id)
      });
    }),
    g: $data.hasMore
  }, $data.hasMore ? {
    h: common_vendor.p({
      status: $data.loadStatus
    })
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-43c06b56"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/mall/index.js.map
