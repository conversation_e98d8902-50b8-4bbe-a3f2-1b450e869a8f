"use strict";
const common_vendor = require("../../common/vendor.js");
const api_index = require("../../api/index.js");
const SvgIcon = () => "../../components/svg-icon/svg-icon.js";
const _sfc_main = {
  components: {
    SvgIcon
  },
  data() {
    return {
      userPoints: 0,
      recentRecords: []
    };
  },
  onLoad() {
    this.loadUserPoints();
    this.loadRecentRecords();
  },
  onShow() {
    this.loadUserPoints();
  },
  methods: {
    // 加载用户积分
    async loadUserPoints() {
      try {
        const token = common_vendor.index.getStorageSync("token");
        if (!token) {
          this.userPoints = 0;
          return;
        }
        const response = await api_index.pointsAPI.getBalance();
        this.userPoints = response.data.available_points || 0;
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/points/index.vue:113", "获取积分余额失败:", error);
        this.userPoints = 1580;
      }
    },
    // 加载最近积分记录
    async loadRecentRecords() {
      try {
        const token = common_vendor.index.getStorageSync("token");
        if (!token) {
          this.recentRecords = [];
          return;
        }
        const response = await api_index.pointsAPI.getRecords({
          page: 1,
          limit: 4
        });
        const records = response.data.list || [];
        this.recentRecords = records.map((record) => ({
          id: record.id,
          title: this.getRecordTitle(record),
          points: record.amount,
          created_at: record.created_at
        }));
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/points/index.vue:143", "获取积分记录失败:", error);
        this.recentRecords = [
          {
            id: 1,
            title: "每日签到",
            points: 10,
            created_at: "2024-01-15 09:30"
          },
          {
            id: 2,
            title: "商品兑换",
            points: -100,
            created_at: "2024-01-14 16:20"
          },
          {
            id: 3,
            title: "邀请好友",
            points: 50,
            created_at: "2024-01-14 14:15"
          },
          {
            id: 4,
            title: "完成任务",
            points: 20,
            created_at: "2024-01-13 11:45"
          }
        ];
      }
    },
    // 跳转到赚积分页面
    goToEarn() {
      common_vendor.index.navigateTo({
        url: "/pages/points/earn"
      });
    },
    // 跳转到兑换页面
    goToExchange() {
      common_vendor.index.switchTab({
        url: "/pages/mall/index"
      });
    },
    // 跳转到签到页面
    goToSign() {
      common_vendor.index.navigateTo({
        url: "/pages/activities/sign"
      });
    },
    // 跳转到转盘页面
    goToWheel() {
      common_vendor.index.navigateTo({
        url: "/pages/activities/wheel"
      });
    },
    // 跳转到任务中心
    goToTasks() {
      common_vendor.index.navigateTo({
        url: "/pages/points/tasks"
      });
    },
    // 跳转到邀请好友
    goToInvite() {
      common_vendor.index.navigateTo({
        url: "/pages/points/invite"
      });
    },
    // 跳转到积分记录
    goToRecords() {
      common_vendor.index.navigateTo({
        url: "/pages/points/records"
      });
    },
    // 根据记录类型生成标题
    getRecordTitle(record) {
      const sourceMap = {
        "admin_adjust": "管理员调整",
        "order_exchange": "订单兑换",
        "points_consume": "积分消费",
        "daily_sign": "每日签到",
        "lucky_wheel": "幸运转盘",
        "invite_reward": "邀请奖励",
        "task_reward": "任务奖励"
      };
      if (record.description) {
        return record.description;
      }
      return sourceMap[record.source] || "积分变动";
    }
  }
};
if (!Array) {
  const _component_uni_icons = common_vendor.resolveComponent("uni-icons");
  const _easycom_svg_icon2 = common_vendor.resolveComponent("svg-icon");
  (_component_uni_icons + _easycom_svg_icon2)();
}
const _easycom_svg_icon = () => "../../components/svg-icon/svg-icon.js";
if (!Math) {
  _easycom_svg_icon();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.t($data.userPoints),
    b: common_vendor.p({
      type: "plus",
      size: "20",
      color: "#fff"
    }),
    c: common_vendor.o((...args) => $options.goToEarn && $options.goToEarn(...args)),
    d: common_vendor.p({
      type: "gift",
      size: "20",
      color: "#fff"
    }),
    e: common_vendor.o((...args) => $options.goToExchange && $options.goToExchange(...args)),
    f: common_vendor.p({
      name: "calendar",
      size: 24,
      color: "#667eea"
    }),
    g: common_vendor.o((...args) => $options.goToSign && $options.goToSign(...args)),
    h: common_vendor.p({
      name: "wheel",
      size: 24,
      color: "#667eea"
    }),
    i: common_vendor.o((...args) => $options.goToWheel && $options.goToWheel(...args)),
    j: common_vendor.p({
      name: "tasks",
      size: 24,
      color: "#667eea"
    }),
    k: common_vendor.o((...args) => $options.goToTasks && $options.goToTasks(...args)),
    l: common_vendor.p({
      name: "invite",
      size: 24,
      color: "#667eea"
    }),
    m: common_vendor.o((...args) => $options.goToInvite && $options.goToInvite(...args)),
    n: common_vendor.o((...args) => $options.goToRecords && $options.goToRecords(...args)),
    o: common_vendor.f($data.recentRecords, (item, k0, i0) => {
      return {
        a: common_vendor.t(item.title),
        b: common_vendor.t(item.created_at),
        c: common_vendor.t(item.points > 0 ? "+" : ""),
        d: common_vendor.t(item.points),
        e: item.points > 0 ? 1 : "",
        f: item.id
      };
    })
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-dd6e20da"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/points/index.js.map
