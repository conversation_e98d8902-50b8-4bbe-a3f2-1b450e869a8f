"use strict";
const utils_request = require("../utils/request.js");
const authAPI = {
  // 微信登录
  login: (data) => utils_request.post("/auth/login", data),
  // 获取用户信息
  getUserInfo: () => utils_request.get("/auth/me"),
  // 验证token
  verifyToken: (token) => utils_request.post("/auth/verify", { token }),
  // 刷新token
  refreshToken: () => utils_request.post("/auth/refresh"),
  // 退出登录
  logout: (token) => utils_request.post("/auth/logout", { token })
};
const userAPI = {
  // 获取用户详情
  getProfile: () => utils_request.get("/user/profile"),
  // 更新用户信息
  updateProfile: (data) => utils_request.put("/user/profile", data),
  // 获取用户统计
  getStats: () => utils_request.get("/user/stats")
};
const pointsAPI = {
  // 获取积分余额
  getBalance: () => utils_request.get("/points/balance"),
  // 获取积分记录
  getRecords: (params) => utils_request.get("/points/records", params),
  // 积分兑换
  exchange: (data) => utils_request.post("/points/exchange", data),
  // 获取积分规则
  getRules: () => utils_request.get("/points/rules"),
  // 签到
  signIn: () => utils_request.post("/points/sign"),
  // 获取签到状态
  getSignStatus: () => utils_request.get("/points/sign/status")
};
const productAPI = {
  // 获取商品列表
  getList: (params) => utils_request.get("/products", params),
  // 获取商品详情
  getDetail: (id) => utils_request.get(`/products/${id}`),
  // 获取商品分类
  getCategories: () => utils_request.get("/categories"),
  // 搜索商品
  search: (keyword, params) => utils_request.get("/products/search", { keyword, ...params })
};
const orderAPI = {
  // 创建订单
  create: (data) => utils_request.post("/orders", data),
  // 获取订单列表
  getList: (params) => utils_request.get("/orders", params),
  // 获取订单详情
  getDetail: (id) => utils_request.get(`/orders/${id}`),
  // 取消订单
  cancel: (id) => utils_request.put(`/orders/${id}/cancel`),
  // 确认收货
  confirm: (id) => utils_request.put(`/orders/${id}/confirm`),
  // 提交公域订单（积分奖励）
  submitPublicOrder: (data) => utils_request.post("/orders/public-submit", data),
  // 获取公域订单列表
  getPublicOrders: (params) => utils_request.get("/orders/public", params)
};
const addressAPI = {
  // 获取地址列表
  getList: () => utils_request.get("/addresses"),
  // 获取地址详情
  getDetail: (id) => utils_request.get(`/addresses/${id}`),
  // 添加地址
  create: (data) => utils_request.post("/addresses", data),
  // 更新地址
  update: (id, data) => utils_request.put(`/addresses/${id}`, data),
  // 删除地址
  delete: (id) => utils_request.del(`/addresses/${id}`),
  // 设置默认地址
  setDefault: (id) => utils_request.put(`/addresses/${id}/default`),
  // 获取默认地址
  getDefault: () => utils_request.get("/addresses/default")
};
const systemAPI = {
  // 获取系统配置
  getConfig: () => utils_request.get("/system/config"),
  // 获取应用配置（包含LOGO等）
  getAppConfig: () => utils_request.get("/system/app-config"),
  // 获取轮播图
  getBanners: () => utils_request.get("/system/banners"),
  // 获取活动配置
  getActivities: () => utils_request.get("/system/activities"),
  // 意见反馈
  feedback: (data) => utils_request.post("/system/feedback", data),
  // 获取帮助文档
  getHelp: () => utils_request.get("/system/help"),
  // 检查更新
  checkUpdate: () => utils_request.get("/system/update")
};
exports.addressAPI = addressAPI;
exports.authAPI = authAPI;
exports.orderAPI = orderAPI;
exports.pointsAPI = pointsAPI;
exports.productAPI = productAPI;
exports.systemAPI = systemAPI;
exports.userAPI = userAPI;
//# sourceMappingURL=../../.sourcemap/mp-weixin/api/index.js.map
