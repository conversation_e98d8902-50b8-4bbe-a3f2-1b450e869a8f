"use strict";
const common_vendor = require("../../common/vendor.js");
const api_index = require("../../api/index.js");
const RegionPicker = () => "../../components/region-picker/region-picker.js";
const _sfc_main = {
  components: {
    RegionPicker
  },
  data() {
    return {
      addressId: 0,
      isEdit: false,
      submitting: false,
      showRegionPicker: false,
      form: {
        name: "",
        phone: "",
        province: "",
        city: "",
        district: "",
        detail: "",
        is_default: false
      }
    };
  },
  computed: {
    regionText() {
      if (this.form.province && this.form.city && this.form.district) {
        return `${this.form.province} ${this.form.city} ${this.form.district}`;
      }
      return "";
    },
    regionValue() {
      return {
        province: this.form.province,
        city: this.form.city,
        district: this.form.district
      };
    },
    canSubmit() {
      return this.form.name.trim() && this.form.phone.trim() && this.form.province && this.form.city && this.form.district && this.form.detail.trim();
    }
  },
  onLoad(options) {
    if (options.id) {
      this.addressId = parseInt(options.id);
      this.isEdit = true;
      this.loadAddress();
    }
  },
  methods: {
    // 加载地址信息（编辑模式）
    async loadAddress() {
      try {
        const response = await api_index.addressAPI.getDetail(this.addressId);
        const address = response.data;
        this.form = {
          name: address.name,
          phone: address.phone,
          province: address.province,
          city: address.city,
          district: address.district,
          detail: address.detail,
          is_default: address.is_default
        };
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/address/edit.vue:162", "获取地址信息失败:", error);
        common_vendor.index.showToast({
          title: "获取地址信息失败",
          icon: "none"
        });
        common_vendor.index.navigateBack();
      }
    },
    // 选择地区
    selectRegion() {
      this.showRegionPicker = true;
    },
    // 地区选择确认
    onRegionConfirm(region) {
      this.form.province = region.province;
      this.form.city = region.city;
      this.form.district = region.district;
    },
    // 默认地址开关变化
    onDefaultChange(e) {
      this.form.is_default = e.detail.value;
    },
    // 提交表单
    async submitForm() {
      if (!this.canSubmit || this.submitting)
        return;
      if (!/^1[3-9]\d{9}$/.test(this.form.phone)) {
        common_vendor.index.showToast({
          title: "请输入正确的手机号",
          icon: "none"
        });
        return;
      }
      this.submitting = true;
      try {
        const formData = { ...this.form };
        if (this.isEdit) {
          await api_index.addressAPI.update(this.addressId, formData);
          common_vendor.index.showToast({
            title: "修改成功",
            icon: "success"
          });
        } else {
          await api_index.addressAPI.create(formData);
          common_vendor.index.showToast({
            title: "添加成功",
            icon: "success"
          });
        }
        setTimeout(() => {
          common_vendor.index.navigateBack();
        }, 1500);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/address/edit.vue:226", "保存地址失败:", error);
        common_vendor.index.showToast({
          title: error.message || "保存失败",
          icon: "none"
        });
      } finally {
        this.submitting = false;
      }
    }
  }
};
if (!Array) {
  const _easycom_region_picker2 = common_vendor.resolveComponent("region-picker");
  _easycom_region_picker2();
}
const _easycom_region_picker = () => "../../components/region-picker/region-picker.js";
if (!Math) {
  _easycom_region_picker();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.form.name,
    b: common_vendor.o(($event) => $data.form.name = $event.detail.value),
    c: $data.form.phone,
    d: common_vendor.o(($event) => $data.form.phone = $event.detail.value),
    e: $options.regionText
  }, $options.regionText ? {
    f: common_vendor.t($options.regionText)
  } : {}, {
    g: common_vendor.o((...args) => $options.selectRegion && $options.selectRegion(...args)),
    h: $data.form.detail,
    i: common_vendor.o(($event) => $data.form.detail = $event.detail.value),
    j: $data.form.is_default,
    k: common_vendor.o((...args) => $options.onDefaultChange && $options.onDefaultChange(...args)),
    l: common_vendor.t($data.submitting ? "保存中..." : $data.isEdit ? "保存修改" : "保存地址"),
    m: !$options.canSubmit || $data.submitting,
    n: common_vendor.o((...args) => $options.submitForm && $options.submitForm(...args)),
    o: common_vendor.o((...args) => $options.submitForm && $options.submitForm(...args)),
    p: common_vendor.o($options.onRegionConfirm),
    q: common_vendor.o(($event) => $data.showRegionPicker = false),
    r: common_vendor.p({
      visible: $data.showRegionPicker,
      value: $options.regionValue
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-dcb1f0d8"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/address/edit.js.map
