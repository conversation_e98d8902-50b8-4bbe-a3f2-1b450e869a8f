"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      signing: false,
      todaySignStatus: false,
      continuousDays: 0,
      monthSignDays: 0,
      currentMonth: "",
      weekDays: ["日", "一", "二", "三", "四", "五", "六"],
      calendarDays: [],
      signedDays: [],
      signRules: [
        {
          id: 1,
          days: 1,
          title: "每日签到",
          description: "每天签到可获得基础积分",
          points: 10
        },
        {
          id: 2,
          days: 7,
          title: "连续7天",
          description: "连续签到7天额外奖励",
          points: 50
        },
        {
          id: 3,
          days: 15,
          title: "连续15天",
          description: "连续签到15天额外奖励",
          points: 100
        },
        {
          id: 4,
          days: 30,
          title: "连续30天",
          description: "连续签到30天额外奖励",
          points: 300
        }
      ],
      signRecords: []
    };
  },
  onLoad() {
    this.initCalendar();
    this.loadSignStatus();
    this.loadSignRecords();
  },
  methods: {
    // 初始化日历
    initCalendar() {
      const now = /* @__PURE__ */ new Date();
      const year = now.getFullYear();
      const month = now.getMonth();
      this.currentMonth = `${year}年${month + 1}月`;
      const lastDay = new Date(year, month + 1, 0);
      const days = [];
      for (let i = 1; i <= lastDay.getDate(); i++) {
        days.push({
          date: `${year}-${String(month + 1).padStart(2, "0")}-${String(i).padStart(2, "0")}`,
          day: i
        });
      }
      this.calendarDays = days;
    },
    // 加载签到状态
    async loadSignStatus() {
      try {
        const token = common_vendor.index.getStorageSync("token");
        if (!token) {
          return;
        }
        this.todaySignStatus = false;
        this.continuousDays = 5;
        this.monthSignDays = 12;
        this.signedDays = [
          "2024-01-10",
          "2024-01-11",
          "2024-01-12",
          "2024-01-13",
          "2024-01-14"
        ];
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/activities/sign.vue:205", "获取签到状态失败:", error);
      }
    },
    // 加载签到记录
    async loadSignRecords() {
      try {
        const token = common_vendor.index.getStorageSync("token");
        if (!token) {
          return;
        }
        this.signRecords = [
          {
            id: 1,
            created_at: "2024-01-14 09:30:00",
            continuous_days: 5,
            points: 10
          },
          {
            id: 2,
            created_at: "2024-01-13 08:45:00",
            continuous_days: 4,
            points: 10
          }
        ];
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/activities/sign.vue:239", "获取签到记录失败:", error);
      }
    },
    // 执行签到
    async handleSign() {
      if (this.todaySignStatus || this.signing)
        return;
      const token = common_vendor.index.getStorageSync("token");
      if (!token) {
        common_vendor.index.showToast({
          title: "请先登录",
          icon: "none"
        });
        common_vendor.index.navigateTo({
          url: "/pages/login/login"
        });
        return;
      }
      this.signing = true;
      try {
        const mockResponse = {
          data: {
            continuous_days: this.continuousDays + 1,
            points: 10
          }
        };
        this.todaySignStatus = true;
        this.continuousDays = mockResponse.data.continuous_days;
        this.monthSignDays += 1;
        const today = this.formatDate(/* @__PURE__ */ new Date(), "YYYY-MM-DD");
        this.signedDays.push(today);
        common_vendor.index.showToast({
          title: `签到成功，获得${mockResponse.data.points}积分`,
          icon: "success",
          duration: 2e3
        });
        this.loadSignRecords();
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/activities/sign.vue:298", "签到失败:", error);
        common_vendor.index.showToast({
          title: error.message || "签到失败",
          icon: "none"
        });
      } finally {
        this.signing = false;
      }
    },
    // 判断是否已签到
    isSignedDay(day) {
      return this.signedDays.includes(day.date);
    },
    // 判断是否今天
    isToday(day) {
      const today = this.formatDate(/* @__PURE__ */ new Date(), "YYYY-MM-DD");
      return day.date === today;
    },
    // 判断是否未来日期
    isFutureDay(day) {
      const today = /* @__PURE__ */ new Date();
      const dayDate = new Date(day.date);
      return dayDate > today;
    },
    // 格式化日期
    formatDate(date, format = "MM-DD") {
      const d = new Date(date);
      const year = d.getFullYear();
      const month = String(d.getMonth() + 1).padStart(2, "0");
      const day = String(d.getDate()).padStart(2, "0");
      return format.replace("YYYY", year).replace("MM", month).replace("DD", day);
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.t($data.currentMonth),
    b: common_vendor.f($data.weekDays, (day, k0, i0) => {
      return {
        a: common_vendor.t(day),
        b: day
      };
    }),
    c: common_vendor.f($data.calendarDays, (day, k0, i0) => {
      return common_vendor.e({
        a: common_vendor.t(day.day),
        b: $options.isSignedDay(day)
      }, $options.isSignedDay(day) ? {
        c: common_vendor.t(day.points || 10)
      } : {}, {
        d: $options.isSignedDay(day) ? 1 : "",
        e: $options.isToday(day) ? 1 : "",
        f: $options.isFutureDay(day) ? 1 : "",
        g: day.date
      });
    }),
    d: common_vendor.t($data.todaySignStatus ? "今日已签到" : "立即签到"),
    e: $data.todaySignStatus ? 1 : "",
    f: $data.todaySignStatus,
    g: common_vendor.o((...args) => $options.handleSign && $options.handleSign(...args)),
    h: $data.signing,
    i: common_vendor.t($data.continuousDays),
    j: common_vendor.t($data.monthSignDays),
    k: common_vendor.f($data.signRules, (rule, k0, i0) => {
      return {
        a: common_vendor.t(rule.days),
        b: common_vendor.t(rule.title),
        c: common_vendor.t(rule.description),
        d: common_vendor.t(rule.points),
        e: rule.id
      };
    }),
    l: common_vendor.f($data.signRecords, (record, k0, i0) => {
      return {
        a: common_vendor.t($options.formatDate(record.created_at)),
        b: common_vendor.t(record.continuous_days),
        c: common_vendor.t(record.points),
        d: record.id
      };
    })
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-c8158183"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/activities/sign.js.map
