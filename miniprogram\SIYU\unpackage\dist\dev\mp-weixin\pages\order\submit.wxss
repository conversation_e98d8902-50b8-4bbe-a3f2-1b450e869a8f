
.submit-container.data-v-c3fa8b8a {
		background: white;
		min-height: 100vh;
		padding: 20px 0 100px 0;
		overflow-x: hidden; /* 防止水平滚动 */
		box-sizing: border-box;
}
.header.data-v-c3fa8b8a {
		text-align: center;
		margin-bottom: 30px;
		padding: 0 15px;
}
.title.data-v-c3fa8b8a {
		display: block;
		font-size: 20px;
		font-weight: bold;
		color: #333;
		margin-bottom: 8px;
}
.subtitle.data-v-c3fa8b8a {
		font-size: 14px;
		color: #666;
}
.form-section.data-v-c3fa8b8a {
		margin-bottom: 30px;
		padding: 0 15px;
}
.form-item.data-v-c3fa8b8a {
		padding: 15px 0;
		border-bottom: 1px solid #f0f0f0;
		display: flex;
		align-items: center;
}
.form-item.data-v-c3fa8b8a:last-child {
		border-bottom: none;
}
.label.data-v-c3fa8b8a {
		font-size: 16px;
		color: #333;
		width: 100px;
		flex-shrink: 0;
}
.picker.data-v-c3fa8b8a {
		flex: 1;
		display: flex;
		justify-content: space-between;
		align-items: center;
		color: #333;
}
.arrow.data-v-c3fa8b8a {
		color: #999;
		font-size: 18px;
}
.input.data-v-c3fa8b8a {
		flex: 1;
		font-size: 16px;
		color: #333;
}
.points-preview.data-v-c3fa8b8a {
		flex: 1;
		display: flex;
		align-items: center;
}
.points.data-v-c3fa8b8a {
		font-size: 18px;
		font-weight: bold;
		color: #ff6b35;
}
.points-unit.data-v-c3fa8b8a {
		color: #666;
		margin-left: 4px;
}
.upload-area.data-v-c3fa8b8a {
		flex: 1;
		margin-left: 10px;
}
.upload-placeholder.data-v-c3fa8b8a {
		border: 2px dashed #ddd;
		border-radius: 8px;
		padding: 30px;
		text-align: center;
		background: #fafafa;
}
.upload-icon.data-v-c3fa8b8a {
		display: block;
		font-size: 32px;
		margin-bottom: 10px;
}
.upload-text.data-v-c3fa8b8a {
		display: block;
		font-size: 16px;
		color: #333;
		margin-bottom: 5px;
}
.upload-tip.data-v-c3fa8b8a {
		font-size: 12px;
		color: #999;
}
.image-list.data-v-c3fa8b8a {
		display: flex;
		flex-wrap: wrap;
		gap: 10px;
}
.image-item.data-v-c3fa8b8a {
		position: relative;
		width: 80px;
		height: 80px;
}
.preview-image.data-v-c3fa8b8a {
		width: 100%;
		height: 100%;
		border-radius: 6px;
}
.delete-btn.data-v-c3fa8b8a {
		position: absolute;
		top: -8px;
		right: -8px;
		width: 20px;
		height: 20px;
		background: #ff4757;
		color: white;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 14px;
		line-height: 1;
}
.add-image.data-v-c3fa8b8a {
		width: 80px;
		height: 80px;
		border: 2px dashed #ddd;
		border-radius: 6px;
		display: flex;
		align-items: center;
		justify-content: center;
		background: #fafafa;
}
.add-icon.data-v-c3fa8b8a {
		font-size: 24px;
		color: #999;
}
.rules-section.data-v-c3fa8b8a {
		margin-bottom: 30px;
}
.rules-title.data-v-c3fa8b8a {
		display: flex;
		align-items: center;
		margin-bottom: 15px;
		padding: 0 15px;
}
.rules-icon.data-v-c3fa8b8a {
		font-size: 18px;
		margin-right: 8px;
}
.rules-text.data-v-c3fa8b8a {
		font-size: 16px;
		font-weight: bold;
		color: #333;
}
.rules-content.data-v-c3fa8b8a {
		line-height: 1.6;
		width: 100%;
		box-sizing: border-box;
}
.submit-section.data-v-c3fa8b8a {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		background: white;
		padding: 15px 15px;
		border-top: 1px solid #f0f0f0;
}
.submit-btn.data-v-c3fa8b8a {
		width: 100%;
		height: 50px;
		background: #ff6b35;
		color: white;
		border: none;
		border-radius: 25px;
		font-size: 18px;
		font-weight: bold;
}
.submit-btn.disabled.data-v-c3fa8b8a {
		background: #ccc;
		color: #999;
}
