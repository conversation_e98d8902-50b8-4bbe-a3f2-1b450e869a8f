"use strict";
const common_vendor = require("../../common/vendor.js");
const api_index = require("../../api/index.js");
const utils_auth = require("../../utils/auth.js");
const SvgIcon = () => "../../components/svg-icon/svg-icon.js";
const _sfc_main = {
  components: {
    SvgIcon
  },
  data() {
    return {
      userInfo: {},
      userStats: {
        points: 0,
        orders: 0,
        coupons: 0
      },
      orderCounts: {
        pending: 0,
        processing: 0,
        shipped: 0,
        completed: 0
      },
      isLoggedIn: false,
      // 头像昵称编辑相关
      showProfileModal: false,
      tempNickname: "",
      tempAvatarUrl: "",
      canUseChooseAvatar: false,
      // 是否支持新版头像选择
      isLoadingStats: false
      // 是否正在加载统计数据
    };
  },
  async onLoad() {
    common_vendor.index.__f__("log", "at pages/user/index.vue:211", "用户中心页面 onLoad");
    this.checkChooseAvatarSupport();
    this.loadUserInfo();
    await this.loadUserStats();
  },
  async onShow() {
    common_vendor.index.__f__("log", "at pages/user/index.vue:217", "用户中心页面 onShow");
    this.loadUserInfo();
    if (!this.userInfo.id || this.isLoggedIn !== utils_auth.AuthUtils.isLoggedIn()) {
      await this.loadUserStats();
    }
  },
  methods: {
    // 加载用户信息
    loadUserInfo() {
      this.isLoggedIn = utils_auth.AuthUtils.isLoggedIn();
      if (!this.userInfo.id) {
        this.userInfo = utils_auth.AuthUtils.getCurrentUser() || {};
      }
    },
    // 加载用户统计数据
    async loadUserStats() {
      var _a;
      if (this.isLoadingStats) {
        common_vendor.index.__f__("log", "at pages/user/index.vue:240", "正在加载中，跳过重复请求");
        return;
      }
      try {
        const token = common_vendor.index.getStorageSync("token");
        if (!token) {
          this.userStats = {
            points: 0,
            orders: 0,
            coupons: 0
          };
          this.orderCounts = {
            pending: 0,
            processing: 0,
            shipped: 0,
            completed: 0
          };
          return;
        }
        this.isLoadingStats = true;
        common_vendor.index.__f__("log", "at pages/user/index.vue:263", "开始加载用户统计数据");
        try {
          const userInfoResult = await api_index.userAPI.getProfile();
          if (userInfoResult.data) {
            const userData = userInfoResult.data;
            this.userInfo = {
              ...userData,
              nickName: userData.nickname,
              // 转换字段名
              avatarUrl: userData.avatar
              // 转换字段名
            };
            common_vendor.index.setStorageSync("userInfo", this.userInfo);
            this.userStats = {
              points: userData.available_points || 0,
              orders: ((_a = userData.stats) == null ? void 0 : _a.total_orders) || 0,
              coupons: 0
            };
            common_vendor.index.__f__("log", "at pages/user/index.vue:286", "用户信息加载成功");
          }
        } catch (userInfoError) {
          common_vendor.index.__f__("log", "at pages/user/index.vue:289", "获取用户信息失败，尝试统计接口");
        }
        try {
          const response = await api_index.userAPI.getStats();
          this.userStats = response.data.stats;
          this.orderCounts = response.data.orderCounts;
        } catch (statsError) {
          common_vendor.index.__f__("log", "at pages/user/index.vue:298", "统计接口不存在，使用默认数据");
          const userInfo = this.userInfo || common_vendor.index.getStorageSync("userInfo");
          if (userInfo) {
            this.userStats.points = userInfo.available_points || 0;
          }
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/user/index.vue:306", "获取用户统计数据失败:", error);
        this.userStats = {
          points: 1580,
          orders: 12,
          coupons: 3
        };
        this.orderCounts = {
          pending: 1,
          processing: 2,
          shipped: 1,
          completed: 8
        };
      }
    },
    // 处理用户信息点击
    handleUserClick() {
      if (!this.isLoggedIn) {
        this.goToLogin();
      } else {
        this.showProfileModal = true;
        this.tempNickname = this.userInfo.nickName || "";
        this.tempAvatarUrl = this.userInfo.avatarUrl || "";
      }
    },
    // 跳转到积分页面
    goToPoints() {
      common_vendor.index.switchTab({
        url: "/pages/points/index"
      });
    },
    // 跳转到订单页面
    goToOrders(status = "") {
      const url = status ? `/pages/user/orders?status=${status}` : "/pages/user/orders";
      common_vendor.index.navigateTo({
        url
      });
    },
    // 跳转到优惠券
    goToCoupons() {
      common_vendor.index.navigateTo({
        url: "/pages/user/coupons"
      });
    },
    // 跳转到收货地址
    goToAddress() {
      common_vendor.index.navigateTo({
        url: "/pages/address/list"
      });
    },
    // 跳转到收藏
    goToFavorites() {
      common_vendor.index.navigateTo({
        url: "/pages/user/favorites"
      });
    },
    // 跳转到帮助中心
    goToHelp() {
      common_vendor.index.navigateTo({
        url: "/pages/user/help"
      });
    },
    // 跳转到登录
    goToLogin() {
      utils_auth.AuthUtils.navigateToLogin();
    },
    // 退出登录
    async handleLogout() {
      common_vendor.index.showModal({
        title: "提示",
        content: "确定要退出登录吗？",
        success: async (res) => {
          if (res.confirm) {
            try {
              await utils_auth.AuthUtils.logout();
              this.userInfo = {};
              this.isLoggedIn = false;
              this.userStats = {
                points: 0,
                orders: 0,
                coupons: 0
              };
              this.orderCounts = {
                pending: 0,
                processing: 0,
                shipped: 0,
                completed: 0
              };
            } catch (error) {
              common_vendor.index.__f__("error", "at pages/user/index.vue:411", "退出登录失败:", error);
              common_vendor.index.showToast({
                title: "退出失败，请重试",
                icon: "none"
              });
            }
          }
        }
      });
    },
    // 关闭头像昵称编辑弹窗
    closeProfileModal() {
      this.showProfileModal = false;
      this.tempNickname = "";
      this.tempAvatarUrl = "";
    },
    // 选择头像回调
    onChooseAvatar(e) {
      var _a;
      common_vendor.index.__f__("log", "at pages/user/index.vue:431", "选择头像事件触发");
      common_vendor.index.__f__("log", "at pages/user/index.vue:432", "完整事件对象:", e);
      common_vendor.index.__f__("log", "at pages/user/index.vue:433", "事件详情:", e.detail);
      common_vendor.index.__f__("log", "at pages/user/index.vue:434", "头像URL:", (_a = e.detail) == null ? void 0 : _a.avatarUrl);
      if (e.detail && e.detail.avatarUrl) {
        common_vendor.index.__f__("log", "at pages/user/index.vue:437", "设置临时头像URL:", e.detail.avatarUrl);
        this.tempAvatarUrl = e.detail.avatarUrl;
        this.$forceUpdate();
      } else {
        common_vendor.index.__f__("log", "at pages/user/index.vue:443", "未获取到头像URL");
      }
    },
    // 昵称输入失焦回调
    onBlurNickname(e) {
      common_vendor.index.__f__("log", "at pages/user/index.vue:449", "昵称输入失焦:", e.detail.value);
      if (e.detail.value && e.detail.value.trim()) {
        this.tempNickname = e.detail.value.trim();
      }
    },
    // 保存头像昵称
    async saveProfile() {
      if (!this.tempNickname.trim()) {
        common_vendor.index.showToast({
          title: "请输入昵称",
          icon: "none"
        });
        return;
      }
      try {
        common_vendor.index.showLoading({
          title: "保存中..."
        });
        const response = await api_index.userAPI.updateProfile({
          nickname: this.tempNickname.trim(),
          avatar: this.tempAvatarUrl
        });
        if (response.code === 200) {
          this.userInfo.nickName = this.tempNickname.trim();
          this.userInfo.avatarUrl = this.tempAvatarUrl;
          this.userInfo.nickname = this.tempNickname.trim();
          this.userInfo.avatar = this.tempAvatarUrl;
          common_vendor.index.setStorageSync("userInfo", this.userInfo);
          common_vendor.index.hideLoading();
          common_vendor.index.showToast({
            title: "保存成功",
            icon: "success"
          });
          this.closeProfileModal();
          this.loadUserStats();
        } else {
          throw new Error(response.message || "保存失败");
        }
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages/user/index.vue:502", "保存头像昵称失败:", error);
        common_vendor.index.showToast({
          title: error.message || "保存失败",
          icon: "none"
        });
      }
    },
    // 图片加载成功
    onImageLoad(e) {
      common_vendor.index.__f__("log", "at pages/user/index.vue:512", "头像加载成功:", e);
    },
    // 图片加载失败
    onImageError(e) {
      common_vendor.index.__f__("log", "at pages/user/index.vue:517", "头像加载失败:", e);
    },
    // 检查是否支持新版头像选择
    checkChooseAvatarSupport() {
      const systemInfo = common_vendor.index.getSystemInfoSync();
      common_vendor.index.__f__("log", "at pages/user/index.vue:523", "系统信息:", systemInfo);
      const SDKVersion = systemInfo.SDKVersion;
      const version = SDKVersion.split(".").map((v) => parseInt(v));
      if (version[0] > 2 || version[0] === 2 && version[1] > 21 || version[0] === 2 && version[1] === 21 && version[2] >= 2) {
        this.canUseChooseAvatar = true;
        common_vendor.index.__f__("log", "at pages/user/index.vue:534", "支持新版头像选择");
      } else {
        this.canUseChooseAvatar = false;
        common_vendor.index.__f__("log", "at pages/user/index.vue:537", "不支持新版头像选择，当前版本:", SDKVersion);
      }
    },
    // 兼容旧版本的头像选择
    chooseAvatarFallback() {
      common_vendor.index.showModal({
        title: "提示",
        content: "当前微信版本不支持头像选择功能，请升级微信到最新版本",
        showCancel: false
      });
    }
  }
};
if (!Array) {
  const _component_uni_icons = common_vendor.resolveComponent("uni-icons");
  const _easycom_svg_icon2 = common_vendor.resolveComponent("svg-icon");
  (_component_uni_icons + _easycom_svg_icon2)();
}
const _easycom_svg_icon = () => "../../components/svg-icon/svg-icon.js";
if (!Math) {
  _easycom_svg_icon();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.userInfo.avatarUrl
  }, $data.userInfo.avatarUrl ? {
    b: $data.userInfo.avatarUrl,
    c: common_vendor.o((...args) => $options.onImageError && $options.onImageError(...args)),
    d: common_vendor.o((...args) => $options.onImageLoad && $options.onImageLoad(...args))
  } : {}, {
    e: common_vendor.t($data.isLoggedIn ? $data.userInfo.nickName : "登录/注册"),
    f: $data.isLoggedIn
  }, $data.isLoggedIn ? {
    g: common_vendor.t($data.userInfo.id || "000000")
  } : {}, {
    h: common_vendor.o((...args) => $options.handleUserClick && $options.handleUserClick(...args)),
    i: common_vendor.t($data.userStats.points),
    j: common_vendor.o((...args) => $options.goToPoints && $options.goToPoints(...args)),
    k: common_vendor.t($data.userStats.orders),
    l: common_vendor.o((...args) => $options.goToOrders && $options.goToOrders(...args)),
    m: common_vendor.t($data.userStats.coupons),
    n: common_vendor.o((...args) => $options.goToCoupons && $options.goToCoupons(...args)),
    o: common_vendor.p({
      type: "right",
      size: "14",
      color: "#999"
    }),
    p: common_vendor.o((...args) => $options.goToOrders && $options.goToOrders(...args)),
    q: common_vendor.p({
      name: "pending",
      size: 24,
      color: "#f39c12"
    }),
    r: $data.orderCounts.pending > 0
  }, $data.orderCounts.pending > 0 ? {
    s: common_vendor.t($data.orderCounts.pending)
  } : {}, {
    t: common_vendor.o(($event) => $options.goToOrders("pending")),
    v: common_vendor.p({
      name: "processing",
      size: 24,
      color: "#3498db"
    }),
    w: $data.orderCounts.processing > 0
  }, $data.orderCounts.processing > 0 ? {
    x: common_vendor.t($data.orderCounts.processing)
  } : {}, {
    y: common_vendor.o(($event) => $options.goToOrders("processing")),
    z: common_vendor.p({
      name: "shipped",
      size: 24,
      color: "#9b59b6"
    }),
    A: $data.orderCounts.shipped > 0
  }, $data.orderCounts.shipped > 0 ? {
    B: common_vendor.t($data.orderCounts.shipped)
  } : {}, {
    C: common_vendor.o(($event) => $options.goToOrders("shipped")),
    D: common_vendor.p({
      name: "completed",
      size: 24,
      color: "#27ae60"
    }),
    E: common_vendor.o(($event) => $options.goToOrders("completed")),
    F: common_vendor.p({
      type: "location",
      size: "20",
      color: "#667eea"
    }),
    G: common_vendor.p({
      type: "right",
      size: "14",
      color: "#999"
    }),
    H: common_vendor.o((...args) => $options.goToAddress && $options.goToAddress(...args)),
    I: common_vendor.p({
      type: "heart",
      size: "20",
      color: "#e74c3c"
    }),
    J: common_vendor.p({
      type: "right",
      size: "14",
      color: "#999"
    }),
    K: common_vendor.o((...args) => $options.goToFavorites && $options.goToFavorites(...args)),
    L: common_vendor.p({
      type: "help",
      size: "20",
      color: "#f39c12"
    }),
    M: common_vendor.p({
      type: "right",
      size: "14",
      color: "#999"
    }),
    N: common_vendor.o((...args) => $options.goToHelp && $options.goToHelp(...args)),
    O: $data.isLoggedIn
  }, $data.isLoggedIn ? {
    P: common_vendor.o((...args) => $options.handleLogout && $options.handleLogout(...args))
  } : {}, {
    Q: $data.showProfileModal
  }, $data.showProfileModal ? common_vendor.e({
    R: common_vendor.p({
      type: "close",
      size: "20",
      color: "#999"
    }),
    S: common_vendor.o((...args) => $options.closeProfileModal && $options.closeProfileModal(...args)),
    T: $data.canUseChooseAvatar
  }, $data.canUseChooseAvatar ? {
    U: $data.tempAvatarUrl || "/static/images/default-avatar.png",
    V: common_vendor.p({
      type: "right",
      size: "14",
      color: "#ccc"
    }),
    W: common_vendor.o((...args) => $options.onChooseAvatar && $options.onChooseAvatar(...args))
  } : {
    X: $data.tempAvatarUrl || "/static/images/default-avatar.png",
    Y: common_vendor.p({
      type: "right",
      size: "14",
      color: "#ccc"
    }),
    Z: common_vendor.o((...args) => $options.chooseAvatarFallback && $options.chooseAvatarFallback(...args))
  }, {
    aa: common_vendor.o((...args) => $options.onBlurNickname && $options.onBlurNickname(...args)),
    ab: $data.tempNickname,
    ac: common_vendor.o(($event) => $data.tempNickname = $event.detail.value),
    ad: common_vendor.o((...args) => $options.saveProfile && $options.saveProfile(...args)),
    ae: common_vendor.o(() => {
    }),
    af: common_vendor.o((...args) => $options.closeProfileModal && $options.closeProfileModal(...args))
  }) : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-79e6a490"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/user/index.js.map
