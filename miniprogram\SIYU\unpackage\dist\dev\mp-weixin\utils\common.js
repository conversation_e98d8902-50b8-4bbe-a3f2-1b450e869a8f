"use strict";
const common_vendor = require("../common/vendor.js");
const utils_request = require("./request.js");
const formatTime = (date, format = "YYYY-MM-DD HH:mm:ss") => {
  if (!date)
    return "";
  const d = new Date(date);
  if (isNaN(d.getTime()))
    return "";
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, "0");
  const day = String(d.getDate()).padStart(2, "0");
  const hour = String(d.getHours()).padStart(2, "0");
  const minute = String(d.getMinutes()).padStart(2, "0");
  const second = String(d.getSeconds()).padStart(2, "0");
  return format.replace("YYYY", year).replace("MM", month).replace("DD", day).replace("HH", hour).replace("mm", minute).replace("ss", second);
};
const timeAgo = (date) => {
  if (!date)
    return "";
  const now = /* @__PURE__ */ new Date();
  const target = new Date(date);
  const diff = now.getTime() - target.getTime();
  const minute = 60 * 1e3;
  const hour = 60 * minute;
  const day = 24 * hour;
  const month = 30 * day;
  const year = 12 * month;
  if (diff < minute) {
    return "刚刚";
  } else if (diff < hour) {
    return Math.floor(diff / minute) + "分钟前";
  } else if (diff < day) {
    return Math.floor(diff / hour) + "小时前";
  } else if (diff < month) {
    return Math.floor(diff / day) + "天前";
  } else if (diff < year) {
    return Math.floor(diff / month) + "个月前";
  } else {
    return Math.floor(diff / year) + "年前";
  }
};
const debounce = (func, delay = 300) => {
  let timer = null;
  return function(...args) {
    if (timer)
      clearTimeout(timer);
    timer = setTimeout(() => {
      func.apply(this, args);
    }, delay);
  };
};
const throttle = (func, delay = 300) => {
  let timer = null;
  return function(...args) {
    if (!timer) {
      timer = setTimeout(() => {
        func.apply(this, args);
        timer = null;
      }, delay);
    }
  };
};
const deepClone = (obj) => {
  if (obj === null || typeof obj !== "object")
    return obj;
  if (obj instanceof Date)
    return new Date(obj);
  if (obj instanceof Array)
    return obj.map((item) => deepClone(item));
  if (typeof obj === "object") {
    const clonedObj = {};
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key]);
      }
    }
    return clonedObj;
  }
};
const randomString = (length = 8) => {
  const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
  let result = "";
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
};
const formatFileSize = (size) => {
  if (!size)
    return "0 B";
  const units = ["B", "KB", "MB", "GB", "TB"];
  let index = 0;
  while (size >= 1024 && index < units.length - 1) {
    size /= 1024;
    index++;
  }
  return Math.round(size * 100) / 100 + " " + units[index];
};
const validatePhone = (phone) => {
  const reg = /^1[3-9]\d{9}$/;
  return reg.test(phone);
};
const validateEmail = (email) => {
  const reg = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  return reg.test(email);
};
const validateIdCard = (idCard) => {
  const reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
  return reg.test(idCard);
};
const getUrlParam = (name, url = window.location.href) => {
  const reg = new RegExp("[?&]" + name + "=([^&#]*)", "i");
  const match = url.match(reg);
  return match ? decodeURIComponent(match[1]) : null;
};
const setPageTitle = (title) => {
  common_vendor.index.setNavigationBarTitle({
    title
  });
};
const showSuccess = (message) => {
  common_vendor.index.showToast({
    title: message,
    icon: "success",
    duration: 2e3
  });
};
const showError = (message) => {
  common_vendor.index.showToast({
    title: message,
    icon: "none",
    duration: 2e3
  });
};
const showLoading = (message = "加载中...") => {
  common_vendor.index.showLoading({
    title: message,
    mask: true
  });
};
const hideLoading = () => {
  common_vendor.index.hideLoading();
};
const showConfirm = (content, title = "提示") => {
  return new Promise((resolve, reject) => {
    common_vendor.index.showModal({
      title,
      content,
      success: (res) => {
        if (res.confirm) {
          resolve(true);
        } else {
          reject(false);
        }
      },
      fail: reject
    });
  });
};
const navigateTo = (url, params = {}) => {
  const query = Object.keys(params).map((key) => `${key}=${encodeURIComponent(params[key])}`).join("&");
  const fullUrl = query ? `${url}?${query}` : url;
  common_vendor.index.navigateTo({
    url: fullUrl,
    fail: (err) => {
      common_vendor.index.__f__("error", "at utils/common.js:269", "页面跳转失败:", err);
    }
  });
};
const navigateBack = (delta = 1) => {
  common_vendor.index.navigateBack({
    delta
  });
};
const switchTab = (url) => {
  common_vendor.index.switchTab({
    url,
    fail: (err) => {
      common_vendor.index.__f__("error", "at utils/common.js:292", "Tab切换失败:", err);
    }
  });
};
const getSystemInfo = () => {
  return new Promise((resolve, reject) => {
    common_vendor.index.getSystemInfo({
      success: resolve,
      fail: reject
    });
  });
};
const getLocation = () => {
  return new Promise((resolve, reject) => {
    common_vendor.index.getLocation({
      type: "gcj02",
      success: resolve,
      fail: reject
    });
  });
};
const chooseImage = (count = 1) => {
  return new Promise((resolve, reject) => {
    common_vendor.index.chooseImage({
      count,
      sizeType: ["compressed"],
      sourceType: ["album", "camera"],
      success: resolve,
      fail: reject
    });
  });
};
const previewImage = (urls, current = 0) => {
  common_vendor.index.previewImage({
    urls,
    current: typeof current === "number" ? urls[current] : current
  });
};
({
  formatTime,
  timeAgo,
  debounce,
  throttle,
  deepClone,
  randomString,
  formatFileSize,
  validatePhone,
  validateEmail,
  validateIdCard,
  getUrlParam,
  setPageTitle,
  showSuccess,
  showError,
  showLoading,
  hideLoading,
  showConfirm,
  navigateTo,
  navigateBack,
  switchTab,
  getSystemInfo,
  getLocation,
  chooseImage,
  previewImage,
  processHtmlImages
});
const processHtmlImages = (htmlContent) => {
  if (!htmlContent)
    return htmlContent;
  return htmlContent.replace(/src=["']([^"']+)["']/g, (match, src) => {
    const fullUrl = utils_request.getImageUrl(src);
    return `src="${fullUrl}"`;
  });
};
exports.processHtmlImages = processHtmlImages;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/common.js.map
