{"version": 3, "file": "region.js", "sources": ["pages/test/region.vue", "../../../Program Files/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvdGVzdC9yZWdpb24udnVl"], "sourcesContent": ["<template>\n\t<view class=\"test-container\">\n\t\t<view class=\"test-section\">\n\t\t\t<text class=\"section-title\">地区选择测试</text>\n\t\t\t\n\t\t\t<view class=\"form-item\" @click=\"showPicker\">\n\t\t\t\t<text class=\"label\">选择地区</text>\n\t\t\t\t<view class=\"region-display\">\n\t\t\t\t\t<text v-if=\"selectedRegion.province\">\n\t\t\t\t\t\t{{ selectedRegion.province }} {{ selectedRegion.city }} {{ selectedRegion.district }}\n\t\t\t\t\t</text>\n\t\t\t\t\t<text v-else class=\"placeholder\">请选择地区</text>\n\t\t\t\t\t<text class=\"arrow\">></text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"result-section\" v-if=\"selectedRegion.province\">\n\t\t\t\t<text class=\"result-title\">选择结果：</text>\n\t\t\t\t<view class=\"result-item\">\n\t\t\t\t\t<text class=\"result-label\">省份：</text>\n\t\t\t\t\t<text class=\"result-value\">{{ selectedRegion.province }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"result-item\">\n\t\t\t\t\t<text class=\"result-label\">城市：</text>\n\t\t\t\t\t<text class=\"result-value\">{{ selectedRegion.city }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"result-item\">\n\t\t\t\t\t<text class=\"result-label\">区县：</text>\n\t\t\t\t\t<text class=\"result-value\">{{ selectedRegion.district }}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 地区选择器 -->\n\t\t<region-picker \n\t\t\t:visible=\"pickerVisible\" \n\t\t\t:value=\"selectedRegion\"\n\t\t\t@confirm=\"onRegionConfirm\"\n\t\t\t@close=\"pickerVisible = false\"\n\t\t/>\n\t</view>\n</template>\n\n<script>\nimport RegionPicker from '@/components/region-picker/region-picker.vue'\n\nexport default {\n\tcomponents: {\n\t\tRegionPicker\n\t},\n\tdata() {\n\t\treturn {\n\t\t\tpickerVisible: false,\n\t\t\tselectedRegion: {\n\t\t\t\tprovince: '',\n\t\t\t\tcity: '',\n\t\t\t\tdistrict: ''\n\t\t\t}\n\t\t}\n\t},\n\tmethods: {\n\t\tshowPicker() {\n\t\t\tthis.pickerVisible = true\n\t\t},\n\t\t\n\t\tonRegionConfirm(region) {\n\t\t\tthis.selectedRegion = {\n\t\t\t\tprovince: region.province,\n\t\t\t\tcity: region.city,\n\t\t\t\tdistrict: region.district\n\t\t\t}\n\t\t\tconsole.log('选择的地区:', region)\n\t\t}\n\t}\n}\n</script>\n\n<style scoped>\n.test-container {\n\tpadding: 20px;\n\tbackground: #f8f8f8;\n\tmin-height: 100vh;\n}\n\n.test-section {\n\tbackground: white;\n\tborder-radius: 12px;\n\tpadding: 20px;\n}\n\n.section-title {\n\tfont-size: 18px;\n\tfont-weight: bold;\n\tcolor: #333;\n\tmargin-bottom: 20px;\n\tdisplay: block;\n}\n\n.form-item {\n\tpadding: 15px 0;\n\tborder-bottom: 1px solid #f0f0f0;\n}\n\n.label {\n\tfont-size: 16px;\n\tcolor: #333;\n\tmargin-bottom: 10px;\n\tdisplay: block;\n}\n\n.region-display {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tpadding: 10px 0;\n}\n\n.placeholder {\n\tcolor: #999;\n}\n\n.arrow {\n\tcolor: #999;\n}\n\n.result-section {\n\tmargin-top: 20px;\n\tpadding-top: 20px;\n\tborder-top: 1px solid #f0f0f0;\n}\n\n.result-title {\n\tfont-size: 16px;\n\tfont-weight: bold;\n\tcolor: #333;\n\tmargin-bottom: 15px;\n\tdisplay: block;\n}\n\n.result-item {\n\tdisplay: flex;\n\tmargin-bottom: 10px;\n}\n\n.result-label {\n\twidth: 60px;\n\tcolor: #666;\n}\n\n.result-value {\n\tcolor: #333;\n\tfont-weight: 500;\n}\n</style>\n", "import MiniProgramPage from 'D:/app/miniprogram/SIYU/pages/test/region.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AA4CA,MAAO,eAAc,MAAW;AAEhC,MAAK,YAAU;AAAA,EACd,YAAY;AAAA,IACX;AAAA,EACA;AAAA,EACD,OAAO;AACN,WAAO;AAAA,MACN,eAAe;AAAA,MACf,gBAAgB;AAAA,QACf,UAAU;AAAA,QACV,MAAM;AAAA,QACN,UAAU;AAAA,MACX;AAAA,IACD;AAAA,EACA;AAAA,EACD,SAAS;AAAA,IACR,aAAa;AACZ,WAAK,gBAAgB;AAAA,IACrB;AAAA,IAED,gBAAgB,QAAQ;AACvB,WAAK,iBAAiB;AAAA,QACrB,UAAU,OAAO;AAAA,QACjB,MAAM,OAAO;AAAA,QACb,UAAU,OAAO;AAAA,MAClB;AACAA,oBAAAA,MAAA,MAAA,OAAA,+BAAY,UAAU,MAAM;AAAA,IAC7B;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzEA,GAAG,WAAW,eAAe;"}