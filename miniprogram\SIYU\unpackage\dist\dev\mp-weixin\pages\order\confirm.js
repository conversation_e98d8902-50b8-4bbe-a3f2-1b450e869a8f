"use strict";
const common_vendor = require("../../common/vendor.js");
const api_index = require("../../api/index.js");
const _sfc_main = {
  data() {
    return {
      productId: 0,
      quantity: 1,
      type: "exchange",
      product: {},
      selectedAddress: null,
      submitting: false
    };
  },
  computed: {
    totalPoints() {
      return this.product.points_price * this.quantity;
    },
    canSubmit() {
      return this.selectedAddress && !this.submitting;
    }
  },
  onLoad(options) {
    this.productId = parseInt(options.productId);
    this.quantity = parseInt(options.quantity) || 1;
    this.type = options.type || "exchange";
    this.loadProduct();
    this.loadDefaultAddress();
  },
  methods: {
    // 加载商品信息
    async loadProduct() {
      try {
        const response = await api_index.productAPI.getDetail(this.productId);
        this.product = response.data;
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/order/confirm.vue:105", "获取商品信息失败:", error);
        common_vendor.index.showToast({
          title: "商品信息加载失败",
          icon: "none"
        });
      }
    },
    // 加载默认地址
    async loadDefaultAddress() {
      try {
        const response = await api_index.addressAPI.getDefault();
        if (response.data) {
          this.selectedAddress = {
            id: response.data.id,
            name: response.data.name,
            phone: response.data.phone,
            fullAddress: response.data.full_address
          };
        }
      } catch (error) {
        common_vendor.index.__f__("log", "at pages/order/confirm.vue:126", "获取默认地址失败:", error);
      }
    },
    // 选择地址
    selectAddress() {
      common_vendor.index.navigateTo({
        url: "/pages/address/list?from=order"
      });
    },
    // 提交订单
    async submitOrder() {
      if (!this.canSubmit)
        return;
      this.submitting = true;
      try {
        const orderData = {
          products: [{
            product_id: this.productId,
            quantity: this.quantity
          }],
          address_info: JSON.stringify(this.selectedAddress)
        };
        const response = await api_index.orderAPI.create(orderData);
        common_vendor.index.showToast({
          title: "兑换成功",
          icon: "success"
        });
        setTimeout(() => {
          common_vendor.index.redirectTo({
            url: "/pages/user/orders"
          });
        }, 1500);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/order/confirm.vue:167", "提交订单失败:", error);
        common_vendor.index.showToast({
          title: error.message || "兑换失败",
          icon: "none"
        });
      } finally {
        this.submitting = false;
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.selectedAddress
  }, $data.selectedAddress ? {
    b: common_vendor.t($data.selectedAddress.name),
    c: common_vendor.t($data.selectedAddress.phone),
    d: common_vendor.t($data.selectedAddress.fullAddress)
  } : {}, {
    e: common_vendor.o((...args) => $options.selectAddress && $options.selectAddress(...args)),
    f: $data.product.image
  }, $data.product.image ? {
    g: $data.product.image
  } : {
    h: common_vendor.t($data.product.emoji || "🎁")
  }, {
    i: common_vendor.t($data.product.name),
    j: common_vendor.t($data.product.points_price),
    k: common_vendor.t($data.quantity),
    l: common_vendor.t($options.totalPoints),
    m: common_vendor.t($options.totalPoints),
    n: common_vendor.t($options.totalPoints),
    o: common_vendor.t($data.submitting ? "提交中..." : "确认兑换"),
    p: !$options.canSubmit,
    q: common_vendor.o((...args) => $options.submitOrder && $options.submitOrder(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-324e7894"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/order/confirm.js.map
