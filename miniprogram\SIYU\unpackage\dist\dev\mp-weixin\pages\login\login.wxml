<view class="login-container data-v-e4e4508d"><view class="nav-bar data-v-e4e4508d"><view class="nav-left data-v-e4e4508d" bindtap="{{b}}"><uni-icons wx:if="{{a}}" class="data-v-e4e4508d" u-i="e4e4508d-0" bind:__l="__l" u-p="{{a}}"></uni-icons></view></view><view class="main-content data-v-e4e4508d"><view class="logo-section data-v-e4e4508d"><view class="logo-container data-v-e4e4508d"><view class="logo-bg data-v-e4e4508d"><text class="logo-text data-v-e4e4508d">思语</text></view><view class="logo-decoration data-v-e4e4508d"><view class="decoration-left data-v-e4e4508d"></view><view class="decoration-right data-v-e4e4508d"></view></view></view></view><view class="agreement-section data-v-e4e4508d"><view class="agreement-checkbox data-v-e4e4508d" bindtap="{{e}}"><view class="{{['checkbox-square', 'data-v-e4e4508d', d && 'checked']}}"><view wx:if="{{c}}" class="checkbox-mark data-v-e4e4508d"></view></view></view><text class="agreement-text data-v-e4e4508d">同意并接受</text><text class="agreement-link data-v-e4e4508d" bindtap="{{f}}">《服务协议》</text><text class="agreement-link data-v-e4e4508d" bindtap="{{g}}">《隐私政策》</text></view><button class="{{['wechat-login-btn', 'data-v-e4e4508d', i && 'disabled']}}" disabled="{{j}}" bindtap="{{k}}" open-type="getPhoneNumber" bindgetphonenumber="{{l}}"><text wx:if="{{h}}" class="data-v-e4e4508d">登录中...</text><text wx:else class="data-v-e4e4508d">免费开通会员</text></button><button class="browse-btn data-v-e4e4508d" bindtap="{{m}}"> 先去逛逛 </button></view></view>