
.favorites-container.data-v-fef149ca {
	min-height: 100vh;
	background-color: #f5f5f5;
}

/* 空状态样式 */
.empty-state.data-v-fef149ca {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 120rpx 40rpx;
	text-align: center;
}
.empty-icon.data-v-fef149ca {
	font-size: 120rpx;
	margin-bottom: 40rpx;
}
.empty-text.data-v-fef149ca {
	font-size: 32rpx;
	color: #333;
	margin-bottom: 20rpx;
	font-weight: 500;
}
.empty-desc.data-v-fef149ca {
	font-size: 28rpx;
	color: #999;
	margin-bottom: 60rpx;
}
.go-shopping-btn.data-v-fef149ca {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	border: none;
	border-radius: 50rpx;
	padding: 24rpx 60rpx;
	font-size: 28rpx;
}

/* 收藏列表样式 */
.favorites-list.data-v-fef149ca {
	padding: 20rpx;
}
.favorite-item.data-v-fef149ca {
	display: flex;
	align-items: center;
	background: white;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.product-image.data-v-fef149ca {
	width: 120rpx;
	height: 120rpx;
	border-radius: 16rpx;
	overflow: hidden;
	margin-right: 30rpx;
}
.image.data-v-fef149ca {
	width: 100%;
	height: 100%;
}
.product-info.data-v-fef149ca {
	flex: 1;
	display: flex;
	flex-direction: column;
}
.product-name.data-v-fef149ca {
	font-size: 30rpx;
	color: #333;
	font-weight: 500;
	margin-bottom: 10rpx;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
.product-desc.data-v-fef149ca {
	font-size: 24rpx;
	color: #999;
	margin-bottom: 20rpx;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
.product-price.data-v-fef149ca {
	display: flex;
	align-items: center;
}
.price-text.data-v-fef149ca {
	font-size: 28rpx;
	color: #e74c3c;
	font-weight: 600;
}
.action-btn.data-v-fef149ca {
	padding: 20rpx;
	margin-left: 20rpx;
}
