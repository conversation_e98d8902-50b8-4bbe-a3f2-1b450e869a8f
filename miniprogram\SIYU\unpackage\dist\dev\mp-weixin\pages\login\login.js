"use strict";
const common_vendor = require("../../common/vendor.js");
const api_index = require("../../api/index.js");
const _sfc_main = {
  data() {
    return {
      isLoading: false,
      agreedToTerms: false
    };
  },
  methods: {
    // 返回上一页
    goBack() {
      common_vendor.index.navigateBack({
        delta: 1
      });
    },
    // 切换协议同意状态
    toggleAgreement() {
      this.agreedToTerms = !this.agreedToTerms;
    },
    // 显示服务协议
    showServiceAgreement() {
      common_vendor.index.showModal({
        title: "服务协议",
        content: "这里是服务协议的内容...",
        showCancel: false
      });
    },
    // 显示隐私政策
    showPrivacyPolicy() {
      common_vendor.index.showModal({
        title: "隐私政策",
        content: "这里是隐私政策的内容...",
        showCancel: false
      });
    },
    // 处理微信登录
    async handleWechatLogin() {
      if (!this.agreedToTerms) {
        common_vendor.index.showToast({
          title: "请先同意协议",
          icon: "none"
        });
        return;
      }
      try {
        const userProfile = await common_vendor.index.getUserProfile({
          desc: "用于完善用户资料"
        });
        common_vendor.index.__f__("log", "at pages/login/login.vue:117", "获取用户信息成功:", userProfile);
        this.tempUserInfo = userProfile.userInfo;
      } catch (error) {
        common_vendor.index.__f__("log", "at pages/login/login.vue:123", "用户取消授权用户信息");
        common_vendor.index.showToast({
          title: "需要授权用户信息才能登录",
          icon: "none"
        });
        return;
      }
    },
    // 获取手机号回调
    async onGetPhoneNumber(e) {
      common_vendor.index.__f__("log", "at pages/login/login.vue:136", "获取手机号结果:", e.detail);
      if (e.detail.errMsg === "getPhoneNumber:ok") {
        this.isLoading = true;
        try {
          const loginRes = await common_vendor.index.login();
          if (loginRes.code) {
            const response = await api_index.authAPI.login({
              code: loginRes.code,
              encryptedData: e.detail.encryptedData,
              iv: e.detail.iv
            });
            if (response.code === 200) {
              common_vendor.index.setStorageSync("token", response.data.token);
              common_vendor.index.setStorageSync("userInfo", response.data.user);
              common_vendor.index.showToast({
                title: response.data.is_new_user ? "注册成功" : "登录成功",
                icon: "success"
              });
              setTimeout(() => {
                common_vendor.index.switchTab({
                  url: "/pages/index/index"
                });
              }, 1500);
            } else {
              throw new Error(response.message || "登录失败");
            }
          } else {
            throw new Error("获取微信登录code失败");
          }
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/login/login.vue:176", "登录失败:", error);
          common_vendor.index.showToast({
            title: error.message || "登录失败",
            icon: "none"
          });
        } finally {
          this.isLoading = false;
        }
      } else {
        common_vendor.index.showToast({
          title: "需要授权手机号才能登录",
          icon: "none"
        });
      }
    },
    // 先去逛逛
    browseMall() {
      common_vendor.index.switchTab({
        url: "/pages/index/index"
      });
    }
  }
};
if (!Array) {
  const _component_uni_icons = common_vendor.resolveComponent("uni-icons");
  _component_uni_icons();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.p({
      type: "left",
      size: "20",
      color: "#333"
    }),
    b: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    c: $data.agreedToTerms
  }, $data.agreedToTerms ? {} : {}, {
    d: $data.agreedToTerms ? 1 : "",
    e: common_vendor.o((...args) => $options.toggleAgreement && $options.toggleAgreement(...args)),
    f: common_vendor.o((...args) => $options.showServiceAgreement && $options.showServiceAgreement(...args)),
    g: common_vendor.o((...args) => $options.showPrivacyPolicy && $options.showPrivacyPolicy(...args)),
    h: $data.isLoading
  }, $data.isLoading ? {} : {}, {
    i: !$data.agreedToTerms || $data.isLoading ? 1 : "",
    j: !$data.agreedToTerms || $data.isLoading,
    k: common_vendor.o((...args) => $options.handleWechatLogin && $options.handleWechatLogin(...args)),
    l: common_vendor.o((...args) => $options.onGetPhoneNumber && $options.onGetPhoneNumber(...args)),
    m: common_vendor.o((...args) => $options.browseMall && $options.browseMall(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-e4e4508d"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/login/login.js.map
