<?php
/**
 * 紧急修复API系统
 * 临时删除有问题的AddressController，恢复API系统正常运行
 */

header('Content-Type: application/json; charset=utf-8');

try {
    $addressControllerPath = __DIR__ . '/api/controllers/api/AddressController.php';
    $routesPath = __DIR__ . '/api/routes.php';
    
    // 步骤1：备份并删除有问题的AddressController
    if (file_exists($addressControllerPath)) {
        $backupPath = $addressControllerPath . '.backup.' . date('YmdHis');
        copy($addressControllerPath, $backupPath);
        unlink($addressControllerPath);
        
        $step1 = "✅ 已备份并删除有问题的AddressController.php";
    } else {
        $step1 = "ℹ️ AddressController.php 文件不存在";
    }
    
    // 步骤2：注释掉routes.php中的地址相关路由
    if (file_exists($routesPath)) {
        $routesContent = file_get_contents($routesPath);
        
        // 注释掉AddressController的引用和路由
        $routesContent = preg_replace(
            '/require_once.*AddressController\.php.*\n/',
            '// require_once __DIR__ . \'/controllers/api/AddressController.php\'; // 临时注释\n',
            $routesContent
        );
        
        // 注释掉地址相关的路由
        $routesContent = preg_replace(
            '/\$router->get\(\'\/addresses.*?\);/s',
            '// $router->get(\'/addresses\', [new AddressController(), \'index\']); // 临时注释',
            $routesContent
        );
        
        $routesContent = preg_replace(
            '/\$router->post\(\'\/addresses.*?\);/s',
            '// $router->post(\'/addresses\', [new AddressController(), \'create\']); // 临时注释',
            $routesContent
        );
        
        $routesContent = preg_replace(
            '/\$router->get\(\'\/addresses\/default.*?\);/s',
            '// $router->get(\'/addresses/default\', [new AddressController(), \'getDefault\']); // 临时注释',
            $routesContent
        );
        
        file_put_contents($routesPath, $routesContent);
        $step2 = "✅ 已注释掉routes.php中的地址相关路由";
    } else {
        $step2 = "❌ routes.php 文件不存在";
    }
    
    // 步骤3：测试API系统是否恢复
    $testResult = "🔄 请手动测试其他API是否恢复正常";
    
    echo json_encode([
        'status' => 'success',
        'message' => 'API系统紧急修复完成',
        'steps' => [
            'step1' => $step1,
            'step2' => $step2,
            'step3' => $testResult
        ],
        'next_actions' => [
            '1. 测试其他API是否恢复正常',
            '2. 重新创建正确的AddressController.php',
            '3. 恢复地址相关路由'
        ]
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        'status' => 'error',
        'message' => '紧急修复失败: ' . $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ], JSON_UNESCAPED_UNICODE);
}
?>
