
.confirm-container.data-v-324e7894 {
	background: #f8f8f8;
	min-height: 100vh;
	padding-bottom: 80px;
}
.address-section.data-v-324e7894, .product-section.data-v-324e7894, .points-section.data-v-324e7894 {
	background: white;
	margin-bottom: 10px;
	padding: 15px;
}
.section-header.data-v-324e7894 {
	display: flex;
	align-items: center;
	margin-bottom: 15px;
}
.section-title.data-v-324e7894 {
	font-size: 16px;
	font-weight: bold;
	color: #333;
}
.required.data-v-324e7894 {
	color: #ff4757;
	margin-left: 4px;
}
.address-card.data-v-324e7894 {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 15px;
	background: #f8f9fa;
	border-radius: 8px;
}
.address-info.data-v-324e7894 {
	flex: 1;
}
.name-phone.data-v-324e7894 {
	display: flex;
	align-items: center;
	margin-bottom: 8px;
}
.name.data-v-324e7894 {
	font-size: 16px;
	font-weight: bold;
	color: #333;
	margin-right: 15px;
}
.phone.data-v-324e7894 {
	font-size: 14px;
	color: #666;
}
.address.data-v-324e7894 {
	font-size: 14px;
	color: #666;
	line-height: 1.4;
}
.change-btn.data-v-324e7894 {
	color: #667eea;
	font-size: 14px;
}
.address-empty.data-v-324e7894 {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20px;
	background: #f8f9fa;
	border-radius: 8px;
	border: 2px dashed #ddd;
}
.empty-text.data-v-324e7894 {
	color: #999;
}
.arrow.data-v-324e7894 {
	color: #999;
}
.product-card.data-v-324e7894 {
	display: flex;
	align-items: center;
	padding: 15px 0;
}
.product-image.data-v-324e7894 {
	width: 60px;
	height: 60px;
	border-radius: 8px;
	margin-right: 15px;
}
.product-emoji.data-v-324e7894 {
	width: 60px;
	height: 60px;
	line-height: 60px;
	text-align: center;
	font-size: 30px;
	background: #f8f9fa;
	border-radius: 8px;
	margin-right: 15px;
}
.product-info.data-v-324e7894 {
	flex: 1;
}
.product-name.data-v-324e7894 {
	font-size: 16px;
	color: #333;
	margin-bottom: 5px;
	display: block;
}
.product-spec.data-v-324e7894 {
	font-size: 12px;
	color: #999;
	margin-bottom: 8px;
	display: block;
}
.price-quantity.data-v-324e7894 {
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.points-price.data-v-324e7894 {
	color: #667eea;
	font-weight: bold;
}
.quantity.data-v-324e7894 {
	color: #666;
}
.points-item.data-v-324e7894 {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 10px 0;
}
.points-label.data-v-324e7894 {
	color: #666;
}
.points-value.data-v-324e7894 {
	color: #333;
}
.points-total.data-v-324e7894 {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 15px 0;
	border-top: 1px solid #f0f0f0;
	margin-top: 10px;
}
.total-label.data-v-324e7894 {
	font-size: 16px;
	font-weight: bold;
	color: #333;
}
.total-value.data-v-324e7894 {
	font-size: 18px;
	font-weight: bold;
	color: #667eea;
}
.bottom-submit.data-v-324e7894 {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: white;
	padding: 15px;
	border-top: 1px solid #f0f0f0;
	display: flex;
	align-items: center;
	justify-content: space-between;
}
.submit-info.data-v-324e7894 {
	display: flex;
	align-items: center;
}
.submit-label.data-v-324e7894 {
	color: #666;
	margin-right: 5px;
}
.submit-points.data-v-324e7894 {
	color: #667eea;
	font-weight: bold;
	font-size: 16px;
}
.submit-btn.data-v-324e7894 {
	background: #667eea;
	color: white;
	border: none;
	border-radius: 25px;
	padding: 12px 30px;
	font-size: 16px;
	font-weight: bold;
}
.submit-btn.data-v-324e7894:disabled {
	background: #ccc;
}
