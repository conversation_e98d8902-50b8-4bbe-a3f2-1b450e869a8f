
.address-edit-container.data-v-dcb1f0d8 {
	background: #f8f8f8;
	min-height: 100vh;
	padding: 10px;
}
.form-section.data-v-dcb1f0d8 {
	background: white;
	border-radius: 12px;
	margin-bottom: 10px;
	overflow: hidden;
}
.form-item.data-v-dcb1f0d8 {
	padding: 20px;
	border-bottom: 1px solid #f0f0f0;
}
.form-item.data-v-dcb1f0d8:last-child {
	border-bottom: none;
}
.label.data-v-dcb1f0d8 {
	display: block;
	font-size: 16px;
	color: #333;
	margin-bottom: 10px;
}
.required.data-v-dcb1f0d8 {
	color: #ff4757;
}
.input.data-v-dcb1f0d8, .textarea.data-v-dcb1f0d8 {
	width: 100%;
	font-size: 16px;
	color: #333;
	background: transparent;
	border: none;
	outline: none;
}
.input.data-v-dcb1f0d8::-webkit-input-placeholder, .textarea.data-v-dcb1f0d8::-webkit-input-placeholder {
	color: #999;
}
.input.data-v-dcb1f0d8::placeholder, .textarea.data-v-dcb1f0d8::placeholder {
	color: #999;
}
.region-selector.data-v-dcb1f0d8 {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 10px 0;
}
.region-text.data-v-dcb1f0d8 {
	color: #333;
	font-size: 16px;
}
.placeholder.data-v-dcb1f0d8 {
	color: #999;
	font-size: 16px;
}
.arrow.data-v-dcb1f0d8 {
	color: #999;
	font-size: 16px;
}
.switch-item.data-v-dcb1f0d8 {
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.submit-section.data-v-dcb1f0d8 {
	padding: 20px 0;
}
.submit-btn.data-v-dcb1f0d8 {
	width: 100%;
	background: #667eea;
	color: white;
	border: none;
	border-radius: 25px;
	padding: 15px;
	font-size: 16px;
	font-weight: bold;
}
.submit-btn.data-v-dcb1f0d8:disabled {
	background: #ccc;
}
