{"version": 3, "file": "favorites.js", "sources": ["pages/user/favorites.vue", "../../../Program Files/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvdXNlci9mYXZvcml0ZXMudnVl"], "sourcesContent": ["<template>\n\t<view class=\"favorites-container\">\n\t\t<!-- 空状态 -->\n\t\t<view v-if=\"favoritesList.length === 0\" class=\"empty-state\">\n\t\t\t<view class=\"empty-icon\">💝</view>\n\t\t\t<text class=\"empty-text\">暂无收藏商品</text>\n\t\t\t<text class=\"empty-desc\">快去收藏你喜欢的商品吧</text>\n\t\t\t<button class=\"go-shopping-btn\" @click=\"goShopping\">去逛逛</button>\n\t\t</view>\n\n\t\t<!-- 收藏列表 -->\n\t\t<view v-else class=\"favorites-list\">\n\t\t\t<view \n\t\t\t\tv-for=\"item in favoritesList\" \n\t\t\t\t:key=\"item.id\" \n\t\t\t\tclass=\"favorite-item\"\n\t\t\t\t@click=\"goToProduct(item.product_id)\"\n\t\t\t>\n\t\t\t\t<view class=\"product-image\">\n\t\t\t\t\t<image \n\t\t\t\t\t\t:src=\"item.product_image || '/static/images/placeholder.png'\" \n\t\t\t\t\t\tmode=\"aspectFill\"\n\t\t\t\t\t\tclass=\"image\"\n\t\t\t\t\t/>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"product-info\">\n\t\t\t\t\t<text class=\"product-name\">{{ item.product_name }}</text>\n\t\t\t\t\t<text class=\"product-desc\">{{ item.product_desc || '精选好物，值得拥有' }}</text>\n\t\t\t\t\t<view class=\"product-price\">\n\t\t\t\t\t\t<text class=\"price-text\">{{ item.points_price }}积分</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"action-btn\" @click.stop=\"removeFavorite(item.id)\">\n\t\t\t\t\t<uni-icons type=\"heart-filled\" size=\"20\" color=\"#e74c3c\"></uni-icons>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tfavoritesList: []\n\t\t}\n\t},\n\t\n\tonLoad() {\n\t\tthis.loadFavorites()\n\t},\n\t\n\tonShow() {\n\t\tthis.loadFavorites()\n\t},\n\t\n\tmethods: {\n\t\t// 加载收藏列表\n\t\tasync loadFavorites() {\n\t\t\ttry {\n\t\t\t\t// TODO: 调用收藏API\n\t\t\t\t// const response = await favoriteAPI.getFavorites()\n\t\t\t\t// this.favoritesList = response.data\n\t\t\t\t\n\t\t\t\t// 临时模拟数据\n\t\t\t\tthis.favoritesList = []\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('加载收藏失败:', error)\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '加载失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t})\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 移除收藏\n\t\tasync removeFavorite(favoriteId) {\n\t\t\ttry {\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '提示',\n\t\t\t\t\tcontent: '确定要取消收藏吗？',\n\t\t\t\t\tsuccess: async (res) => {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\t// TODO: 调用取消收藏API\n\t\t\t\t\t\t\t// await favoriteAPI.removeFavorite(favoriteId)\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 临时处理\n\t\t\t\t\t\t\tthis.favoritesList = this.favoritesList.filter(item => item.id !== favoriteId)\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '已取消收藏',\n\t\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('取消收藏失败:', error)\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '操作失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t})\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 跳转到商品详情\n\t\tgoToProduct(productId) {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: `/pages/product/detail?id=${productId}`\n\t\t\t})\n\t\t},\n\t\t\n\t\t// 去购物\n\t\tgoShopping() {\n\t\t\tuni.switchTab({\n\t\t\t\turl: '/pages/index/index'\n\t\t\t})\n\t\t}\n\t}\n}\n</script>\n\n<style scoped>\n.favorites-container {\n\tmin-height: 100vh;\n\tbackground-color: #f5f5f5;\n}\n\n/* 空状态样式 */\n.empty-state {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: center;\n\tpadding: 120rpx 40rpx;\n\ttext-align: center;\n}\n\n.empty-icon {\n\tfont-size: 120rpx;\n\tmargin-bottom: 40rpx;\n}\n\n.empty-text {\n\tfont-size: 32rpx;\n\tcolor: #333;\n\tmargin-bottom: 20rpx;\n\tfont-weight: 500;\n}\n\n.empty-desc {\n\tfont-size: 28rpx;\n\tcolor: #999;\n\tmargin-bottom: 60rpx;\n}\n\n.go-shopping-btn {\n\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n\tcolor: white;\n\tborder: none;\n\tborder-radius: 50rpx;\n\tpadding: 24rpx 60rpx;\n\tfont-size: 28rpx;\n}\n\n/* 收藏列表样式 */\n.favorites-list {\n\tpadding: 20rpx;\n}\n\n.favorite-item {\n\tdisplay: flex;\n\talign-items: center;\n\tbackground: white;\n\tborder-radius: 20rpx;\n\tpadding: 30rpx;\n\tmargin-bottom: 20rpx;\n\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\n}\n\n.product-image {\n\twidth: 120rpx;\n\theight: 120rpx;\n\tborder-radius: 16rpx;\n\toverflow: hidden;\n\tmargin-right: 30rpx;\n}\n\n.image {\n\twidth: 100%;\n\theight: 100%;\n}\n\n.product-info {\n\tflex: 1;\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n.product-name {\n\tfont-size: 30rpx;\n\tcolor: #333;\n\tfont-weight: 500;\n\tmargin-bottom: 10rpx;\n\toverflow: hidden;\n\ttext-overflow: ellipsis;\n\twhite-space: nowrap;\n}\n\n.product-desc {\n\tfont-size: 24rpx;\n\tcolor: #999;\n\tmargin-bottom: 20rpx;\n\toverflow: hidden;\n\ttext-overflow: ellipsis;\n\twhite-space: nowrap;\n}\n\n.product-price {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.price-text {\n\tfont-size: 28rpx;\n\tcolor: #e74c3c;\n\tfont-weight: 600;\n}\n\n.action-btn {\n\tpadding: 20rpx;\n\tmargin-left: 20rpx;\n}\n</style>\n", "import MiniProgramPage from 'D:/app/miniprogram/SIYU/pages/user/favorites.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AA2CA,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,eAAe,CAAC;AAAA,IACjB;AAAA,EACA;AAAA,EAED,SAAS;AACR,SAAK,cAAc;AAAA,EACnB;AAAA,EAED,SAAS;AACR,SAAK,cAAc;AAAA,EACnB;AAAA,EAED,SAAS;AAAA;AAAA,IAER,MAAM,gBAAgB;AACrB,UAAI;AAMH,aAAK,gBAAgB,CAAC;AAAA,MACrB,SAAO,OAAO;AACfA,sBAAAA,MAAA,MAAA,SAAA,kCAAc,WAAW,KAAK;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,SACN;AAAA,MACF;AAAA,IACA;AAAA;AAAA,IAGD,MAAM,eAAe,YAAY;AAChC,UAAI;AACHA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,SAAS;AAAA,UACT,SAAS,OAAO,QAAQ;AACvB,gBAAI,IAAI,SAAS;AAKhB,mBAAK,gBAAgB,KAAK,cAAc,OAAO,UAAQ,KAAK,OAAO,UAAU;AAE7EA,4BAAAA,MAAI,UAAU;AAAA,gBACb,OAAO;AAAA,gBACP,MAAM;AAAA,eACN;AAAA,YACF;AAAA,UACD;AAAA,SACA;AAAA,MACA,SAAO,OAAO;AACfA,sBAAAA,MAAA,MAAA,SAAA,mCAAc,WAAW,KAAK;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,SACN;AAAA,MACF;AAAA,IACA;AAAA;AAAA,IAGD,YAAY,WAAW;AACtBA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK,4BAA4B,SAAS;AAAA,OAC1C;AAAA,IACD;AAAA;AAAA,IAGD,aAAa;AACZA,oBAAAA,MAAI,UAAU;AAAA,QACb,KAAK;AAAA,OACL;AAAA,IACF;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACxHA,GAAG,WAAW,eAAe;"}