
.mall-container.data-v-43c06b56 {
	background: #f8f8f8;
	min-height: 100vh;
}
.search-bar.data-v-43c06b56 {
	background: #ffffff;
	padding: 10px 15px;
	border-bottom: 1px solid #f0f0f0;
}
.search-input.data-v-43c06b56 {
	display: flex;
	align-items: center;
	background: #f5f5f5;
	border-radius: 20px;
	padding: 8px 15px;
}
.search-input input.data-v-43c06b56 {
	flex: 1;
	margin-left: 10px;
	font-size: 14px;
	border: none;
	background: transparent;
}
.category-nav.data-v-43c06b56 {
	background: #ffffff;
	white-space: nowrap;
	border-bottom: 1px solid #f0f0f0;
}
.category-list.data-v-43c06b56 {
	display: flex;
	padding: 0 15px;
}
.category-item.data-v-43c06b56 {
	padding: 15px 20px;
	white-space: nowrap;
}
.category-item.active .category-name.data-v-43c06b56 {
	color: #667eea;
	font-weight: 500;
}
.category-name.data-v-43c06b56 {
	font-size: 14px;
	color: #333;
}
.product-list.data-v-43c06b56 {
	padding: 15px;
}
.product-item.data-v-43c06b56 {
	background: #ffffff;
	border-radius: 8px;
	margin-bottom: 15px;
	overflow: hidden;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
.product-image.data-v-43c06b56 {
	width: 100%;
	height: 200px;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	display: flex;
	align-items: center;
	justify-content: center;
}
.product-emoji.data-v-43c06b56 {
	font-size: 64px;
}
.product-img.data-v-43c06b56 {
	width: 100%;
	height: 100%;
	border-radius: 8px;
}
.product-info.data-v-43c06b56 {
	padding: 15px;
}
.product-name.data-v-43c06b56 {
	display: block;
	font-size: 16px;
	font-weight: 500;
	color: #333;
	margin-bottom: 5px;
}
.product-desc.data-v-43c06b56 {
	display: block;
	font-size: 12px;
	color: #999;
	margin-bottom: 10px;
}
.product-price.data-v-43c06b56 {
	display: flex;
	align-items: center;
	justify-content: space-between;
}
.points-price.data-v-43c06b56 {
	font-size: 18px;
	font-weight: bold;
	color: #667eea;
}
.market-price.data-v-43c06b56 {
	font-size: 12px;
	color: #999;
	text-decoration: line-through;
}
.load-more.data-v-43c06b56 {
	padding: 20px;
}
