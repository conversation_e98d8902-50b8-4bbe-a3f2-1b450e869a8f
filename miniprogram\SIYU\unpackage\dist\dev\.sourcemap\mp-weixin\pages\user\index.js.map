{"version": 3, "file": "index.js", "sources": ["pages/user/index.vue", "../../../Program Files/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvdXNlci9pbmRleC52dWU"], "sourcesContent": ["<template>\n\t<view class=\"user-container\">\n\t\t<!-- 用户信息卡片 -->\n\t\t<view class=\"user-card\">\n\t\t\t<view class=\"user-bg\">\n\t\t\t\t<view class=\"user-info\" @click=\"handleUserClick\">\n\t\t\t\t\t<view class=\"avatar\">\n\t\t\t\t\t\t<image v-if=\"userInfo.avatarUrl\" class=\"avatar-image\" :src=\"userInfo.avatarUrl\" mode=\"aspectFill\" @error=\"onImageError\" @load=\"onImageLoad\"></image>\n\t\t\t\t\t\t<text v-else class=\"avatar-emoji\">👤</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"user-details\">\n\t\t\t\t\t\t<text class=\"nickname\">{{ isLoggedIn ? userInfo.nickName : '登录/注册' }}</text>\n\t\t\t\t\t\t<text class=\"user-id\" v-if=\"isLoggedIn\">ID: {{ userInfo.id || '000000' }}</text>\n\t\t\t\t\t\t<text class=\"user-desc\" v-else>点击登录享受更多服务</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<text class=\"action-arrow\">›</text>\n\t\t\t\t</view>\n\n\t\t\t\t<view class=\"user-stats\">\n\t\t\t\t\t<view class=\"stat-item\" @click=\"goToPoints\">\n\t\t\t\t\t\t<text class=\"stat-number\">{{ userStats.points }}</text>\n\t\t\t\t\t\t<text class=\"stat-label\">积分</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"stat-item\" @click=\"goToOrders\">\n\t\t\t\t\t\t<text class=\"stat-number\">{{ userStats.orders }}</text>\n\t\t\t\t\t\t<text class=\"stat-label\">订单</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"stat-item\" @click=\"goToCoupons\">\n\t\t\t\t\t\t<text class=\"stat-number\">{{ userStats.coupons }}</text>\n\t\t\t\t\t\t<text class=\"stat-label\">优惠券</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 我的订单 -->\n\t\t<view class=\"section\">\n\t\t\t<view class=\"section-header\" @click=\"goToOrders\">\n\t\t\t\t<text class=\"section-title\">我的订单</text>\n\t\t\t\t<view class=\"section-more\">\n\t\t\t\t\t<text class=\"more-text\">查看全部</text>\n\t\t\t\t\t<uni-icons type=\"right\" size=\"14\" color=\"#999\"></uni-icons>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<view class=\"order-types\">\n\t\t\t\t<view class=\"order-type\" @click=\"goToOrders('pending')\">\n\t\t\t\t\t<svg-icon name=\"pending\" :size=\"24\" color=\"#f39c12\"></svg-icon>\n\t\t\t\t\t<text class=\"type-text\">待付款</text>\n\t\t\t\t\t<view class=\"badge\" v-if=\"orderCounts.pending > 0\">{{ orderCounts.pending }}</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"order-type\" @click=\"goToOrders('processing')\">\n\t\t\t\t\t<svg-icon name=\"processing\" :size=\"24\" color=\"#3498db\"></svg-icon>\n\t\t\t\t\t<text class=\"type-text\">处理中</text>\n\t\t\t\t\t<view class=\"badge\" v-if=\"orderCounts.processing > 0\">{{ orderCounts.processing }}</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"order-type\" @click=\"goToOrders('shipped')\">\n\t\t\t\t\t<svg-icon name=\"shipped\" :size=\"24\" color=\"#9b59b6\"></svg-icon>\n\t\t\t\t\t<text class=\"type-text\">已发货</text>\n\t\t\t\t\t<view class=\"badge\" v-if=\"orderCounts.shipped > 0\">{{ orderCounts.shipped }}</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"order-type\" @click=\"goToOrders('completed')\">\n\t\t\t\t\t<svg-icon name=\"completed\" :size=\"24\" color=\"#27ae60\"></svg-icon>\n\t\t\t\t\t<text class=\"type-text\">已完成</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 功能菜单 -->\n\t\t<view class=\"section\">\n\t\t\t<view class=\"menu-list\">\n\t\t\t\t<view class=\"menu-item\" @click=\"goToAddress\">\n\t\t\t\t\t<view class=\"menu-left\">\n\t\t\t\t\t\t<uni-icons type=\"location\" size=\"20\" color=\"#667eea\"></uni-icons>\n\t\t\t\t\t\t<text class=\"menu-text\">收货地址</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<uni-icons type=\"right\" size=\"14\" color=\"#999\"></uni-icons>\n\t\t\t\t</view>\n\n\t\t\t\t<view class=\"menu-item\" @click=\"goToFavorites\">\n\t\t\t\t\t<view class=\"menu-left\">\n\t\t\t\t\t\t<uni-icons type=\"heart\" size=\"20\" color=\"#e74c3c\"></uni-icons>\n\t\t\t\t\t\t<text class=\"menu-text\">我的收藏</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<uni-icons type=\"right\" size=\"14\" color=\"#999\"></uni-icons>\n\t\t\t\t</view>\n\n\t\t\t\t<view class=\"menu-item\" @click=\"goToHelp\">\n\t\t\t\t\t<view class=\"menu-left\">\n\t\t\t\t\t\t<uni-icons type=\"help\" size=\"20\" color=\"#f39c12\"></uni-icons>\n\t\t\t\t\t\t<text class=\"menu-text\">帮助中心</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<uni-icons type=\"right\" size=\"14\" color=\"#999\"></uni-icons>\n\t\t\t\t</view>\n\n\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 退出登录 -->\n\t\t<view class=\"section\" v-if=\"isLoggedIn\">\n\t\t\t<view class=\"logout-btn\" @click=\"handleLogout\">\n\t\t\t\t<text class=\"logout-text\">退出登录</text>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 头像昵称编辑弹窗 -->\n\t\t<view v-if=\"showProfileModal\" class=\"profile-modal-overlay\" @click=\"closeProfileModal\">\n\t\t\t<view class=\"profile-modal\" @click.stop>\n\t\t\t\t<view class=\"modal-header\">\n\t\t\t\t\t<text class=\"modal-title\">获取你的昵称、头像</text>\n\t\t\t\t\t<view class=\"modal-close\" @click=\"closeProfileModal\">\n\t\t\t\t\t\t<uni-icons type=\"close\" size=\"20\" color=\"#999\"></uni-icons>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<view class=\"modal-content\">\n\t\t\t\t\t<text class=\"modal-desc\">获取用户头像、昵称，主要用于向用户提供具有辨识度的用户中心页面。</text>\n\n\t\t\t\t\t<!-- 头像选择 -->\n\t\t\t\t\t<view class=\"avatar-section\">\n\t\t\t\t\t\t<text class=\"section-label\">头像</text>\n\t\t\t\t\t\t<text class=\"debug-info\" style=\"font-size: 12px; color: #999;\">支持新版头像: {{ canUseChooseAvatar ? '是' : '否' }}</text>\n\t\t\t\t\t\t<!-- 新版本微信支持的头像选择 -->\n\t\t\t\t\t\t<button\n\t\t\t\t\t\t\tv-if=\"canUseChooseAvatar\"\n\t\t\t\t\t\t\tclass=\"avatar-button\"\n\t\t\t\t\t\t\topen-type=\"chooseAvatar\"\n\t\t\t\t\t\t\t@chooseavatar=\"onChooseAvatar\"\n\t\t\t\t\t\t\t@tap=\"onAvatarButtonTap\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<image\n\t\t\t\t\t\t\t\t:src=\"tempAvatarUrl || '/static/images/default-avatar.png'\"\n\t\t\t\t\t\t\t\tclass=\"avatar-preview\"\n\t\t\t\t\t\t\t\tmode=\"aspectFill\"\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t<view class=\"avatar-arrow\">\n\t\t\t\t\t\t\t\t<uni-icons type=\"right\" size=\"14\" color=\"#ccc\"></uni-icons>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</button>\n\t\t\t\t\t\t<!-- 兼容旧版本的头像选择 -->\n\t\t\t\t\t\t<button\n\t\t\t\t\t\t\tv-else\n\t\t\t\t\t\t\tclass=\"avatar-button\"\n\t\t\t\t\t\t\t@click=\"chooseAvatarFallback\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<image\n\t\t\t\t\t\t\t\t:src=\"tempAvatarUrl || '/static/images/default-avatar.png'\"\n\t\t\t\t\t\t\t\tclass=\"avatar-preview\"\n\t\t\t\t\t\t\t\tmode=\"aspectFill\"\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t<view class=\"avatar-arrow\">\n\t\t\t\t\t\t\t\t<uni-icons type=\"right\" size=\"14\" color=\"#ccc\"></uni-icons>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</button>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<!-- 昵称输入 -->\n\t\t\t\t\t<view class=\"nickname-section\">\n\t\t\t\t\t\t<text class=\"section-label\">昵称</text>\n\t\t\t\t\t\t<input\n\t\t\t\t\t\t\tclass=\"nickname-input\"\n\t\t\t\t\t\t\ttype=\"nickname\"\n\t\t\t\t\t\t\tv-model=\"tempNickname\"\n\t\t\t\t\t\t\tplaceholder=\"请输入昵称\"\n\t\t\t\t\t\t\tmaxlength=\"20\"\n\t\t\t\t\t\t\t@blur=\"onBlurNickname\"\n\t\t\t\t\t\t/>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<view class=\"modal-footer\">\n\t\t\t\t\t<button class=\"save-button\" @click=\"saveProfile\">保存</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nimport { userAPI, authAPI } from '@/api/index.js'\nimport AuthUtils from '@/utils/auth.js'\nimport SvgIcon from '@/components/svg-icon/svg-icon.vue'\n\nexport default {\n\tcomponents: {\n\t\tSvgIcon\n\t},\n\tdata() {\n\t\treturn {\n\t\t\tuserInfo: {},\n\t\t\tuserStats: {\n\t\t\t\tpoints: 0,\n\t\t\t\torders: 0,\n\t\t\t\tcoupons: 0\n\t\t\t},\n\t\t\torderCounts: {\n\t\t\t\tpending: 0,\n\t\t\t\tprocessing: 0,\n\t\t\t\tshipped: 0,\n\t\t\t\tcompleted: 0\n\t\t\t},\n\t\t\tisLoggedIn: false,\n\t\t\t// 头像昵称编辑相关\n\t\t\tshowProfileModal: false,\n\t\t\ttempNickname: '',\n\t\t\ttempAvatarUrl: '',\n\t\t\tcanUseChooseAvatar: false, // 是否支持新版头像选择\n\t\t\tisLoadingStats: false // 是否正在加载统计数据\n\t\t}\n\t},\n\tasync onLoad() {\n\t\tconsole.log('用户中心页面 onLoad');\n\t\tthis.checkChooseAvatarSupport();\n\t\tthis.loadUserInfo();\n\t\tawait this.loadUserStats();\n\t},\n\tasync onShow() {\n\t\tconsole.log('用户中心页面 onShow');\n\t\t// 只在页面显示时更新登录状态，避免重复请求\n\t\tthis.loadUserInfo();\n\n\t\t// 如果用户信息为空或者登录状态改变，才重新加载\n\t\tif (!this.userInfo.id || this.isLoggedIn !== AuthUtils.isLoggedIn()) {\n\t\t\tawait this.loadUserStats();\n\t\t}\n\t},\n\tmethods: {\n\t\t// 加载用户信息\n\t\tloadUserInfo() {\n\t\t\tthis.isLoggedIn = AuthUtils.isLoggedIn()\n\t\t\t// 只在没有用户信息时从本地存储加载，避免覆盖服务器数据\n\t\t\tif (!this.userInfo.id) {\n\t\t\t\tthis.userInfo = AuthUtils.getCurrentUser() || {}\n\t\t\t}\n\t\t},\n\n\t\t// 加载用户统计数据\n\t\tasync loadUserStats() {\n\t\t\t// 防止重复加载\n\t\t\tif (this.isLoadingStats) {\n\t\t\t\tconsole.log('正在加载中，跳过重复请求');\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\ttry {\n\t\t\t\t// 检查是否已登录\n\t\t\t\tconst token = uni.getStorageSync('token')\n\t\t\t\tif (!token) {\n\t\t\t\t\tthis.userStats = {\n\t\t\t\t\t\tpoints: 0,\n\t\t\t\t\t\torders: 0,\n\t\t\t\t\t\tcoupons: 0\n\t\t\t\t\t}\n\t\t\t\t\tthis.orderCounts = {\n\t\t\t\t\t\tpending: 0,\n\t\t\t\t\t\tprocessing: 0,\n\t\t\t\t\t\tshipped: 0,\n\t\t\t\t\t\tcompleted: 0\n\t\t\t\t\t}\n\t\t\t\t\treturn\n\t\t\t\t}\n\n\t\t\t\tthis.isLoadingStats = true;\n\t\t\t\tconsole.log('开始加载用户统计数据');\n\n\t\t\t\t// 尝试获取用户详细信息\n\t\t\t\ttry {\n\t\t\t\t\tconst userInfoResult = await userAPI.getProfile()\n\t\t\t\t\tif (userInfoResult.data) {\n\t\t\t\t\t\tconst userData = userInfoResult.data\n\n\t\t\t\t\t\t// 转换数据格式以匹配模板期望的字段名\n\t\t\t\t\t\tthis.userInfo = {\n\t\t\t\t\t\t\t...userData,\n\t\t\t\t\t\t\tnickName: userData.nickname,  // 转换字段名\n\t\t\t\t\t\t\tavatarUrl: userData.avatar    // 转换字段名\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tuni.setStorageSync('userInfo', this.userInfo)\n\n\t\t\t\t\t\tthis.userStats = {\n\t\t\t\t\t\t\tpoints: userData.available_points || 0,\n\t\t\t\t\t\t\torders: userData.stats?.total_orders || 0,\n\t\t\t\t\t\t\tcoupons: 0\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tconsole.log('用户信息加载成功');\n\t\t\t\t\t}\n\t\t\t\t} catch (userInfoError) {\n\t\t\t\t\tconsole.log('获取用户信息失败，尝试统计接口')\n\t\t\t\t}\n\n\t\t\t\t// 尝试获取统计数据\n\t\t\t\ttry {\n\t\t\t\t\tconst response = await userAPI.getStats()\n\t\t\t\t\tthis.userStats = response.data.stats\n\t\t\t\t\tthis.orderCounts = response.data.orderCounts\n\t\t\t\t} catch (statsError) {\n\t\t\t\t\tconsole.log('统计接口不存在，使用默认数据')\n\t\t\t\t\t// 如果统计接口不存在，使用用户信息中的数据\n\t\t\t\t\tconst userInfo = this.userInfo || uni.getStorageSync('userInfo')\n\t\t\t\t\tif (userInfo) {\n\t\t\t\t\t\tthis.userStats.points = userInfo.available_points || 0\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('获取用户统计数据失败:', error)\n\t\t\t\t// 使用模拟数据作为降级方案\n\t\t\t\tthis.userStats = {\n\t\t\t\t\tpoints: 1580,\n\t\t\t\t\torders: 12,\n\t\t\t\t\tcoupons: 3\n\t\t\t\t}\n\n\t\t\t\tthis.orderCounts = {\n\t\t\t\t\tpending: 1,\n\t\t\t\t\tprocessing: 2,\n\t\t\t\t\tshipped: 1,\n\t\t\t\t\tcompleted: 8\n\t\t\t\t}\n\t\t\t} finally {\n\t\t\t\tthis.isLoadingStats = false;\n\t\t\t\tconsole.log('用户统计数据加载完成');\n\t\t\t}\n\t\t},\n\n\t\t// 处理用户信息点击\n\t\thandleUserClick() {\n\t\t\tif (!this.isLoggedIn) {\n\t\t\t\tthis.goToLogin();\n\t\t\t} else {\n\t\t\t\t// 已登录用户显示头像昵称编辑弹窗\n\t\t\t\tthis.showProfileModal = true;\n\t\t\t\tthis.tempNickname = this.userInfo.nickName || '';\n\t\t\t\tthis.tempAvatarUrl = this.userInfo.avatarUrl || '';\n\t\t\t}\n\t\t},\n\n\t\t// 跳转到积分页面\n\t\tgoToPoints() {\n\t\t\tuni.switchTab({\n\t\t\t\turl: '/pages/points/index'\n\t\t\t});\n\t\t},\n\n\t\t// 跳转到订单页面\n\t\tgoToOrders(status = '') {\n\t\t\tconst url = status ? `/pages/user/orders?status=${status}` : '/pages/user/orders';\n\t\t\tuni.navigateTo({\n\t\t\t\turl: url\n\t\t\t});\n\t\t},\n\n\t\t// 跳转到优惠券\n\t\tgoToCoupons() {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: '/pages/user/coupons'\n\t\t\t});\n\t\t},\n\n\t\t// 跳转到收货地址\n\t\tgoToAddress() {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: '/pages/address/list'\n\t\t\t});\n\t\t},\n\n\t\t// 跳转到收藏\n\t\tgoToFavorites() {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: '/pages/user/favorites'\n\t\t\t});\n\t\t},\n\n\t\t// 跳转到帮助中心\n\t\tgoToHelp() {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: '/pages/user/help'\n\t\t\t});\n\t\t},\n\n\n\n\t\t// 跳转到登录\n\t\tgoToLogin() {\n\t\t\tAuthUtils.navigateToLogin()\n\t\t},\n\n\t\t// 退出登录\n\t\tasync handleLogout() {\n\t\t\tuni.showModal({\n\t\t\t\ttitle: '提示',\n\t\t\t\tcontent: '确定要退出登录吗？',\n\t\t\t\tsuccess: async (res) => {\n\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t// 使用AuthUtils退出登录\n\t\t\t\t\t\t\tawait AuthUtils.logout()\n\n\t\t\t\t\t\t\t// 重置页面数据\n\t\t\t\t\t\t\tthis.userInfo = {}\n\t\t\t\t\t\t\tthis.isLoggedIn = false\n\t\t\t\t\t\t\tthis.userStats = {\n\t\t\t\t\t\t\t\tpoints: 0,\n\t\t\t\t\t\t\t\torders: 0,\n\t\t\t\t\t\t\t\tcoupons: 0\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tthis.orderCounts = {\n\t\t\t\t\t\t\t\tpending: 0,\n\t\t\t\t\t\t\t\tprocessing: 0,\n\t\t\t\t\t\t\t\tshipped: 0,\n\t\t\t\t\t\t\t\tcompleted: 0\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\t\tconsole.error('退出登录失败:', error)\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '退出失败，请重试',\n\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t})\n\t\t},\n\n\t\t// 关闭头像昵称编辑弹窗\n\t\tcloseProfileModal() {\n\t\t\tthis.showProfileModal = false;\n\t\t\tthis.tempNickname = '';\n\t\t\tthis.tempAvatarUrl = '';\n\t\t},\n\n\t\t// 头像按钮点击事件\n\t\tonAvatarButtonTap() {\n\t\t\tconsole.log('头像按钮被点击');\n\t\t\tconsole.log('当前支持新版头像选择:', this.canUseChooseAvatar);\n\t\t},\n\n\t\t// 选择头像回调\n\t\tonChooseAvatar(e) {\n\t\t\tconsole.log('=== 选择头像事件触发 ===');\n\t\t\tconsole.log('完整事件对象:', e);\n\t\t\tconsole.log('事件详情:', e.detail);\n\t\t\tconsole.log('头像URL:', e.detail?.avatarUrl);\n\n\t\t\tif (e.detail && e.detail.avatarUrl) {\n\t\t\t\tconsole.log('✅ 获取到头像URL:', e.detail.avatarUrl);\n\t\t\t\tthis.tempAvatarUrl = e.detail.avatarUrl;\n\n\t\t\t\t// 强制更新视图\n\t\t\t\tthis.$forceUpdate();\n\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '头像选择成功',\n\t\t\t\t\ticon: 'success'\n\t\t\t\t});\n\t\t\t} else {\n\t\t\t\tconsole.log('❌ 未获取到头像URL');\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '头像选择失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\n\t\t// 昵称输入失焦回调\n\t\tonBlurNickname(e) {\n\t\t\tconsole.log('昵称输入失焦:', e.detail.value);\n\t\t\t// 这里可以添加昵称验证逻辑\n\t\t\tif (e.detail.value && e.detail.value.trim()) {\n\t\t\t\tthis.tempNickname = e.detail.value.trim();\n\t\t\t}\n\t\t},\n\n\t\t// 保存头像昵称\n\t\tasync saveProfile() {\n\t\t\tif (!this.tempNickname.trim()) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请输入昵称',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\ttry {\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: '保存中...'\n\t\t\t\t});\n\n\t\t\t\t// 调用API更新用户信息\n\t\t\t\tconst response = await userAPI.updateProfile({\n\t\t\t\t\tnickname: this.tempNickname.trim(),\n\t\t\t\t\tavatar: this.tempAvatarUrl\n\t\t\t\t});\n\n\t\t\t\tif (response.code === 200) {\n\t\t\t\t\t// 更新本地用户信息（字段映射）\n\t\t\t\t\tthis.userInfo.nickName = this.tempNickname.trim();\n\t\t\t\t\tthis.userInfo.avatarUrl = this.tempAvatarUrl;\n\t\t\t\t\tthis.userInfo.nickname = this.tempNickname.trim(); // 后端字段\n\t\t\t\t\tthis.userInfo.avatar = this.tempAvatarUrl; // 后端字段\n\n\t\t\t\t\t// 更新本地存储\n\t\t\t\t\tuni.setStorageSync('userInfo', this.userInfo);\n\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '保存成功',\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t});\n\n\t\t\t\t\tthis.closeProfileModal();\n\n\t\t\t\t\t// 重新加载用户信息以确保数据同步\n\t\t\t\t\tthis.loadUserStats();\n\t\t\t\t} else {\n\t\t\t\t\tthrow new Error(response.message || '保存失败');\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tuni.hideLoading();\n\t\t\t\tconsole.error('保存头像昵称失败:', error);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: error.message || '保存失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\n\t\t// 图片加载成功\n\t\tonImageLoad(e) {\n\t\t\tconsole.log('头像加载成功:', e);\n\t\t},\n\n\t\t// 图片加载失败\n\t\tonImageError(e) {\n\t\t\tconsole.log('头像加载失败:', e);\n\t\t},\n\n\t\t// 检查是否支持新版头像选择\n\t\tcheckChooseAvatarSupport() {\n\t\t\ttry {\n\t\t\t\tconst systemInfo = uni.getSystemInfoSync();\n\t\t\t\tconsole.log('=== 系统信息检查 ===');\n\t\t\t\tconsole.log('完整系统信息:', systemInfo);\n\t\t\t\tconsole.log('基础库版本:', systemInfo.SDKVersion);\n\t\t\t\tconsole.log('微信版本:', systemInfo.version);\n\t\t\t\tconsole.log('平台:', systemInfo.platform);\n\n\t\t\t\t// 检查基础库版本是否支持 chooseAvatar\n\t\t\t\tconst SDKVersion = systemInfo.SDKVersion || '0.0.0';\n\t\t\t\tconst version = SDKVersion.split('.').map(v => parseInt(v) || 0);\n\n\t\t\t\tconsole.log('版本数组:', version);\n\n\t\t\t\t// 需要基础库版本 2.21.2 或以上\n\t\t\t\tlet isSupported = false;\n\t\t\t\tif (version[0] > 2) {\n\t\t\t\t\tisSupported = true;\n\t\t\t\t} else if (version[0] === 2) {\n\t\t\t\t\tif (version[1] > 21) {\n\t\t\t\t\t\tisSupported = true;\n\t\t\t\t\t} else if (version[1] === 21 && version[2] >= 2) {\n\t\t\t\t\t\tisSupported = true;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tthis.canUseChooseAvatar = isSupported;\n\n\t\t\t\tif (isSupported) {\n\t\t\t\t\tconsole.log('✅ 支持新版头像选择');\n\t\t\t\t} else {\n\t\t\t\t\tconsole.log('❌ 不支持新版头像选择，当前版本:', SDKVersion, '需要版本: 2.21.2+');\n\t\t\t\t}\n\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('检查头像选择支持时出错:', error);\n\t\t\t\tthis.canUseChooseAvatar = false;\n\t\t\t}\n\t\t},\n\n\t\t// 兼容旧版本的头像选择\n\t\tchooseAvatarFallback() {\n\t\t\tuni.showModal({\n\t\t\t\ttitle: '提示',\n\t\t\t\tcontent: '当前微信版本不支持头像选择功能，请升级微信到最新版本',\n\t\t\t\tshowCancel: false\n\t\t\t});\n\t\t}\n\t}\n}\n</script>\n\n<style scoped>\n.user-container {\n\tbackground: #f8f8f8;\n\tmin-height: 100vh;\n}\n\n.user-card {\n\tmargin-bottom: 15px;\n}\n\n.user-bg {\n\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n\tpadding: 25px 20px;\n\tcolor: #ffffff;\n}\n\n.user-info {\n\tdisplay: flex;\n\talign-items: center;\n\tmargin-bottom: 25px;\n}\n\n.avatar {\n\twidth: 60px;\n\theight: 60px;\n\tborder-radius: 30px;\n\tmargin-right: 15px;\n\tbackground: rgba(255, 255, 255, 0.2);\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.avatar-image {\n\twidth: 100%;\n\theight: 100%;\n\tborder-radius: 30px;\n}\n\n.avatar-emoji {\n\tfont-size: 32px;\n}\n\n.action-arrow {\n\tfont-size: 20px;\n\tcolor: rgba(255, 255, 255, 0.8);\n}\n\n.user-details {\n\tflex: 1;\n}\n\n.nickname {\n\tdisplay: block;\n\tfont-size: 18px;\n\tfont-weight: 500;\n\tmargin-bottom: 5px;\n}\n\n.user-id {\n\tdisplay: block;\n\tfont-size: 12px;\n\topacity: 0.8;\n}\n\n.user-desc {\n\tdisplay: block;\n\tfont-size: 12px;\n\topacity: 0.8;\n\tcolor: rgba(255, 255, 255, 0.7);\n}\n\n.user-stats {\n\tdisplay: flex;\n\tjustify-content: space-around;\n}\n\n.stat-item {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n}\n\n.stat-number {\n\tfont-size: 20px;\n\tfont-weight: bold;\n\tmargin-bottom: 5px;\n}\n\n.stat-label {\n\tfont-size: 12px;\n\topacity: 0.8;\n}\n\n.section {\n\tbackground: #ffffff;\n\tmargin-bottom: 15px;\n}\n\n.section-header {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tpadding: 15px 20px;\n\tborder-bottom: 1px solid #f5f5f5;\n}\n\n.section-title {\n\tfont-size: 16px;\n\tfont-weight: 500;\n\tcolor: #333;\n}\n\n.section-more {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.more-text {\n\tfont-size: 12px;\n\tcolor: #999;\n\tmargin-right: 5px;\n}\n\n.order-types {\n\tdisplay: flex;\n\tjustify-content: space-around;\n\tpadding: 20px;\n}\n\n.order-type {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tposition: relative;\n}\n\n.type-text {\n\tfont-size: 12px;\n\tcolor: #333;\n\tmargin-top: 8px;\n}\n\n.badge {\n\tposition: absolute;\n\ttop: -5px;\n\tright: -10px;\n\tbackground: #f56c6c;\n\tcolor: #ffffff;\n\tfont-size: 10px;\n\tpadding: 2px 6px;\n\tborder-radius: 10px;\n\tmin-width: 16px;\n\ttext-align: center;\n}\n\n.menu-list {\n\t/* 菜单列表样式 */\n}\n\n.menu-item {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tpadding: 15px 20px;\n\tborder-bottom: 1px solid #f5f5f5;\n}\n\n.menu-item:last-child {\n\tborder-bottom: none;\n}\n\n.menu-left {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.menu-text {\n\tfont-size: 14px;\n\tcolor: #333;\n\tmargin-left: 12px;\n}\n\n.logout-btn {\n\tmargin: 20px;\n\tbackground: #f56c6c;\n\tborder-radius: 8px;\n\tpadding: 15px;\n\ttext-align: center;\n}\n\n.logout-text {\n\tcolor: #ffffff;\n\tfont-size: 16px;\n\tfont-weight: 500;\n}\n\n/* 头像昵称编辑弹窗样式 */\n.profile-modal-overlay {\n\tposition: fixed;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tbottom: 0;\n\tbackground: rgba(0, 0, 0, 0.5);\n\tz-index: 1000;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tpadding: 40rpx;\n}\n\n.profile-modal {\n\tbackground: white;\n\tborder-radius: 20rpx;\n\twidth: 100%;\n\tmax-width: 600rpx;\n\tmax-height: 80vh;\n\toverflow: hidden;\n}\n\n.modal-header {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tpadding: 40rpx 40rpx 20rpx;\n\tborder-bottom: 1rpx solid #f0f0f0;\n}\n\n.modal-title {\n\tfont-size: 32rpx;\n\tfont-weight: 600;\n\tcolor: #333;\n}\n\n.modal-close {\n\twidth: 60rpx;\n\theight: 60rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.modal-content {\n\tpadding: 40rpx;\n}\n\n.modal-desc {\n\tfont-size: 28rpx;\n\tcolor: #666;\n\tline-height: 1.6;\n\tmargin-bottom: 40rpx;\n}\n\n.avatar-section,\n.nickname-section {\n\tmargin-bottom: 40rpx;\n}\n\n.section-label {\n\tdisplay: block;\n\tfont-size: 28rpx;\n\tcolor: #333;\n\tmargin-bottom: 20rpx;\n}\n\n.avatar-button {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tbackground: #f8f9fa;\n\tborder: none;\n\tborder-radius: 16rpx;\n\tpadding: 20rpx;\n\twidth: 100%;\n}\n\n.avatar-preview {\n\twidth: 80rpx;\n\theight: 80rpx;\n\tborder-radius: 50%;\n\tbackground: #ddd;\n}\n\n.nickname-input {\n\twidth: 100%;\n\theight: 80rpx;\n\tbackground: #f8f9fa;\n\tborder: none;\n\tborder-radius: 16rpx;\n\tpadding: 0 30rpx;\n\tfont-size: 28rpx;\n\tcolor: #333;\n}\n\n.modal-footer {\n\tpadding: 20rpx 40rpx 40rpx;\n}\n\n.save-button {\n\twidth: 100%;\n\theight: 80rpx;\n\tbackground: #007aff;\n\tcolor: white;\n\tborder: none;\n\tborder-radius: 40rpx;\n\tfont-size: 32rpx;\n\tfont-weight: 500;\n}\n</style>\n", "import MiniProgramPage from 'D:/app/miniprogram/SIYU/pages/user/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "userAPI"], "mappings": ";;;;AAsLA,gBAAgB,MAAW;AAE3B,MAAK,YAAU;AAAA,EACd,YAAY;AAAA,IACX;AAAA,EACA;AAAA,EACD,OAAO;AACN,WAAO;AAAA,MACN,UAAU,CAAE;AAAA,MACZ,WAAW;AAAA,QACV,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,SAAS;AAAA,MACT;AAAA,MACD,aAAa;AAAA,QACZ,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,SAAS;AAAA,QACT,WAAW;AAAA,MACX;AAAA,MACD,YAAY;AAAA;AAAA,MAEZ,kBAAkB;AAAA,MAClB,cAAc;AAAA,MACd,eAAe;AAAA,MACf,oBAAoB;AAAA;AAAA,MACpB,gBAAgB;AAAA;AAAA,IACjB;AAAA,EACA;AAAA,EACD,MAAM,SAAS;AACdA,kBAAAA,MAAY,MAAA,OAAA,+BAAA,eAAe;AAC3B,SAAK,yBAAwB;AAC7B,SAAK,aAAY;AACjB,UAAM,KAAK;EACX;AAAA,EACD,MAAM,SAAS;AACdA,kBAAAA,MAAY,MAAA,OAAA,+BAAA,eAAe;AAE3B,SAAK,aAAY;AAGjB,QAAI,CAAC,KAAK,SAAS,MAAM,KAAK,eAAeC,WAAAA,UAAU,cAAc;AACpE,YAAM,KAAK;IACZ;AAAA,EACA;AAAA,EACD,SAAS;AAAA;AAAA,IAER,eAAe;AACd,WAAK,aAAaA,WAAS,UAAC,WAAW;AAEvC,UAAI,CAAC,KAAK,SAAS,IAAI;AACtB,aAAK,WAAWA,qBAAU,eAAiB,KAAG,CAAC;AAAA,MAChD;AAAA,IACA;AAAA;AAAA,IAGD,MAAM,gBAAgB;;AAErB,UAAI,KAAK,gBAAgB;AACxBD,sBAAAA,kDAAY,cAAc;AAC1B;AAAA,MACD;AAEA,UAAI;AAEH,cAAM,QAAQA,cAAAA,MAAI,eAAe,OAAO;AACxC,YAAI,CAAC,OAAO;AACX,eAAK,YAAY;AAAA,YAChB,QAAQ;AAAA,YACR,QAAQ;AAAA,YACR,SAAS;AAAA,UACV;AACA,eAAK,cAAc;AAAA,YAClB,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,SAAS;AAAA,YACT,WAAW;AAAA,UACZ;AACA;AAAA,QACD;AAEA,aAAK,iBAAiB;AACtBA,sBAAAA,MAAY,MAAA,OAAA,+BAAA,YAAY;AAGxB,YAAI;AACH,gBAAM,iBAAiB,MAAME,UAAO,QAAC,WAAW;AAChD,cAAI,eAAe,MAAM;AACxB,kBAAM,WAAW,eAAe;AAGhC,iBAAK,WAAW;AAAA,cACf,GAAG;AAAA,cACH,UAAU,SAAS;AAAA;AAAA,cACnB,WAAW,SAAS;AAAA;AAAA,YACrB;AAEAF,0BAAAA,MAAI,eAAe,YAAY,KAAK,QAAQ;AAE5C,iBAAK,YAAY;AAAA,cAChB,QAAQ,SAAS,oBAAoB;AAAA,cACrC,UAAQ,cAAS,UAAT,mBAAgB,iBAAgB;AAAA,cACxC,SAAS;AAAA,YACV;AAEAA,0BAAAA,kDAAY,UAAU;AAAA,UACvB;AAAA,QACD,SAAS,eAAe;AACvBA,wBAAAA,kDAAY,iBAAiB;AAAA,QAC9B;AAGA,YAAI;AACH,gBAAM,WAAW,MAAME,UAAO,QAAC,SAAS;AACxC,eAAK,YAAY,SAAS,KAAK;AAC/B,eAAK,cAAc,SAAS,KAAK;AAAA,QAClC,SAAS,YAAY;AACpBF,wBAAAA,kDAAY,gBAAgB;AAE5B,gBAAM,WAAW,KAAK,YAAYA,cAAAA,MAAI,eAAe,UAAU;AAC/D,cAAI,UAAU;AACb,iBAAK,UAAU,SAAS,SAAS,oBAAoB;AAAA,UACtD;AAAA,QACD;AAAA,MACC,SAAO,OAAO;AACfA,sBAAAA,MAAc,MAAA,SAAA,+BAAA,eAAe,KAAK;AAElC,aAAK,YAAY;AAAA,UAChB,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,SAAS;AAAA,QACV;AAEA,aAAK,cAAc;AAAA,UAClB,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,SAAS;AAAA,UACT,WAAW;AAAA,QACZ;AAAA,MACD,UAAU;AACT,aAAK,iBAAiB;AACtBA,sBAAAA,MAAY,MAAA,OAAA,+BAAA,YAAY;AAAA,MACzB;AAAA,IACA;AAAA;AAAA,IAGD,kBAAkB;AACjB,UAAI,CAAC,KAAK,YAAY;AACrB,aAAK,UAAS;AAAA,aACR;AAEN,aAAK,mBAAmB;AACxB,aAAK,eAAe,KAAK,SAAS,YAAY;AAC9C,aAAK,gBAAgB,KAAK,SAAS,aAAa;AAAA,MACjD;AAAA,IACA;AAAA;AAAA,IAGD,aAAa;AACZA,oBAAAA,MAAI,UAAU;AAAA,QACb,KAAK;AAAA,MACN,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,WAAW,SAAS,IAAI;AACvB,YAAM,MAAM,SAAS,6BAA6B,MAAM,KAAK;AAC7DA,oBAAAA,MAAI,WAAW;AAAA,QACd;AAAA,MACD,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,cAAc;AACbA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACN,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,cAAc;AACbA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACN,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,gBAAgB;AACfA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACN,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,WAAW;AACVA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACN,CAAC;AAAA,IACD;AAAA;AAAA,IAKD,YAAY;AACXC,iBAAAA,UAAU,gBAAgB;AAAA,IAC1B;AAAA;AAAA,IAGD,MAAM,eAAe;AACpBD,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,OAAO,QAAQ;AACvB,cAAI,IAAI,SAAS;AAChB,gBAAI;AAEH,oBAAMC,WAAAA,UAAU,OAAO;AAGvB,mBAAK,WAAW,CAAC;AACjB,mBAAK,aAAa;AAClB,mBAAK,YAAY;AAAA,gBAChB,QAAQ;AAAA,gBACR,QAAQ;AAAA,gBACR,SAAS;AAAA,cACV;AACA,mBAAK,cAAc;AAAA,gBAClB,SAAS;AAAA,gBACT,YAAY;AAAA,gBACZ,SAAS;AAAA,gBACT,WAAW;AAAA,cACZ;AAAA,YACC,SAAO,OAAO;AACfD,4BAAAA,MAAA,MAAA,SAAA,+BAAc,WAAW,KAAK;AAC9BA,4BAAAA,MAAI,UAAU;AAAA,gBACb,OAAO;AAAA,gBACP,MAAM;AAAA,eACN;AAAA,YACF;AAAA,UACD;AAAA,QACD;AAAA,OACA;AAAA,IACD;AAAA;AAAA,IAGD,oBAAoB;AACnB,WAAK,mBAAmB;AACxB,WAAK,eAAe;AACpB,WAAK,gBAAgB;AAAA,IACrB;AAAA;AAAA,IAGD,oBAAoB;AACnBA,oBAAAA,MAAY,MAAA,OAAA,+BAAA,SAAS;AACrBA,oBAAA,MAAA,MAAA,OAAA,+BAAY,eAAe,KAAK,kBAAkB;AAAA,IAClD;AAAA;AAAA,IAGD,eAAe,GAAG;;AACjBA,oBAAAA,MAAY,MAAA,OAAA,+BAAA,kBAAkB;AAC9BA,oBAAA,MAAA,MAAA,OAAA,+BAAY,WAAW,CAAC;AACxBA,oBAAY,MAAA,MAAA,OAAA,+BAAA,SAAS,EAAE,MAAM;AAC7BA,0BAAY,MAAA,OAAA,+BAAA,WAAU,OAAE,WAAF,mBAAU,SAAS;AAEzC,UAAI,EAAE,UAAU,EAAE,OAAO,WAAW;AACnCA,4BAAY,MAAA,OAAA,+BAAA,eAAe,EAAE,OAAO,SAAS;AAC7C,aAAK,gBAAgB,EAAE,OAAO;AAG9B,aAAK,aAAY;AAEjBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,aACK;AACNA,sBAAAA,kDAAY,aAAa;AACzBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,MACF;AAAA,IACA;AAAA;AAAA,IAGD,eAAe,GAAG;AACjBA,0BAAY,MAAA,OAAA,+BAAA,WAAW,EAAE,OAAO,KAAK;AAErC,UAAI,EAAE,OAAO,SAAS,EAAE,OAAO,MAAM,QAAQ;AAC5C,aAAK,eAAe,EAAE,OAAO,MAAM,KAAI;AAAA,MACxC;AAAA,IACA;AAAA;AAAA,IAGD,MAAM,cAAc;AACnB,UAAI,CAAC,KAAK,aAAa,QAAQ;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AACD;AAAA,MACD;AAEA,UAAI;AACHA,sBAAAA,MAAI,YAAY;AAAA,UACf,OAAO;AAAA,QACR,CAAC;AAGD,cAAM,WAAW,MAAME,UAAO,QAAC,cAAc;AAAA,UAC5C,UAAU,KAAK,aAAa,KAAM;AAAA,UAClC,QAAQ,KAAK;AAAA,QACd,CAAC;AAED,YAAI,SAAS,SAAS,KAAK;AAE1B,eAAK,SAAS,WAAW,KAAK,aAAa,KAAI;AAC/C,eAAK,SAAS,YAAY,KAAK;AAC/B,eAAK,SAAS,WAAW,KAAK,aAAa,KAAI;AAC/C,eAAK,SAAS,SAAS,KAAK;AAG5BF,wBAAAA,MAAI,eAAe,YAAY,KAAK,QAAQ;AAE5CA,wBAAG,MAAC,YAAW;AACfA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACP,CAAC;AAED,eAAK,kBAAiB;AAGtB,eAAK,cAAa;AAAA,eACZ;AACN,gBAAM,IAAI,MAAM,SAAS,WAAW,MAAM;AAAA,QAC3C;AAAA,MACC,SAAO,OAAO;AACfA,sBAAG,MAAC,YAAW;AACfA,sBAAA,MAAA,MAAA,SAAA,+BAAc,aAAa,KAAK;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO,MAAM,WAAW;AAAA,UACxB,MAAM;AAAA,QACP,CAAC;AAAA,MACF;AAAA,IACA;AAAA;AAAA,IAGD,YAAY,GAAG;AACdA,oBAAA,MAAA,MAAA,OAAA,+BAAY,WAAW,CAAC;AAAA,IACxB;AAAA;AAAA,IAGD,aAAa,GAAG;AACfA,oBAAA,MAAA,MAAA,OAAA,+BAAY,WAAW,CAAC;AAAA,IACxB;AAAA;AAAA,IAGD,2BAA2B;AAC1B,UAAI;AACH,cAAM,aAAaA,oBAAI;AACvBA,sBAAAA,MAAA,MAAA,OAAA,+BAAY,gBAAgB;AAC5BA,sBAAY,MAAA,MAAA,OAAA,+BAAA,WAAW,UAAU;AACjCA,sBAAY,MAAA,MAAA,OAAA,+BAAA,UAAU,WAAW,UAAU;AAC3CA,sBAAA,MAAA,MAAA,OAAA,+BAAY,SAAS,WAAW,OAAO;AACvCA,sBAAA,MAAA,MAAA,OAAA,+BAAY,OAAO,WAAW,QAAQ;AAGtC,cAAM,aAAa,WAAW,cAAc;AAC5C,cAAM,UAAU,WAAW,MAAM,GAAG,EAAE,IAAI,OAAK,SAAS,CAAC,KAAK,CAAC;AAE/DA,sBAAA,MAAA,MAAA,OAAA,+BAAY,SAAS,OAAO;AAG5B,YAAI,cAAc;AAClB,YAAI,QAAQ,CAAC,IAAI,GAAG;AACnB,wBAAc;AAAA,QACb,WAAS,QAAQ,CAAC,MAAM,GAAG;AAC5B,cAAI,QAAQ,CAAC,IAAI,IAAI;AACpB,0BAAc;AAAA,UACf,WAAW,QAAQ,CAAC,MAAM,MAAM,QAAQ,CAAC,KAAK,GAAG;AAChD,0BAAc;AAAA,UACf;AAAA,QACD;AAEA,aAAK,qBAAqB;AAE1B,YAAI,aAAa;AAChBA,wBAAAA,MAAY,MAAA,OAAA,+BAAA,YAAY;AAAA,eAClB;AACNA,wBAAA,MAAA,MAAA,OAAA,+BAAY,qBAAqB,YAAY,eAAe;AAAA,QAC7D;AAAA,MAEC,SAAO,OAAO;AACfA,sBAAc,MAAA,MAAA,SAAA,+BAAA,gBAAgB,KAAK;AACnC,aAAK,qBAAqB;AAAA,MAC3B;AAAA,IACA;AAAA;AAAA,IAGD,uBAAuB;AACtBA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,MACb,CAAC;AAAA,IACF;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7kBA,GAAG,WAAW,eAAe;"}