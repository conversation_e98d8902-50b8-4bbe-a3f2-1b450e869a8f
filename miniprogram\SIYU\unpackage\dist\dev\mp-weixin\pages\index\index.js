"use strict";
const common_vendor = require("../../common/vendor.js");
const api_index = require("../../api/index.js");
const utils_request = require("../../utils/request.js");
const _sfc_main = {
  data() {
    return {
      banners: [],
      recommendProducts: [],
      activities: {}
    };
  },
  onLoad() {
    this.loadActivities();
    this.loadBanners();
    this.loadRecommendProducts();
  },
  methods: {
    // 加载活动配置
    async loadActivities() {
      try {
        const result = await api_index.systemAPI.getActivities();
        this.activities = result.data || {};
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/index.vue:151", "获取活动配置失败:", error);
        this.activities = {
          daily_sign: { enabled: true },
          lucky_wheel: { enabled: false }
        };
      }
    },
    // 加载轮播图
    async loadBanners() {
      try {
        const response = await api_index.systemAPI.getBanners();
        this.banners = response.data || [];
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/index.vue:166", "获取轮播图失败:", error);
        this.banners = [
          {
            id: 1,
            emoji: "🛒",
            title: "积分商城上线",
            description: "精品好物等你兑换",
            url: "/pages/mall/index"
          },
          {
            id: 2,
            emoji: "📅",
            title: "每日签到送积分",
            description: "坚持签到获得更多奖励",
            url: "/pages/activities/sign"
          },
          {
            id: 3,
            emoji: "🎰",
            title: "幸运转盘抽大奖",
            description: "每天3次免费抽奖机会",
            url: "/pages/activities/wheel"
          }
        ];
      }
    },
    // 加载推荐商品
    async loadRecommendProducts() {
      try {
        const response = await api_index.productAPI.getList({
          limit: 4,
          status: 1,
          sort: "sales"
          // 按销量排序
        });
        const products = response.data.list || [];
        products.forEach((product) => {
          if (product.image) {
            product.image = utils_request.getImageUrl(product.image);
          }
          if (product.images && Array.isArray(product.images)) {
            product.images = product.images.map((img) => utils_request.getImageUrl(img));
          }
        });
        this.recommendProducts = products;
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/index.vue:216", "获取推荐商品失败:", error);
        this.recommendProducts = [
          {
            id: 1,
            name: "精美水杯",
            emoji: "☕",
            points_price: 200
          },
          {
            id: 2,
            name: "蓝牙耳机",
            emoji: "🎧",
            points_price: 800
          },
          {
            id: 3,
            name: "充电宝",
            emoji: "🔋",
            points_price: 500
          },
          {
            id: 4,
            name: "手机支架",
            emoji: "📱",
            points_price: 150
          }
        ];
      }
    },
    // 处理轮播图点击
    handleBannerClick(banner) {
      if (banner.url) {
        common_vendor.index.navigateTo({
          url: banner.url
        });
      }
    },
    // 跳转到赚积分页面
    goToEarn() {
      common_vendor.index.navigateTo({
        url: "/pages/points/earn"
      });
    },
    // 跳转到签到
    goToSign() {
      common_vendor.index.navigateTo({
        url: "/pages/activities/sign"
      });
    },
    // 跳转到转盘
    goToWheel() {
      common_vendor.index.navigateTo({
        url: "/pages/activities/wheel"
      });
    },
    // 跳转到积分页面
    goToPoints() {
      common_vendor.index.switchTab({
        url: "/pages/points/index"
      });
    },
    // 跳转到商城
    goToMall() {
      common_vendor.index.switchTab({
        url: "/pages/mall/index"
      });
    },
    // 跳转到商品详情
    goToDetail(productId) {
      common_vendor.index.navigateTo({
        url: `/pages/mall/detail?id=${productId}`
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  var _a, _b;
  return common_vendor.e({
    a: common_vendor.f($data.banners, (item, index, i0) => {
      return common_vendor.e({
        a: item.image
      }, item.image ? {
        b: item.image
      } : {}, {
        c: !item.image && item.emoji
      }, !item.image && item.emoji ? {
        d: common_vendor.t(item.emoji)
      } : {}, {
        e: common_vendor.t(item.title),
        f: common_vendor.t(item.description),
        g: item.image ? "rgba(0,0,0,0.4)" : item.background || "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
        h: item.text_color || "#ffffff",
        i: common_vendor.o(($event) => $options.handleBannerClick(item), index),
        j: index
      });
    }),
    b: common_vendor.o((...args) => $options.goToEarn && $options.goToEarn(...args)),
    c: (_a = $data.activities.daily_sign) == null ? void 0 : _a.enabled
  }, ((_b = $data.activities.daily_sign) == null ? void 0 : _b.enabled) ? {
    d: common_vendor.o((...args) => $options.goToSign && $options.goToSign(...args))
  } : {}, {
    e: common_vendor.o((...args) => $options.goToPoints && $options.goToPoints(...args)),
    f: common_vendor.o((...args) => $options.goToMall && $options.goToMall(...args)),
    g: common_vendor.o((...args) => $options.goToMall && $options.goToMall(...args)),
    h: common_vendor.f($data.recommendProducts, (item, k0, i0) => {
      return common_vendor.e({
        a: item.image
      }, item.image ? {
        b: item.image
      } : {
        c: common_vendor.t(item.emoji)
      }, {
        d: common_vendor.t(item.name),
        e: common_vendor.t(item.points_price),
        f: item.id,
        g: common_vendor.o(($event) => $options.goToDetail(item.id), item.id)
      });
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-1cf27b2a"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/index/index.js.map
