"use strict";
const common_vendor = require("../../common/vendor.js");
const api_index = require("../../api/index.js");
const _sfc_main = {
  data() {
    return {
      addresses: [],
      loading: false,
      fromOrder: false
      // 是否从订单页面跳转过来
    };
  },
  onLoad(options) {
    this.fromOrder = options.from === "order";
    this.loadAddresses();
  },
  onShow() {
    this.loadAddresses();
  },
  methods: {
    // 加载地址列表
    async loadAddresses() {
      if (this.loading)
        return;
      this.loading = true;
      try {
        const response = await api_index.addressAPI.getList();
        this.addresses = response.data || [];
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/address/list.vue:85", "获取地址列表失败:", error);
        common_vendor.index.showToast({
          title: error.message || "获取地址列表失败",
          icon: "none"
        });
      } finally {
        this.loading = false;
      }
    },
    // 选择地址（从订单页面跳转过来时）
    selectAddress(address) {
      if (this.fromOrder) {
        const pages = getCurrentPages();
        const prevPage = pages[pages.length - 2];
        if (prevPage) {
          prevPage.$vm.selectedAddress = address;
        }
        common_vendor.index.navigateBack();
      }
    },
    // 添加地址
    addAddress() {
      common_vendor.index.navigateTo({
        url: "/pages/address/edit"
      });
    },
    // 编辑地址
    editAddress(address) {
      common_vendor.index.navigateTo({
        url: `/pages/address/edit?id=${address.id}`
      });
    },
    // 删除地址
    async deleteAddress(address) {
      try {
        await common_vendor.index.showModal({
          title: "确认删除",
          content: "确定要删除这个地址吗？",
          confirmText: "删除",
          confirmColor: "#ff4757"
        });
        await api_index.addressAPI.delete(address.id);
        common_vendor.index.showToast({
          title: "删除成功",
          icon: "success"
        });
        this.loadAddresses();
      } catch (error) {
        if (error.errMsg && error.errMsg.includes("cancel")) {
          return;
        }
        common_vendor.index.__f__("error", "at pages/address/list.vue:149", "删除地址失败:", error);
        common_vendor.index.showToast({
          title: error.message || "删除失败",
          icon: "none"
        });
      }
    },
    // 设为默认地址
    async setDefault(address) {
      try {
        await api_index.addressAPI.setDefault(address.id);
        common_vendor.index.showToast({
          title: "设置成功",
          icon: "success"
        });
        this.loadAddresses();
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/address/list.vue:171", "设置默认地址失败:", error);
        common_vendor.index.showToast({
          title: error.message || "设置失败",
          icon: "none"
        });
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.addresses.length > 0
  }, $data.addresses.length > 0 ? {
    b: common_vendor.f($data.addresses, (address, k0, i0) => {
      return common_vendor.e({
        a: common_vendor.t(address.name),
        b: common_vendor.t(address.phone),
        c: address.is_default
      }, address.is_default ? {} : {}, {
        d: common_vendor.t(address.full_address),
        e: common_vendor.o(($event) => $options.editAddress(address), address.id),
        f: !address.is_default
      }, !address.is_default ? {
        g: common_vendor.o(($event) => $options.deleteAddress(address), address.id)
      } : {}, {
        h: !address.is_default
      }, !address.is_default ? {
        i: common_vendor.o(($event) => $options.setDefault(address), address.id)
      } : {}, {
        j: common_vendor.o(() => {
        }, address.id),
        k: address.id,
        l: common_vendor.o(($event) => $options.selectAddress(address), address.id)
      });
    })
  } : {}, {
    c: common_vendor.o((...args) => $options.addAddress && $options.addAddress(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-90a3874e"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/address/list.js.map
