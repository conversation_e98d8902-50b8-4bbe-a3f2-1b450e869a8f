<?php
/**
 * 认证服务类
 * 处理用户登录、注册、Token验证等
 */

declare(strict_types=1);

require_once __DIR__ . '/../core/Database.php';
require_once __DIR__ . '/../core/JWT.php';
require_once __DIR__ . '/../models/User.php';
require_once __DIR__ . '/WechatService.php';

class AuthService
{
    private Database $db;
    private WechatService $wechatService;
    private array $config;

    public function __construct()
    {
        $this->db = Database::getInstance();
        $this->wechatService = new WechatService();
        $this->config = require __DIR__ . '/../config/app.php';
    }

    /**
     * 微信小程序登录
     */
    public function wechatLogin(string $code, ?array $userInfo = null): array
    {
        try {
            // 1. 通过code获取openid
            error_log('AuthService: 开始获取微信信息');
            $wechatInfo = $this->wechatService->getOpenId($code);

            if (empty($wechatInfo['openid'])) {
                throw new RuntimeException('获取用户OpenID失败');
            }
            error_log('AuthService: 获取到openid: ' . $wechatInfo['openid']);

            // 2. 查找或创建用户
            error_log('AuthService: 开始查找或创建用户');
            $user = $this->findOrCreateUser($wechatInfo, $userInfo);
            error_log('AuthService: 用户处理完成，用户ID: ' . $user->id);

            // 3. 更新登录时间
            error_log('AuthService: 开始更新登录时间');
            $user->updateLoginTime();
            error_log('AuthService: 登录时间更新完成');

            // 4. 生成JWT Token
            error_log('AuthService: 开始生成Token');
            $token = $this->generateToken($user);
            error_log('AuthService: Token生成完成');

            $result = [
                'token' => $token,
                'user' => $user->toArray(),
                'session_key' => $wechatInfo['session_key'] ?? null
            ];

            error_log('AuthService: 登录成功，返回结果');
            return $result;

        } catch (Exception $e) {
            error_log('AuthService: 登录失败 - ' . $e->getMessage());
            error_log('AuthService: 错误堆栈 - ' . $e->getTraceAsString());
            throw new RuntimeException('微信登录失败: ' . $e->getMessage());
        }
    }

    /**
     * 使用加密用户信息登录
     */
    public function wechatLoginWithEncryptedData(
        string $code,
        string $encryptedData,
        string $iv
    ): array {
        try {
            // 1. 获取session_key
            $wechatInfo = $this->wechatService->getOpenId($code);

            // 2. 解密用户信息
            $userInfo = $this->wechatService->decryptUserInfo(
                $wechatInfo['session_key'],
                $encryptedData,
                $iv
            );

            // 3. 合并信息
            $completeInfo = array_merge($wechatInfo, $userInfo);

            // 4. 查找或创建用户
            $user = $this->findOrCreateUser($completeInfo);

            // 5. 更新登录时间
            $user->updateLoginTime();

            // 6. 生成Token
            $token = $this->generateToken($user);

            return [
                'token' => $token,
                'user' => $user->toArray()
            ];

        } catch (Exception $e) {
            throw new RuntimeException('微信登录失败: ' . $e->getMessage());
        }
    }

    /**
     * 查找或创建用户
     */
    private function findOrCreateUser(array $wechatInfo, ?array $additionalInfo = null): User
    {
        try {
            error_log('findOrCreateUser: 开始查找用户，openid: ' . $wechatInfo['openid']);

            // 查找现有用户
            $user = User::findByOpenId($wechatInfo['openid']);

            if ($user) {
                error_log('findOrCreateUser: 找到现有用户，ID: ' . $user->id);
                // 更新用户信息（如果有新信息）
                $mergedInfo = $wechatInfo;
                if ($additionalInfo) {
                    $mergedInfo = array_merge($wechatInfo, $additionalInfo);
                }
                $this->updateUserInfo($user, $mergedInfo);
                return $user;
            }

            error_log('findOrCreateUser: 用户不存在，开始创建新用户');

            // 创建新用户
            $userData = $this->prepareUserData($wechatInfo, $additionalInfo);
            error_log('findOrCreateUser: 用户数据准备完成: ' . json_encode($userData, JSON_UNESCAPED_UNICODE));

            $user = User::createFromWechat($userData);
            error_log('findOrCreateUser: 新用户创建完成，ID: ' . $user->id);

            // 记录用户注册
            $this->logUserAction($user->id, 'register', [
                'source' => 'wechat_miniprogram',
                'has_userinfo' => !empty($additionalInfo)
            ]);

            return $user;

        } catch (Exception $e) {
            error_log('findOrCreateUser: 错误 - ' . $e->getMessage());
            error_log('findOrCreateUser: 错误堆栈 - ' . $e->getTraceAsString());
            throw $e;
        }
    }

    /**
     * 准备用户数据
     */
    private function prepareUserData(array $wechatInfo, ?array $additionalInfo = null): array
    {
        $userData = [
            'openid' => $wechatInfo['openid'],
            'unionid' => $wechatInfo['unionid'] ?? null,
            'nickname' => '',
            'avatar' => '',
            'gender' => 0,
            'total_points' => 0,
            'available_points' => 0,
            'used_points' => 0,
            'frozen_points' => 0,
            'status' => 1
        ];

        // 合并微信信息
        if (!empty($wechatInfo['nickname'])) {
            $userData['nickname'] = $this->sanitizeNickname($wechatInfo['nickname']);
        }

        if (!empty($wechatInfo['avatar'])) {
            $userData['avatar'] = $wechatInfo['avatar'];
        }

        if (isset($wechatInfo['gender'])) {
            $userData['gender'] = (int)$wechatInfo['gender'];
        }

        // 合并额外信息
        if ($additionalInfo) {
            foreach (['city', 'province', 'country'] as $field) {
                if (!empty($additionalInfo[$field])) {
                    $userData[$field] = $additionalInfo[$field];
                }
            }
        }

        return $userData;
    }

    /**
     * 清理昵称
     */
    private function sanitizeNickname(string $nickname): string
    {
        try {
            error_log('sanitizeNickname: 原始昵称: ' . $nickname);

            // 移除特殊字符，保留中文、英文、数字
            $cleaned = preg_replace('/[^\p{L}\p{N}\s]/u', '', $nickname);

            if ($cleaned === null) {
                error_log('sanitizeNickname: preg_replace 失败，使用原始昵称');
                $cleaned = $nickname;
            }

            error_log('sanitizeNickname: 清理后昵称: ' . $cleaned);

            // 限制长度
            if (mb_strlen($cleaned) > 20) {
                $cleaned = mb_substr($cleaned, 0, 20);
            }

            // 如果清理后为空，生成默认昵称
            if (empty(trim($cleaned))) {
                $cleaned = '用户' . substr(md5(uniqid()), 0, 6);
                error_log('sanitizeNickname: 生成默认昵称: ' . $cleaned);
            }

            $result = trim($cleaned);
            error_log('sanitizeNickname: 最终昵称: ' . $result);
            return $result;

        } catch (Exception $e) {
            error_log('sanitizeNickname: 错误 - ' . $e->getMessage());
            // 如果出错，返回默认昵称
            return '用户' . substr(md5(uniqid()), 0, 6);
        }
    }

    /**
     * 更新用户信息
     */
    private function updateUserInfo(User $user, array $info): void
    {
        $needUpdate = false;

        // 准备更新数据
        $updateData = [];

        // 更新昵称
        if (!empty($info['nickname']) && $user->nickname !== $info['nickname']) {
            $updateData['nickname'] = $info['nickname'];
            $needUpdate = true;
        }

        // 更新头像
        if (!empty($info['avatar']) && $user->avatar !== $info['avatar']) {
            $updateData['avatar'] = $info['avatar'];
            $needUpdate = true;
        }

        // 批量更新属性
        if (!empty($updateData)) {
            foreach ($updateData as $key => $value) {
                $user->setAttribute($key, $value);
            }
        }

        // 更新性别
        if (isset($info['gender']) && $user->gender !== $info['gender']) {
            $user->gender = $info['gender'];
            $needUpdate = true;
        }

        // 更新unionid
        if (!empty($info['unionid']) && $user->unionid !== $info['unionid']) {
            $user->unionid = $info['unionid'];
            $needUpdate = true;
        }

        if ($needUpdate) {
            $user->save();
        }
    }

    /**
     * 生成JWT Token
     */
    private function generateToken(User $user): string
    {
        $payload = [
            'user_id' => $user->id,
            'openid' => $user->openid,
            'iat' => time(),
            'exp' => time() + $this->config['jwt_expire']
        ];

        return JWT::encode($payload, $this->config['jwt_secret']);
    }

    /**
     * 验证Token
     */
    public function validateToken(string $token): array
    {
        try {
            $payload = JWT::decode($token, $this->config['jwt_secret']);

            // 验证用户是否存在且状态正常
            $user = User::find($payload['user_id']);
            if (!$user || !$user->isActive()) {
                throw new RuntimeException('用户不存在或已被禁用');
            }

            return [
                'user_id' => $payload['user_id'],
                'openid' => $payload['openid'],
                'user' => $user
            ];

        } catch (Exception $e) {
            throw new RuntimeException('Token验证失败: ' . $e->getMessage());
        }
    }

    /**
     * 刷新Token
     */
    public function refreshToken(string $token): string
    {
        try {
            // 验证当前Token（允许过期）
            $payload = JWT::decode($token, $this->config['jwt_secret']);

            // 检查用户状态
            $user = User::find($payload['user_id']);
            if (!$user || !$user->isActive()) {
                throw new RuntimeException('用户不存在或已被禁用');
            }

            // 生成新Token
            return $this->generateToken($user);

        } catch (Exception $e) {
            throw new RuntimeException('Token刷新失败: ' . $e->getMessage());
        }
    }

    /**
     * 退出登录
     */
    public function logout(string $token): bool
    {
        try {
            // 这里可以实现Token黑名单机制
            // 目前简单返回true，实际项目中可以将Token加入黑名单

            // 可选：记录退出日志
            $payload = JWT::decode($token, $this->config['jwt_secret']);
            $this->logUserAction($payload['user_id'], 'logout');

            return true;

        } catch (Exception $e) {
            // 即使Token无效，也认为退出成功
            return true;
        }
    }

    /**
     * 获取当前用户信息
     */
    public function getCurrentUser(string $token): User
    {
        $authInfo = $this->validateToken($token);
        return $authInfo['user'];
    }

    /**
     * 检查用户权限
     */
    public function checkPermission(User $user, string $permission): bool
    {
        // 这里可以实现复杂的权限检查逻辑
        // 目前简单检查用户状态
        return $user->isActive();
    }

    /**
     * 记录用户操作日志
     */
    private function logUserAction(int $userId, string $action, ?array $data = null): void
    {
        try {
            $this->db->insert('user_logs', [
                'user_id' => $userId,
                'action' => $action,
                'data' => $data ? json_encode($data) : null,
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? null,
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null,
                'created_at' => date('Y-m-d H:i:s')
            ]);
        } catch (Exception $e) {
            // 日志记录失败不影响主流程
            error_log('用户操作日志记录失败: ' . $e->getMessage());
        }
    }

    /**
     * 生成随机字符串
     */
    private function generateRandomString(int $length = 32): string
    {
        return bin2hex(random_bytes($length / 2));
    }
}
