{"version": 3, "file": "common.js", "sources": ["utils/common.js"], "sourcesContent": ["// 通用工具函数\nimport { getImageUrl } from './request.js'\n\n/**\n * 格式化时间\n * @param {Date|string|number} date 时间\n * @param {string} format 格式 YYYY-MM-DD HH:mm:ss\n */\nexport const formatTime = (date, format = 'YYYY-MM-DD HH:mm:ss') => {\n\tif (!date) return ''\n\n\tconst d = new Date(date)\n\tif (isNaN(d.getTime())) return ''\n\n\tconst year = d.getFullYear()\n\tconst month = String(d.getMonth() + 1).padStart(2, '0')\n\tconst day = String(d.getDate()).padStart(2, '0')\n\tconst hour = String(d.getHours()).padStart(2, '0')\n\tconst minute = String(d.getMinutes()).padStart(2, '0')\n\tconst second = String(d.getSeconds()).padStart(2, '0')\n\n\treturn format\n\t\t.replace('YYYY', year)\n\t\t.replace('MM', month)\n\t\t.replace('DD', day)\n\t\t.replace('HH', hour)\n\t\t.replace('mm', minute)\n\t\t.replace('ss', second)\n}\n\n/**\n * 相对时间\n * @param {Date|string|number} date 时间\n */\nexport const timeAgo = (date) => {\n\tif (!date) return ''\n\n\tconst now = new Date()\n\tconst target = new Date(date)\n\tconst diff = now.getTime() - target.getTime()\n\n\tconst minute = 60 * 1000\n\tconst hour = 60 * minute\n\tconst day = 24 * hour\n\tconst month = 30 * day\n\tconst year = 12 * month\n\n\tif (diff < minute) {\n\t\treturn '刚刚'\n\t} else if (diff < hour) {\n\t\treturn Math.floor(diff / minute) + '分钟前'\n\t} else if (diff < day) {\n\t\treturn Math.floor(diff / hour) + '小时前'\n\t} else if (diff < month) {\n\t\treturn Math.floor(diff / day) + '天前'\n\t} else if (diff < year) {\n\t\treturn Math.floor(diff / month) + '个月前'\n\t} else {\n\t\treturn Math.floor(diff / year) + '年前'\n\t}\n}\n\n/**\n * 防抖函数\n * @param {Function} func 要防抖的函数\n * @param {number} delay 延迟时间\n */\nexport const debounce = (func, delay = 300) => {\n\tlet timer = null\n\treturn function(...args) {\n\t\tif (timer) clearTimeout(timer)\n\t\ttimer = setTimeout(() => {\n\t\t\tfunc.apply(this, args)\n\t\t}, delay)\n\t}\n}\n\n/**\n * 节流函数\n * @param {Function} func 要节流的函数\n * @param {number} delay 延迟时间\n */\nexport const throttle = (func, delay = 300) => {\n\tlet timer = null\n\treturn function(...args) {\n\t\tif (!timer) {\n\t\t\ttimer = setTimeout(() => {\n\t\t\t\tfunc.apply(this, args)\n\t\t\t\ttimer = null\n\t\t\t}, delay)\n\t\t}\n\t}\n}\n\n/**\n * 深拷贝\n * @param {any} obj 要拷贝的对象\n */\nexport const deepClone = (obj) => {\n\tif (obj === null || typeof obj !== 'object') return obj\n\tif (obj instanceof Date) return new Date(obj)\n\tif (obj instanceof Array) return obj.map(item => deepClone(item))\n\tif (typeof obj === 'object') {\n\t\tconst clonedObj = {}\n\t\tfor (const key in obj) {\n\t\t\tif (obj.hasOwnProperty(key)) {\n\t\t\t\tclonedObj[key] = deepClone(obj[key])\n\t\t\t}\n\t\t}\n\t\treturn clonedObj\n\t}\n}\n\n/**\n * 生成随机字符串\n * @param {number} length 长度\n */\nexport const randomString = (length = 8) => {\n\tconst chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'\n\tlet result = ''\n\tfor (let i = 0; i < length; i++) {\n\t\tresult += chars.charAt(Math.floor(Math.random() * chars.length))\n\t}\n\treturn result\n}\n\n/**\n * 格式化文件大小\n * @param {number} size 文件大小（字节）\n */\nexport const formatFileSize = (size) => {\n\tif (!size) return '0 B'\n\n\tconst units = ['B', 'KB', 'MB', 'GB', 'TB']\n\tlet index = 0\n\n\twhile (size >= 1024 && index < units.length - 1) {\n\t\tsize /= 1024\n\t\tindex++\n\t}\n\n\treturn Math.round(size * 100) / 100 + ' ' + units[index]\n}\n\n/**\n * 验证手机号\n * @param {string} phone 手机号\n */\nexport const validatePhone = (phone) => {\n\tconst reg = /^1[3-9]\\d{9}$/\n\treturn reg.test(phone)\n}\n\n/**\n * 验证邮箱\n * @param {string} email 邮箱\n */\nexport const validateEmail = (email) => {\n\tconst reg = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$/\n\treturn reg.test(email)\n}\n\n/**\n * 验证身份证号\n * @param {string} idCard 身份证号\n */\nexport const validateIdCard = (idCard) => {\n\tconst reg = /(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)/\n\treturn reg.test(idCard)\n}\n\n/**\n * 获取URL参数\n * @param {string} name 参数名\n * @param {string} url URL地址\n */\nexport const getUrlParam = (name, url = window.location.href) => {\n\tconst reg = new RegExp('[?&]' + name + '=([^&#]*)', 'i')\n\tconst match = url.match(reg)\n\treturn match ? decodeURIComponent(match[1]) : null\n}\n\n/**\n * 设置页面标题\n * @param {string} title 标题\n */\nexport const setPageTitle = (title) => {\n\tuni.setNavigationBarTitle({\n\t\ttitle: title\n\t})\n}\n\n/**\n * 显示成功提示\n * @param {string} message 消息\n */\nexport const showSuccess = (message) => {\n\tuni.showToast({\n\t\ttitle: message,\n\t\ticon: 'success',\n\t\tduration: 2000\n\t})\n}\n\n/**\n * 显示错误提示\n * @param {string} message 消息\n */\nexport const showError = (message) => {\n\tuni.showToast({\n\t\ttitle: message,\n\t\ticon: 'none',\n\t\tduration: 2000\n\t})\n}\n\n/**\n * 显示加载提示\n * @param {string} message 消息\n */\nexport const showLoading = (message = '加载中...') => {\n\tuni.showLoading({\n\t\ttitle: message,\n\t\tmask: true\n\t})\n}\n\n/**\n * 隐藏加载提示\n */\nexport const hideLoading = () => {\n\tuni.hideLoading()\n}\n\n/**\n * 确认对话框\n * @param {string} content 内容\n * @param {string} title 标题\n */\nexport const showConfirm = (content, title = '提示') => {\n\treturn new Promise((resolve, reject) => {\n\t\tuni.showModal({\n\t\t\ttitle,\n\t\t\tcontent,\n\t\t\tsuccess: (res) => {\n\t\t\t\tif (res.confirm) {\n\t\t\t\t\tresolve(true)\n\t\t\t\t} else {\n\t\t\t\t\treject(false)\n\t\t\t\t}\n\t\t\t},\n\t\t\tfail: reject\n\t\t})\n\t})\n}\n\n/**\n * 跳转页面\n * @param {string} url 页面路径\n * @param {object} params 参数\n */\nexport const navigateTo = (url, params = {}) => {\n\tconst query = Object.keys(params).map(key => `${key}=${encodeURIComponent(params[key])}`).join('&')\n\tconst fullUrl = query ? `${url}?${query}` : url\n\n\tuni.navigateTo({\n\t\turl: fullUrl,\n\t\tfail: (err) => {\n\t\t\tconsole.error('页面跳转失败:', err)\n\t\t}\n\t})\n}\n\n/**\n * 返回上一页\n * @param {number} delta 返回层数\n */\nexport const navigateBack = (delta = 1) => {\n\tuni.navigateBack({\n\t\tdelta\n\t})\n}\n\n/**\n * 切换Tab页面\n * @param {string} url Tab页面路径\n */\nexport const switchTab = (url) => {\n\tuni.switchTab({\n\t\turl,\n\t\tfail: (err) => {\n\t\t\tconsole.error('Tab切换失败:', err)\n\t\t}\n\t})\n}\n\n/**\n * 获取系统信息\n */\nexport const getSystemInfo = () => {\n\treturn new Promise((resolve, reject) => {\n\t\tuni.getSystemInfo({\n\t\t\tsuccess: resolve,\n\t\t\tfail: reject\n\t\t})\n\t})\n}\n\n/**\n * 获取用户位置\n */\nexport const getLocation = () => {\n\treturn new Promise((resolve, reject) => {\n\t\tuni.getLocation({\n\t\t\ttype: 'gcj02',\n\t\t\tsuccess: resolve,\n\t\t\tfail: reject\n\t\t})\n\t})\n}\n\n/**\n * 选择图片\n * @param {number} count 数量\n */\nexport const chooseImage = (count = 1) => {\n\treturn new Promise((resolve, reject) => {\n\t\tuni.chooseImage({\n\t\t\tcount,\n\t\t\tsizeType: ['compressed'],\n\t\t\tsourceType: ['album', 'camera'],\n\t\t\tsuccess: resolve,\n\t\t\tfail: reject\n\t\t})\n\t})\n}\n\n/**\n * 预览图片\n * @param {Array} urls 图片地址数组\n * @param {number} current 当前显示图片索引\n */\nexport const previewImage = (urls, current = 0) => {\n\tuni.previewImage({\n\t\turls,\n\t\tcurrent: typeof current === 'number' ? urls[current] : current\n\t})\n}\n\nexport default {\n\tformatTime,\n\ttimeAgo,\n\tdebounce,\n\tthrottle,\n\tdeepClone,\n\trandomString,\n\tformatFileSize,\n\tvalidatePhone,\n\tvalidateEmail,\n\tvalidateIdCard,\n\tgetUrlParam,\n\tsetPageTitle,\n\tshowSuccess,\n\tshowError,\n\tshowLoading,\n\thideLoading,\n\tshowConfirm,\n\tnavigateTo,\n\tnavigateBack,\n\tswitchTab,\n\tgetSystemInfo,\n\tgetLocation,\n\tchooseImage,\n\tpreviewImage,\n\tprocessHtmlImages\n}\n\n/**\n * 处理HTML内容中的图片URL\n * 将相对路径转换为完整的URL\n * @param {string} htmlContent HTML内容\n * @returns {string} 处理后的HTML内容\n */\nexport const processHtmlImages = (htmlContent) => {\n\tif (!htmlContent) return htmlContent\n\n\t// 使用正则表达式匹配img标签的src属性\n\treturn htmlContent.replace(/src=[\"']([^\"']+)[\"']/g, (match, src) => {\n\t\t// 使用统一的getImageUrl函数处理图片路径\n\t\tconst fullUrl = getImageUrl(src)\n\t\treturn `src=\"${fullUrl}\"`\n\t})\n}\n"], "names": ["uni", "getImageUrl"], "mappings": ";;;AAQO,MAAM,aAAa,CAAC,MAAM,SAAS,0BAA0B;AACnE,MAAI,CAAC;AAAM,WAAO;AAElB,QAAM,IAAI,IAAI,KAAK,IAAI;AACvB,MAAI,MAAM,EAAE,QAAS,CAAA;AAAG,WAAO;AAE/B,QAAM,OAAO,EAAE,YAAa;AAC5B,QAAM,QAAQ,OAAO,EAAE,SAAQ,IAAK,CAAC,EAAE,SAAS,GAAG,GAAG;AACtD,QAAM,MAAM,OAAO,EAAE,QAAS,CAAA,EAAE,SAAS,GAAG,GAAG;AAC/C,QAAM,OAAO,OAAO,EAAE,SAAU,CAAA,EAAE,SAAS,GAAG,GAAG;AACjD,QAAM,SAAS,OAAO,EAAE,WAAY,CAAA,EAAE,SAAS,GAAG,GAAG;AACrD,QAAM,SAAS,OAAO,EAAE,WAAY,CAAA,EAAE,SAAS,GAAG,GAAG;AAErD,SAAO,OACL,QAAQ,QAAQ,IAAI,EACpB,QAAQ,MAAM,KAAK,EACnB,QAAQ,MAAM,GAAG,EACjB,QAAQ,MAAM,IAAI,EAClB,QAAQ,MAAM,MAAM,EACpB,QAAQ,MAAM,MAAM;AACvB;AAMO,MAAM,UAAU,CAAC,SAAS;AAChC,MAAI,CAAC;AAAM,WAAO;AAElB,QAAM,MAAM,oBAAI,KAAM;AACtB,QAAM,SAAS,IAAI,KAAK,IAAI;AAC5B,QAAM,OAAO,IAAI,QAAO,IAAK,OAAO,QAAS;AAE7C,QAAM,SAAS,KAAK;AACpB,QAAM,OAAO,KAAK;AAClB,QAAM,MAAM,KAAK;AACjB,QAAM,QAAQ,KAAK;AACnB,QAAM,OAAO,KAAK;AAElB,MAAI,OAAO,QAAQ;AAClB,WAAO;AAAA,EACT,WAAY,OAAO,MAAM;AACvB,WAAO,KAAK,MAAM,OAAO,MAAM,IAAI;AAAA,EACrC,WAAY,OAAO,KAAK;AACtB,WAAO,KAAK,MAAM,OAAO,IAAI,IAAI;AAAA,EACnC,WAAY,OAAO,OAAO;AACxB,WAAO,KAAK,MAAM,OAAO,GAAG,IAAI;AAAA,EAClC,WAAY,OAAO,MAAM;AACvB,WAAO,KAAK,MAAM,OAAO,KAAK,IAAI;AAAA,EACpC,OAAQ;AACN,WAAO,KAAK,MAAM,OAAO,IAAI,IAAI;AAAA,EACjC;AACF;AAOO,MAAM,WAAW,CAAC,MAAM,QAAQ,QAAQ;AAC9C,MAAI,QAAQ;AACZ,SAAO,YAAY,MAAM;AACxB,QAAI;AAAO,mBAAa,KAAK;AAC7B,YAAQ,WAAW,MAAM;AACxB,WAAK,MAAM,MAAM,IAAI;AAAA,IACrB,GAAE,KAAK;AAAA,EACR;AACF;AAOO,MAAM,WAAW,CAAC,MAAM,QAAQ,QAAQ;AAC9C,MAAI,QAAQ;AACZ,SAAO,YAAY,MAAM;AACxB,QAAI,CAAC,OAAO;AACX,cAAQ,WAAW,MAAM;AACxB,aAAK,MAAM,MAAM,IAAI;AACrB,gBAAQ;AAAA,MACR,GAAE,KAAK;AAAA,IACR;AAAA,EACD;AACF;AAMO,MAAM,YAAY,CAAC,QAAQ;AACjC,MAAI,QAAQ,QAAQ,OAAO,QAAQ;AAAU,WAAO;AACpD,MAAI,eAAe;AAAM,WAAO,IAAI,KAAK,GAAG;AAC5C,MAAI,eAAe;AAAO,WAAO,IAAI,IAAI,UAAQ,UAAU,IAAI,CAAC;AAChE,MAAI,OAAO,QAAQ,UAAU;AAC5B,UAAM,YAAY,CAAE;AACpB,eAAW,OAAO,KAAK;AACtB,UAAI,IAAI,eAAe,GAAG,GAAG;AAC5B,kBAAU,GAAG,IAAI,UAAU,IAAI,GAAG,CAAC;AAAA,MACnC;AAAA,IACD;AACD,WAAO;AAAA,EACP;AACF;AAMO,MAAM,eAAe,CAAC,SAAS,MAAM;AAC3C,QAAM,QAAQ;AACd,MAAI,SAAS;AACb,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAChC,cAAU,MAAM,OAAO,KAAK,MAAM,KAAK,OAAQ,IAAG,MAAM,MAAM,CAAC;AAAA,EAC/D;AACD,SAAO;AACR;AAMO,MAAM,iBAAiB,CAAC,SAAS;AACvC,MAAI,CAAC;AAAM,WAAO;AAElB,QAAM,QAAQ,CAAC,KAAK,MAAM,MAAM,MAAM,IAAI;AAC1C,MAAI,QAAQ;AAEZ,SAAO,QAAQ,QAAQ,QAAQ,MAAM,SAAS,GAAG;AAChD,YAAQ;AACR;AAAA,EACA;AAED,SAAO,KAAK,MAAM,OAAO,GAAG,IAAI,MAAM,MAAM,MAAM,KAAK;AACxD;AAMO,MAAM,gBAAgB,CAAC,UAAU;AACvC,QAAM,MAAM;AACZ,SAAO,IAAI,KAAK,KAAK;AACtB;AAMO,MAAM,gBAAgB,CAAC,UAAU;AACvC,QAAM,MAAM;AACZ,SAAO,IAAI,KAAK,KAAK;AACtB;AAMO,MAAM,iBAAiB,CAAC,WAAW;AACzC,QAAM,MAAM;AACZ,SAAO,IAAI,KAAK,MAAM;AACvB;AAOO,MAAM,cAAc,CAAC,MAAM,MAAM,OAAO,SAAS,SAAS;AAChE,QAAM,MAAM,IAAI,OAAO,SAAS,OAAO,aAAa,GAAG;AACvD,QAAM,QAAQ,IAAI,MAAM,GAAG;AAC3B,SAAO,QAAQ,mBAAmB,MAAM,CAAC,CAAC,IAAI;AAC/C;AAMO,MAAM,eAAe,CAAC,UAAU;AACtCA,gBAAAA,MAAI,sBAAsB;AAAA,IACzB;AAAA,EACF,CAAE;AACF;AAMO,MAAM,cAAc,CAAC,YAAY;AACvCA,gBAAAA,MAAI,UAAU;AAAA,IACb,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU;AAAA,EACZ,CAAE;AACF;AAMO,MAAM,YAAY,CAAC,YAAY;AACrCA,gBAAAA,MAAI,UAAU;AAAA,IACb,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU;AAAA,EACZ,CAAE;AACF;AAMO,MAAM,cAAc,CAAC,UAAU,aAAa;AAClDA,gBAAAA,MAAI,YAAY;AAAA,IACf,OAAO;AAAA,IACP,MAAM;AAAA,EACR,CAAE;AACF;AAKO,MAAM,cAAc,MAAM;AAChCA,gBAAAA,MAAI,YAAa;AAClB;AAOO,MAAM,cAAc,CAAC,SAAS,QAAQ,SAAS;AACrD,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACvCA,kBAAAA,MAAI,UAAU;AAAA,MACb;AAAA,MACA;AAAA,MACA,SAAS,CAAC,QAAQ;AACjB,YAAI,IAAI,SAAS;AAChB,kBAAQ,IAAI;AAAA,QACjB,OAAW;AACN,iBAAO,KAAK;AAAA,QACZ;AAAA,MACD;AAAA,MACD,MAAM;AAAA,IACT,CAAG;AAAA,EACH,CAAE;AACF;AAOO,MAAM,aAAa,CAAC,KAAK,SAAS,OAAO;AAC/C,QAAM,QAAQ,OAAO,KAAK,MAAM,EAAE,IAAI,SAAO,GAAG,GAAG,IAAI,mBAAmB,OAAO,GAAG,CAAC,CAAC,EAAE,EAAE,KAAK,GAAG;AAClG,QAAM,UAAU,QAAQ,GAAG,GAAG,IAAI,KAAK,KAAK;AAE5CA,gBAAAA,MAAI,WAAW;AAAA,IACd,KAAK;AAAA,IACL,MAAM,CAAC,QAAQ;AACdA,oBAAAA,MAAA,MAAA,SAAA,0BAAc,WAAW,GAAG;AAAA,IAC5B;AAAA,EACH,CAAE;AACF;AAMO,MAAM,eAAe,CAAC,QAAQ,MAAM;AAC1CA,gBAAAA,MAAI,aAAa;AAAA,IAChB;AAAA,EACF,CAAE;AACF;AAMO,MAAM,YAAY,CAAC,QAAQ;AACjCA,gBAAAA,MAAI,UAAU;AAAA,IACb;AAAA,IACA,MAAM,CAAC,QAAQ;AACdA,oBAAAA,MAAA,MAAA,SAAA,0BAAc,YAAY,GAAG;AAAA,IAC7B;AAAA,EACH,CAAE;AACF;AAKO,MAAM,gBAAgB,MAAM;AAClC,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACvCA,kBAAAA,MAAI,cAAc;AAAA,MACjB,SAAS;AAAA,MACT,MAAM;AAAA,IACT,CAAG;AAAA,EACH,CAAE;AACF;AAKO,MAAM,cAAc,MAAM;AAChC,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACvCA,kBAAAA,MAAI,YAAY;AAAA,MACf,MAAM;AAAA,MACN,SAAS;AAAA,MACT,MAAM;AAAA,IACT,CAAG;AAAA,EACH,CAAE;AACF;AAMO,MAAM,cAAc,CAAC,QAAQ,MAAM;AACzC,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACvCA,kBAAAA,MAAI,YAAY;AAAA,MACf;AAAA,MACA,UAAU,CAAC,YAAY;AAAA,MACvB,YAAY,CAAC,SAAS,QAAQ;AAAA,MAC9B,SAAS;AAAA,MACT,MAAM;AAAA,IACT,CAAG;AAAA,EACH,CAAE;AACF;AAOO,MAAM,eAAe,CAAC,MAAM,UAAU,MAAM;AAClDA,gBAAAA,MAAI,aAAa;AAAA,IAChB;AAAA,IACA,SAAS,OAAO,YAAY,WAAW,KAAK,OAAO,IAAI;AAAA,EACzD,CAAE;AACF;AAAA,CAEe;AAAA,EACd;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;AAQY,MAAC,oBAAoB,CAAC,gBAAgB;AACjD,MAAI,CAAC;AAAa,WAAO;AAGzB,SAAO,YAAY,QAAQ,yBAAyB,CAAC,OAAO,QAAQ;AAEnE,UAAM,UAAUC,cAAW,YAAC,GAAG;AAC/B,WAAO,QAAQ,OAAO;AAAA,EACxB,CAAE;AACF;;"}