{"version": 3, "file": "index.js", "sources": ["pages/index/index.vue", "../../../Program Files/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvaW5kZXgvaW5kZXgudnVl"], "sourcesContent": ["<template>\r\n\t<view class=\"home-container\">\r\n\t\t<!-- 轮播图 -->\r\n\t\t<swiper class=\"banner-swiper\" indicator-dots=\"true\" autoplay=\"true\" interval=\"3000\" duration=\"500\">\r\n\t\t\t<swiper-item v-for=\"(item, index) in banners\" :key=\"index\">\r\n\t\t\t\t<view class=\"banner-card\" @click=\"handleBannerClick(item)\">\r\n\t\t\t\t\t<!-- 如果有图片，显示图片背景 -->\r\n\t\t\t\t\t<image v-if=\"item.image\" class=\"banner-bg-image\" :src=\"item.image\" mode=\"aspectFill\"></image>\r\n\r\n\t\t\t\t\t<!-- 内容覆盖层 -->\r\n\t\t\t\t\t<view class=\"banner-overlay\" :style=\"{ background: item.image ? 'rgba(0,0,0,0.4)' : (item.background || 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'), color: item.text_color || '#ffffff' }\">\r\n\t\t\t\t\t\t<view v-if=\"!item.image && item.emoji\" class=\"banner-icon\">{{ item.emoji }}</view>\r\n\t\t\t\t\t\t<view class=\"banner-content\">\r\n\t\t\t\t\t\t\t<text class=\"banner-title\">{{ item.title }}</text>\r\n\t\t\t\t\t\t\t<text class=\"banner-desc\">{{ item.description }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</swiper-item>\r\n\t\t</swiper>\r\n\r\n\t\t<!-- 快捷入口 -->\r\n\t\t<view class=\"quick-nav\">\r\n\t\t\t<view class=\"nav-item highlight\" @click=\"goToEarn\">\r\n\t\t\t\t<view class=\"nav-icon\">\r\n\t\t\t\t\t<text class=\"nav-emoji\">💰</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<text class=\"nav-text\">赚积分</text>\r\n\t\t\t\t<text class=\"nav-badge\">热门</text>\r\n\t\t\t</view>\r\n\t\t\t<view v-if=\"activities.daily_sign?.enabled\" class=\"nav-item\" @click=\"goToSign\">\r\n\t\t\t\t<view class=\"nav-icon\">\r\n\t\t\t\t\t<text class=\"nav-emoji\">📅</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<text class=\"nav-text\">每日签到</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"nav-item\" @click=\"goToPoints\">\r\n\t\t\t\t<view class=\"nav-icon\">\r\n\t\t\t\t\t<text class=\"nav-emoji\">💰</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<text class=\"nav-text\">我的积分</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"nav-item\" @click=\"goToMall\">\r\n\t\t\t\t<view class=\"nav-icon\">\r\n\t\t\t\t\t<text class=\"nav-emoji\">🛒</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<text class=\"nav-text\">积分商城</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 推荐商品 -->\r\n\t\t<view class=\"recommend-section\">\r\n\t\t\t<view class=\"section-header\">\r\n\t\t\t\t<text class=\"section-title\">热门推荐</text>\r\n\t\t\t\t<text class=\"more-btn\" @click=\"goToMall\">查看更多</text>\r\n\t\t\t</view>\r\n\r\n\t\t\t<scroll-view class=\"product-scroll\" scroll-x=\"true\" show-scrollbar=\"false\">\r\n\t\t\t\t<view class=\"product-list\">\r\n\t\t\t\t\t<view\r\n\t\t\t\t\t\tclass=\"product-card\"\r\n\t\t\t\t\t\tv-for=\"item in recommendProducts\"\r\n\t\t\t\t\t\t:key=\"item.id\"\r\n\t\t\t\t\t\t@click=\"goToDetail(item.id)\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<view class=\"product-image\">\r\n\t\t\t\t\t\t\t<!-- 如果有真实图片，显示图片；否则显示emoji -->\r\n\t\t\t\t\t\t\t<image v-if=\"item.image\"\r\n\t\t\t\t\t\t\t\t   :src=\"item.image\"\r\n\t\t\t\t\t\t\t\t   class=\"product-img\"\r\n\t\t\t\t\t\t\t\t   mode=\"aspectFill\" />\r\n\t\t\t\t\t\t\t<text v-else class=\"product-emoji\">{{ item.emoji }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"product-info\">\r\n\t\t\t\t\t\t\t<text class=\"product-name\">{{ item.name }}</text>\r\n\t\t\t\t\t\t\t<text class=\"product-price\">{{ item.points_price }}积分</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</scroll-view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 积分获取方式 -->\r\n\t\t<view class=\"earn-section\">\r\n\t\t\t<view class=\"section-header\">\r\n\t\t\t\t<text class=\"section-title\">赚取积分</text>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"earn-list\">\r\n\t\t\t\t<view class=\"earn-item\">\r\n\t\t\t\t\t<view class=\"earn-icon\">\r\n\t\t\t\t\t\t<text class=\"earn-emoji\">📅</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"earn-info\">\r\n\t\t\t\t\t\t<text class=\"earn-title\">每日签到</text>\r\n\t\t\t\t\t\t<text class=\"earn-desc\">每天签到可获得10积分</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"earn-points\">+10</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"earn-item\">\r\n\t\t\t\t\t<view class=\"earn-icon\">\r\n\t\t\t\t\t\t<text class=\"earn-emoji\">👥</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"earn-info\">\r\n\t\t\t\t\t\t<text class=\"earn-title\">邀请好友</text>\r\n\t\t\t\t\t\t<text class=\"earn-desc\">邀请好友注册可获得50积分</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"earn-points\">+50</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"earn-item\">\r\n\t\t\t\t\t<view class=\"earn-icon\">\r\n\t\t\t\t\t\t<text class=\"earn-emoji\">🎰</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"earn-info\">\r\n\t\t\t\t\t\t<text class=\"earn-title\">幸运转盘</text>\r\n\t\t\t\t\t\t<text class=\"earn-desc\">每天3次免费抽奖机会</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"earn-points\">随机</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport { systemAPI, productAPI } from '@/api/index.js'\r\nimport { getImageUrl } from '@/utils/request.js'\r\n\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tbanners: [],\r\n\t\t\trecommendProducts: [],\r\n\t\t\tactivities: {}\r\n\t\t}\r\n\t},\r\n\tonLoad() {\r\n\t\tthis.loadActivities();\r\n\t\tthis.loadBanners();\r\n\t\tthis.loadRecommendProducts();\r\n\t},\r\n\tmethods: {\r\n\t\t// 加载活动配置\r\n\t\tasync loadActivities() {\r\n\t\t\ttry {\r\n\t\t\t\tconst result = await systemAPI.getActivities()\r\n\t\t\t\tthis.activities = result.data || {}\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('获取活动配置失败:', error)\r\n\t\t\t\t// 使用默认配置\r\n\t\t\t\tthis.activities = {\r\n\t\t\t\t\tdaily_sign: { enabled: true },\r\n\t\t\t\t\tlucky_wheel: { enabled: false }\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 加载轮播图\r\n\t\tasync loadBanners() {\r\n\t\t\ttry {\r\n\t\t\t\tconst response = await systemAPI.getBanners()\r\n\t\t\t\tthis.banners = response.data || []\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('获取轮播图失败:', error)\r\n\t\t\t\t// 使用模拟数据作为降级方案\r\n\t\t\t\tthis.banners = [\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tid: 1,\r\n\t\t\t\t\t\temoji: '🛒',\r\n\t\t\t\t\t\ttitle: '积分商城上线',\r\n\t\t\t\t\t\tdescription: '精品好物等你兑换',\r\n\t\t\t\t\t\turl: '/pages/mall/index'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tid: 2,\r\n\t\t\t\t\t\temoji: '📅',\r\n\t\t\t\t\t\ttitle: '每日签到送积分',\r\n\t\t\t\t\t\tdescription: '坚持签到获得更多奖励',\r\n\t\t\t\t\t\turl: '/pages/activities/sign'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tid: 3,\r\n\t\t\t\t\t\temoji: '🎰',\r\n\t\t\t\t\t\ttitle: '幸运转盘抽大奖',\r\n\t\t\t\t\t\tdescription: '每天3次免费抽奖机会',\r\n\t\t\t\t\t\turl: '/pages/activities/wheel'\r\n\t\t\t\t\t}\r\n\t\t\t\t]\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 加载推荐商品\r\n\t\tasync loadRecommendProducts() {\r\n\t\t\ttry {\r\n\t\t\t\tconst response = await productAPI.getList({\r\n\t\t\t\t\tlimit: 4,\r\n\t\t\t\t\tstatus: 1,\r\n\t\t\t\t\tsort: 'sales' // 按销量排序\r\n\t\t\t\t})\r\n\t\t\t\tconst products = response.data.list || []\r\n\r\n\t\t\t\t// 处理商品图片URL\r\n\t\t\t\tproducts.forEach(product => {\r\n\t\t\t\t\tif (product.image) {\r\n\t\t\t\t\t\tproduct.image = getImageUrl(product.image)\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (product.images && Array.isArray(product.images)) {\r\n\t\t\t\t\t\tproduct.images = product.images.map(img => getImageUrl(img))\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\r\n\t\t\t\tthis.recommendProducts = products\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('获取推荐商品失败:', error)\r\n\t\t\t\t// 使用模拟数据作为降级方案\r\n\t\t\t\tthis.recommendProducts = [\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tid: 1,\r\n\t\t\t\t\t\tname: '精美水杯',\r\n\t\t\t\t\t\temoji: '☕',\r\n\t\t\t\t\t\tpoints_price: 200\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tid: 2,\r\n\t\t\t\t\t\tname: '蓝牙耳机',\r\n\t\t\t\t\t\temoji: '🎧',\r\n\t\t\t\t\t\tpoints_price: 800\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tid: 3,\r\n\t\t\t\t\t\tname: '充电宝',\r\n\t\t\t\t\t\temoji: '🔋',\r\n\t\t\t\t\t\tpoints_price: 500\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tid: 4,\r\n\t\t\t\t\t\tname: '手机支架',\r\n\t\t\t\t\t\temoji: '📱',\r\n\t\t\t\t\t\tpoints_price: 150\r\n\t\t\t\t\t}\r\n\t\t\t\t]\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 处理轮播图点击\r\n\t\thandleBannerClick(banner) {\r\n\t\t\tif (banner.url) {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: banner.url\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 跳转到赚积分页面\r\n\t\tgoToEarn() {\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: '/pages/points/earn'\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t// 跳转到签到\r\n\t\tgoToSign() {\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: '/pages/activities/sign'\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t// 跳转到转盘\r\n\t\tgoToWheel() {\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: '/pages/activities/wheel'\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t// 跳转到积分页面\r\n\t\tgoToPoints() {\r\n\t\t\tuni.switchTab({\r\n\t\t\t\turl: '/pages/points/index'\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t// 跳转到商城\r\n\t\tgoToMall() {\r\n\t\t\tuni.switchTab({\r\n\t\t\t\turl: '/pages/mall/index'\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t// 跳转到商品详情\r\n\t\tgoToDetail(productId) {\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: `/pages/mall/detail?id=${productId}`\r\n\t\t\t});\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.home-container {\r\n\tbackground: #f8f8f8;\r\n\tmin-height: 100vh;\r\n}\r\n\r\n.banner-swiper {\r\n\theight: 200px;\r\n\tmargin-bottom: 15px;\r\n}\r\n\r\n.banner-card {\r\n\twidth: 100%;\r\n\theight: 100%;\r\n\tposition: relative;\r\n\tmargin: 0;\r\n\toverflow: hidden;\r\n}\r\n\r\n.banner-bg-image {\r\n\tposition: absolute;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\twidth: 100%;\r\n\theight: 100%;\r\n\tz-index: 1;\r\n}\r\n\r\n.banner-overlay {\r\n\tposition: relative;\r\n\tz-index: 2;\r\n\twidth: 100%;\r\n\theight: 100%;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tflex-direction: column;\r\n}\r\n\r\n.banner-icon {\r\n\tfont-size: 48px;\r\n\tmargin-bottom: 10px;\r\n}\r\n\r\n.banner-content {\r\n\ttext-align: center;\r\n}\r\n\r\n.banner-title {\r\n\tdisplay: block;\r\n\tfont-size: 18px;\r\n\tfont-weight: bold;\r\n\tmargin-bottom: 5px;\r\n}\r\n\r\n.banner-desc {\r\n\tdisplay: block;\r\n\tfont-size: 14px;\r\n\topacity: 0.9;\r\n}\r\n\r\n.quick-nav {\r\n\tdisplay: grid;\r\n\tgrid-template-columns: repeat(4, 1fr);\r\n\tgap: 15px;\r\n\tpadding: 20px 15px;\r\n\tbackground: #ffffff;\r\n\tmargin-bottom: 15px;\r\n}\r\n\r\n.nav-item {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tposition: relative;\r\n}\r\n\r\n.nav-item.highlight .nav-icon {\r\n\tbackground: linear-gradient(135deg, #ff6b35 0%, #ff8c42 100%);\r\n}\r\n\r\n.nav-item.highlight .nav-emoji {\r\n\tfilter: brightness(0) invert(1);\r\n}\r\n\r\n.nav-badge {\r\n\tposition: absolute;\r\n\ttop: -5px;\r\n\tright: 5px;\r\n\tbackground: #ff4757;\r\n\tcolor: white;\r\n\tfont-size: 10px;\r\n\tpadding: 2px 6px;\r\n\tborder-radius: 8px;\r\n\ttransform: scale(0.8);\r\n}\r\n\r\n.nav-icon {\r\n\twidth: 50px;\r\n\theight: 50px;\r\n\tbackground: rgba(102, 126, 234, 0.1);\r\n\tborder-radius: 25px;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tmargin-bottom: 8px;\r\n}\r\n\r\n.nav-emoji {\r\n\tfont-size: 24px;\r\n}\r\n\r\n.nav-text {\r\n\tfont-size: 12px;\r\n\tcolor: #333;\r\n}\r\n\r\n.recommend-section,\r\n.earn-section {\r\n\tbackground: #ffffff;\r\n\tmargin-bottom: 15px;\r\n\tpadding: 20px 15px;\r\n}\r\n\r\n.section-header {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tmargin-bottom: 15px;\r\n}\r\n\r\n.section-title {\r\n\tfont-size: 16px;\r\n\tfont-weight: 500;\r\n\tcolor: #333;\r\n}\r\n\r\n.more-btn {\r\n\tfont-size: 12px;\r\n\tcolor: #667eea;\r\n}\r\n\r\n.product-scroll {\r\n\twhite-space: nowrap;\r\n}\r\n\r\n.product-list {\r\n\tdisplay: flex;\r\n\tgap: 15px;\r\n}\r\n\r\n.product-card {\r\n\twidth: 120px;\r\n\tflex-shrink: 0;\r\n}\r\n\r\n.product-image {\r\n\twidth: 120px;\r\n\theight: 120px;\r\n\tborder-radius: 8px;\r\n\tmargin-bottom: 8px;\r\n\tbackground: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n}\r\n\r\n.product-emoji {\r\n\tfont-size: 48px;\r\n}\r\n\r\n.product-img {\r\n\twidth: 100%;\r\n\theight: 100%;\r\n\tborder-radius: 8px;\r\n}\r\n\r\n.product-info {\r\n\ttext-align: center;\r\n}\r\n\r\n.product-name {\r\n\tdisplay: block;\r\n\tfont-size: 12px;\r\n\tcolor: #333;\r\n\tmargin-bottom: 4px;\r\n\toverflow: hidden;\r\n\ttext-overflow: ellipsis;\r\n\twhite-space: nowrap;\r\n}\r\n\r\n.product-price {\r\n\tdisplay: block;\r\n\tfont-size: 14px;\r\n\tfont-weight: 500;\r\n\tcolor: #667eea;\r\n}\r\n\r\n.earn-list {\r\n\t/* 赚积分列表样式 */\r\n}\r\n\r\n.earn-item {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tpadding: 15px 0;\r\n\tborder-bottom: 1px solid #f5f5f5;\r\n}\r\n\r\n.earn-item:last-child {\r\n\tborder-bottom: none;\r\n}\r\n\r\n.earn-icon {\r\n\twidth: 40px;\r\n\theight: 40px;\r\n\tbackground: rgba(102, 126, 234, 0.1);\r\n\tborder-radius: 20px;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tmargin-right: 15px;\r\n}\r\n\r\n.earn-emoji {\r\n\tfont-size: 20px;\r\n}\r\n\r\n.earn-info {\r\n\tflex: 1;\r\n}\r\n\r\n.earn-title {\r\n\tdisplay: block;\r\n\tfont-size: 14px;\r\n\tcolor: #333;\r\n\tmargin-bottom: 4px;\r\n}\r\n\r\n.earn-desc {\r\n\tdisplay: block;\r\n\tfont-size: 12px;\r\n\tcolor: #999;\r\n}\r\n\r\n.earn-points {\r\n\tfont-size: 14px;\r\n\tfont-weight: 500;\r\n\tcolor: #67c23a;\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'D:/app/miniprogram/SIYU/pages/index/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["systemAPI", "uni", "productAPI", "getImageUrl"], "mappings": ";;;;AAkIA,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,SAAS,CAAE;AAAA,MACX,mBAAmB,CAAE;AAAA,MACrB,YAAY,CAAC;AAAA,IACd;AAAA,EACA;AAAA,EACD,SAAS;AACR,SAAK,eAAc;AACnB,SAAK,YAAW;AAChB,SAAK,sBAAqB;AAAA,EAC1B;AAAA,EACD,SAAS;AAAA;AAAA,IAER,MAAM,iBAAiB;AACtB,UAAI;AACH,cAAM,SAAS,MAAMA,UAAS,UAAC,cAAc;AAC7C,aAAK,aAAa,OAAO,QAAQ,CAAC;AAAA,MACjC,SAAO,OAAO;AACfC,sBAAAA,MAAA,MAAA,SAAA,gCAAc,aAAa,KAAK;AAEhC,aAAK,aAAa;AAAA,UACjB,YAAY,EAAE,SAAS,KAAM;AAAA,UAC7B,aAAa,EAAE,SAAS,MAAM;AAAA,QAC/B;AAAA,MACD;AAAA,IACA;AAAA;AAAA,IAGD,MAAM,cAAc;AACnB,UAAI;AACH,cAAM,WAAW,MAAMD,UAAS,UAAC,WAAW;AAC5C,aAAK,UAAU,SAAS,QAAQ,CAAC;AAAA,MAChC,SAAO,OAAO;AACfC,sBAAAA,MAAA,MAAA,SAAA,gCAAc,YAAY,KAAK;AAE/B,aAAK,UAAU;AAAA,UACd;AAAA,YACC,IAAI;AAAA,YACJ,OAAO;AAAA,YACP,OAAO;AAAA,YACP,aAAa;AAAA,YACb,KAAK;AAAA,UACL;AAAA,UACD;AAAA,YACC,IAAI;AAAA,YACJ,OAAO;AAAA,YACP,OAAO;AAAA,YACP,aAAa;AAAA,YACb,KAAK;AAAA,UACL;AAAA,UACD;AAAA,YACC,IAAI;AAAA,YACJ,OAAO;AAAA,YACP,OAAO;AAAA,YACP,aAAa;AAAA,YACb,KAAK;AAAA,UACN;AAAA,QACD;AAAA,MACD;AAAA,IACA;AAAA;AAAA,IAGD,MAAM,wBAAwB;AAC7B,UAAI;AACH,cAAM,WAAW,MAAMC,UAAU,WAAC,QAAQ;AAAA,UACzC,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,MAAM;AAAA;AAAA,SACN;AACD,cAAM,WAAW,SAAS,KAAK,QAAQ,CAAC;AAGxC,iBAAS,QAAQ,aAAW;AAC3B,cAAI,QAAQ,OAAO;AAClB,oBAAQ,QAAQC,0BAAY,QAAQ,KAAK;AAAA,UAC1C;AACA,cAAI,QAAQ,UAAU,MAAM,QAAQ,QAAQ,MAAM,GAAG;AACpD,oBAAQ,SAAS,QAAQ,OAAO,IAAI,SAAOA,cAAAA,YAAY,GAAG,CAAC;AAAA,UAC5D;AAAA,SACA;AAED,aAAK,oBAAoB;AAAA,MACxB,SAAO,OAAO;AACfF,sBAAAA,MAAA,MAAA,SAAA,gCAAc,aAAa,KAAK;AAEhC,aAAK,oBAAoB;AAAA,UACxB;AAAA,YACC,IAAI;AAAA,YACJ,MAAM;AAAA,YACN,OAAO;AAAA,YACP,cAAc;AAAA,UACd;AAAA,UACD;AAAA,YACC,IAAI;AAAA,YACJ,MAAM;AAAA,YACN,OAAO;AAAA,YACP,cAAc;AAAA,UACd;AAAA,UACD;AAAA,YACC,IAAI;AAAA,YACJ,MAAM;AAAA,YACN,OAAO;AAAA,YACP,cAAc;AAAA,UACd;AAAA,UACD;AAAA,YACC,IAAI;AAAA,YACJ,MAAM;AAAA,YACN,OAAO;AAAA,YACP,cAAc;AAAA,UACf;AAAA,QACD;AAAA,MACD;AAAA,IACA;AAAA;AAAA,IAGD,kBAAkB,QAAQ;AACzB,UAAI,OAAO,KAAK;AACfA,sBAAAA,MAAI,WAAW;AAAA,UACd,KAAK,OAAO;AAAA,QACb,CAAC;AAAA,MACF;AAAA,IACA;AAAA;AAAA,IAGD,WAAW;AACVA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACN,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,WAAW;AACVA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACN,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,YAAY;AACXA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACN,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,aAAa;AACZA,oBAAAA,MAAI,UAAU;AAAA,QACb,KAAK;AAAA,MACN,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,WAAW;AACVA,oBAAAA,MAAI,UAAU;AAAA,QACb,KAAK;AAAA,MACN,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,WAAW,WAAW;AACrBA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK,yBAAyB,SAAS;AAAA,MACxC,CAAC;AAAA,IACF;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACxSA,GAAG,WAAW,eAAe;"}