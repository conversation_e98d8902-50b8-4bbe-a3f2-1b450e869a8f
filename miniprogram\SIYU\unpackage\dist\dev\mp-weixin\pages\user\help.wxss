
.help-container.data-v-7818abf9 {
	min-height: 100vh;
	background-color: #f5f5f5;
}

/* 搜索区域 */
.search-section.data-v-7818abf9 {
	background: white;
	padding: 30rpx;
	margin-bottom: 20rpx;
}
.search-box.data-v-7818abf9 {
	display: flex;
	align-items: center;
	background: #f8f9fa;
	border-radius: 50rpx;
	padding: 20rpx 30rpx;
}
.search-input.data-v-7818abf9 {
	flex: 1;
	margin-left: 20rpx;
	font-size: 28rpx;
	color: #333;
}

/* 通用区域样式 */
.section.data-v-7818abf9 {
	background: white;
	margin-bottom: 20rpx;
	padding: 30rpx;
}
.section-title.data-v-7818abf9 {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 30rpx;
}

/* FAQ样式 */
.faq-list.data-v-7818abf9 {
}
.faq-item.data-v-7818abf9 {
	border-bottom: 1rpx solid #f0f0f0;
	padding: 30rpx 0;
}
.faq-item.data-v-7818abf9:last-child {
	border-bottom: none;
}
.faq-question.data-v-7818abf9 {
	display: flex;
	justify-content: space-between;
	align-items: center;
	cursor: pointer;
}
.question-text.data-v-7818abf9 {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
}
.faq-answer.data-v-7818abf9 {
	margin-top: 20rpx;
	padding-top: 20rpx;
	border-top: 1rpx solid #f8f9fa;
}
.answer-text.data-v-7818abf9 {
	font-size: 26rpx;
	color: #666;
	line-height: 1.6;
}

/* 联系我们样式 */
.contact-list.data-v-7818abf9 {
}
.contact-item.data-v-7818abf9 {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
}
.contact-item.data-v-7818abf9:last-child {
	border-bottom: none;
}
.contact-left.data-v-7818abf9 {
	display: flex;
	align-items: center;
}
.contact-text.data-v-7818abf9 {
	font-size: 28rpx;
	color: #333;
	margin-left: 20rpx;
}
.contact-value.data-v-7818abf9 {
	font-size: 26rpx;
	color: #667eea;
}

/* 反馈按钮 */
.feedback-btn.data-v-7818abf9 {
	display: flex;
	align-items: center;
	justify-content: center;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	border: none;
	border-radius: 50rpx;
	padding: 30rpx;
	width: 100%;
}
.feedback-text.data-v-7818abf9 {
	margin-left: 10rpx;
	font-size: 28rpx;
}
