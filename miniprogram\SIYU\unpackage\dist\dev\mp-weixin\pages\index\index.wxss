
.home-container.data-v-1cf27b2a {
	background: #f8f8f8;
	min-height: 100vh;
}
.banner-swiper.data-v-1cf27b2a {
	height: 200px;
	margin-bottom: 15px;
}
.banner-card.data-v-1cf27b2a {
	width: 100%;
	height: 100%;
	position: relative;
	margin: 0;
	overflow: hidden;
}
.banner-bg-image.data-v-1cf27b2a {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 1;
}
.banner-overlay.data-v-1cf27b2a {
	position: relative;
	z-index: 2;
	width: 100%;
	height: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
	flex-direction: column;
}
.banner-icon.data-v-1cf27b2a {
	font-size: 48px;
	margin-bottom: 10px;
}
.banner-content.data-v-1cf27b2a {
	text-align: center;
}
.banner-title.data-v-1cf27b2a {
	display: block;
	font-size: 18px;
	font-weight: bold;
	margin-bottom: 5px;
}
.banner-desc.data-v-1cf27b2a {
	display: block;
	font-size: 14px;
	opacity: 0.9;
}
.quick-nav.data-v-1cf27b2a {
	display: grid;
	grid-template-columns: repeat(4, 1fr);
	gap: 15px;
	padding: 20px 15px;
	background: #ffffff;
	margin-bottom: 15px;
}
.nav-item.data-v-1cf27b2a {
	display: flex;
	flex-direction: column;
	align-items: center;
	position: relative;
}
.nav-item.highlight .nav-icon.data-v-1cf27b2a {
	background: linear-gradient(135deg, #ff6b35 0%, #ff8c42 100%);
}
.nav-item.highlight .nav-emoji.data-v-1cf27b2a {
	filter: brightness(0) invert(1);
}
.nav-badge.data-v-1cf27b2a {
	position: absolute;
	top: -5px;
	right: 5px;
	background: #ff4757;
	color: white;
	font-size: 10px;
	padding: 2px 6px;
	border-radius: 8px;
	transform: scale(0.8);
}
.nav-icon.data-v-1cf27b2a {
	width: 50px;
	height: 50px;
	background: rgba(102, 126, 234, 0.1);
	border-radius: 25px;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 8px;
}
.nav-emoji.data-v-1cf27b2a {
	font-size: 24px;
}
.nav-text.data-v-1cf27b2a {
	font-size: 12px;
	color: #333;
}
.recommend-section.data-v-1cf27b2a,
.earn-section.data-v-1cf27b2a {
	background: #ffffff;
	margin-bottom: 15px;
	padding: 20px 15px;
}
.section-header.data-v-1cf27b2a {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 15px;
}
.section-title.data-v-1cf27b2a {
	font-size: 16px;
	font-weight: 500;
	color: #333;
}
.more-btn.data-v-1cf27b2a {
	font-size: 12px;
	color: #667eea;
}
.product-scroll.data-v-1cf27b2a {
	white-space: nowrap;
}
.product-list.data-v-1cf27b2a {
	display: flex;
	gap: 15px;
}
.product-card.data-v-1cf27b2a {
	width: 120px;
	flex-shrink: 0;
}
.product-image.data-v-1cf27b2a {
	width: 120px;
	height: 120px;
	border-radius: 8px;
	margin-bottom: 8px;
	background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
	display: flex;
	align-items: center;
	justify-content: center;
}
.product-emoji.data-v-1cf27b2a {
	font-size: 48px;
}
.product-img.data-v-1cf27b2a {
	width: 100%;
	height: 100%;
	border-radius: 8px;
}
.product-info.data-v-1cf27b2a {
	text-align: center;
}
.product-name.data-v-1cf27b2a {
	display: block;
	font-size: 12px;
	color: #333;
	margin-bottom: 4px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
.product-price.data-v-1cf27b2a {
	display: block;
	font-size: 14px;
	font-weight: 500;
	color: #667eea;
}
.earn-list.data-v-1cf27b2a {
	/* 赚积分列表样式 */
}
.earn-item.data-v-1cf27b2a {
	display: flex;
	align-items: center;
	padding: 15px 0;
	border-bottom: 1px solid #f5f5f5;
}
.earn-item.data-v-1cf27b2a:last-child {
	border-bottom: none;
}
.earn-icon.data-v-1cf27b2a {
	width: 40px;
	height: 40px;
	background: rgba(102, 126, 234, 0.1);
	border-radius: 20px;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 15px;
}
.earn-emoji.data-v-1cf27b2a {
	font-size: 20px;
}
.earn-info.data-v-1cf27b2a {
	flex: 1;
}
.earn-title.data-v-1cf27b2a {
	display: block;
	font-size: 14px;
	color: #333;
	margin-bottom: 4px;
}
.earn-desc.data-v-1cf27b2a {
	display: block;
	font-size: 12px;
	color: #999;
}
.earn-points.data-v-1cf27b2a {
	font-size: 14px;
	font-weight: 500;
	color: #67c23a;
}
