"use strict";
const common_vendor = require("../../common/vendor.js");
const static_data_regions = require("../../static/data/regions.js");
const _sfc_main = {
  name: "RegionPicker",
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    value: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      currentTab: 0,
      selectedProvince: null,
      selectedCity: null,
      selectedDistrict: null,
      provinces: static_data_regions.regions,
      cities: [],
      districts: []
    };
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.initData();
      }
    },
    value: {
      handler(newVal) {
        if (newVal && newVal.province) {
          this.initFromValue(newVal);
        }
      },
      immediate: true
    }
  },
  methods: {
    // 初始化数据
    initData() {
      if (this.value && this.value.province) {
        this.initFromValue(this.value);
      } else {
        this.currentTab = 0;
        this.selectedProvince = null;
        this.selectedCity = null;
        this.selectedDistrict = null;
        this.cities = [];
        this.districts = [];
      }
    },
    // 从传入值初始化
    initFromValue(value) {
      this.selectedProvince = this.provinces.find((p) => p.name === value.province);
      if (this.selectedProvince) {
        this.cities = static_data_regions.getCitiesByProvince(this.selectedProvince.code);
        if (value.city) {
          this.selectedCity = this.cities.find((c) => c.name === value.city);
          if (this.selectedCity) {
            this.districts = static_data_regions.getDistrictsByCity(this.selectedCity.code);
            if (value.district) {
              this.selectedDistrict = this.districts.find((d) => d.name === value.district);
              this.currentTab = 2;
            } else {
              this.currentTab = 1;
            }
          } else {
            this.currentTab = 1;
          }
        } else {
          this.currentTab = 0;
        }
      }
    },
    // 切换标签
    switchTab(index) {
      this.currentTab = index;
    },
    // 选择省份
    selectProvince(province) {
      this.selectedProvince = province;
      this.selectedCity = null;
      this.selectedDistrict = null;
      this.cities = static_data_regions.getCitiesByProvince(province.code);
      this.districts = [];
      this.currentTab = 1;
    },
    // 选择城市
    selectCity(city) {
      this.selectedCity = city;
      this.selectedDistrict = null;
      this.districts = static_data_regions.getDistrictsByCity(city.code);
      this.currentTab = 2;
    },
    // 选择区县
    selectDistrict(district) {
      this.selectedDistrict = district;
      setTimeout(() => {
        this.confirm();
      }, 300);
    },
    // 确认选择
    confirm() {
      if (!this.selectedProvince || !this.selectedCity || !this.selectedDistrict) {
        common_vendor.index.showToast({
          title: "请选择完整的地区信息",
          icon: "none"
        });
        return;
      }
      const result = {
        province: this.selectedProvince.name,
        city: this.selectedCity.name,
        district: this.selectedDistrict.name,
        provinceCode: this.selectedProvince.code,
        cityCode: this.selectedCity.code,
        districtCode: this.selectedDistrict.code
      };
      this.$emit("confirm", result);
      this.close();
    },
    // 关闭选择器
    close() {
      this.$emit("close");
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $props.visible
  }, $props.visible ? {
    b: common_vendor.o((...args) => $options.close && $options.close(...args))
  } : {}, {
    c: common_vendor.o((...args) => $options.close && $options.close(...args)),
    d: common_vendor.o((...args) => $options.confirm && $options.confirm(...args)),
    e: common_vendor.t($data.selectedProvince ? $data.selectedProvince.name : "请选择"),
    f: $data.currentTab === 0 ? 1 : "",
    g: common_vendor.o(($event) => $options.switchTab(0)),
    h: $data.selectedProvince
  }, $data.selectedProvince ? {
    i: common_vendor.t($data.selectedCity ? $data.selectedCity.name : "请选择"),
    j: $data.currentTab === 1 ? 1 : "",
    k: common_vendor.o(($event) => $options.switchTab(1))
  } : {}, {
    l: $data.selectedCity
  }, $data.selectedCity ? {
    m: common_vendor.t($data.selectedDistrict ? $data.selectedDistrict.name : "请选择"),
    n: $data.currentTab === 2 ? 1 : "",
    o: common_vendor.o(($event) => $options.switchTab(2))
  } : {}, {
    p: $data.currentTab === 0
  }, $data.currentTab === 0 ? {
    q: common_vendor.f($data.provinces, (item, k0, i0) => {
      return common_vendor.e({
        a: common_vendor.t(item.name),
        b: item.code === ($data.selectedProvince && $data.selectedProvince.code)
      }, item.code === ($data.selectedProvince && $data.selectedProvince.code) ? {} : {}, {
        c: item.code === ($data.selectedProvince && $data.selectedProvince.code) ? 1 : "",
        d: item.code,
        e: common_vendor.o(($event) => $options.selectProvince(item), item.code)
      });
    })
  } : {}, {
    r: $data.currentTab === 1
  }, $data.currentTab === 1 ? {
    s: common_vendor.f($data.cities, (item, k0, i0) => {
      return common_vendor.e({
        a: common_vendor.t(item.name),
        b: item.code === ($data.selectedCity && $data.selectedCity.code)
      }, item.code === ($data.selectedCity && $data.selectedCity.code) ? {} : {}, {
        c: item.code === ($data.selectedCity && $data.selectedCity.code) ? 1 : "",
        d: item.code,
        e: common_vendor.o(($event) => $options.selectCity(item), item.code)
      });
    })
  } : {}, {
    t: $data.currentTab === 2
  }, $data.currentTab === 2 ? {
    v: common_vendor.f($data.districts, (item, k0, i0) => {
      return common_vendor.e({
        a: common_vendor.t(item.name),
        b: item.code === ($data.selectedDistrict && $data.selectedDistrict.code)
      }, item.code === ($data.selectedDistrict && $data.selectedDistrict.code) ? {} : {}, {
        c: item.code === ($data.selectedDistrict && $data.selectedDistrict.code) ? 1 : "",
        d: item.code,
        e: common_vendor.o(($event) => $options.selectDistrict(item), item.code)
      });
    })
  } : {}, {
    w: $props.visible ? 1 : ""
  });
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-fef920e2"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/region-picker/region-picker.js.map
