<?php
/**
 * 创建全新的AddressController
 */

header('Content-Type: application/json; charset=utf-8');

try {
    $targetPath = __DIR__ . '/api/controllers/api/AddressController.php';
    
    // 全新的、简化的AddressController代码
    $newControllerCode = '<?php

declare(strict_types=1);

require_once __DIR__ . \'/../../core/Database.php\';
require_once __DIR__ . \'/../../core/Response.php\';
require_once __DIR__ . \'/../../middleware/AuthMiddleware.php\';

class AddressController
{
    private Database $db;

    public function __construct()
    {
        $this->db = Database::getInstance();
    }

    /**
     * 获取用户地址列表
     */
    public function index(): void
    {
        try {
            // 验证认证
            $authMiddleware = new AuthMiddleware();
            $authMiddleware->handle();
            
            $userId = AuthMiddleware::getCurrentUserId();
            if (!$userId) {
                Response::unauthorized(\'请先登录\');
                return;
            }

            $sql = "SELECT * FROM user_addresses WHERE user_id = ? ORDER BY is_default DESC, created_at DESC";
            $stmt = $this->db->query($sql, [$userId]);
            $addresses = $stmt->fetchAll();

            // 格式化数据
            foreach ($addresses as &$address) {
                $address[\'id\'] = (int)$address[\'id\'];
                $address[\'user_id\'] = (int)$address[\'user_id\'];
                $address[\'is_default\'] = (bool)$address[\'is_default\'];
                $address[\'full_address\'] = $address[\'province\'] . $address[\'city\'] . $address[\'district\'] . $address[\'detail\'];
            }

            Response::success($addresses, \'获取地址列表成功\');

        } catch (Exception $e) {
            error_log("获取地址列表失败: " . $e->getMessage());
            Response::businessError(\'获取地址列表失败，请稍后重试\');
        }
    }

    /**
     * 创建地址
     */
    public function create(): void
    {
        try {
            // 验证认证
            $authMiddleware = new AuthMiddleware();
            $authMiddleware->handle();
            
            $userId = AuthMiddleware::getCurrentUserId();
            if (!$userId) {
                Response::unauthorized(\'请先登录\');
                return;
            }

            $input = json_decode(file_get_contents(\'php://input\'), true);

            // 验证必需参数
            $required = [\'name\', \'phone\', \'province\', \'city\', \'district\', \'detail\'];
            foreach ($required as $field) {
                if (empty($input[$field])) {
                    Response::validationError([$field => \'此字段不能为空\']);
                    return;
                }
            }

            // 验证手机号格式
            if (!preg_match(\'/^1[3-9]\\d{9}$/\', $input[\'phone\'])) {
                Response::validationError([\'phone\' => \'手机号格式不正确\']);
                return;
            }

            $this->db->beginTransaction();

            // 如果设置为默认地址，先取消其他默认地址
            $isDefault = !empty($input[\'is_default\']);
            if ($isDefault) {
                $this->db->query("UPDATE user_addresses SET is_default = 0 WHERE user_id = ?", [$userId]);
            }

            // 创建地址
            $addressData = [
                \'user_id\' => $userId,
                \'name\' => trim($input[\'name\']),
                \'phone\' => trim($input[\'phone\']),
                \'province\' => trim($input[\'province\']),
                \'city\' => trim($input[\'city\']),
                \'district\' => trim($input[\'district\']),
                \'detail\' => trim($input[\'detail\']),
                \'is_default\' => $isDefault ? 1 : 0,
                \'created_at\' => date(\'Y-m-d H:i:s\')
            ];

            $addressId = $this->db->insert(\'user_addresses\', $addressData);

            $this->db->commit();

            Response::success([
                \'address_id\' => $addressId
            ], \'地址创建成功\');

        } catch (Exception $e) {
            $this->db->rollback();
            error_log("创建地址失败: " . $e->getMessage());
            Response::businessError(\'创建地址失败，请稍后重试\');
        }
    }

    /**
     * 获取默认地址
     */
    public function getDefault(): void
    {
        try {
            // 验证认证
            $authMiddleware = new AuthMiddleware();
            $authMiddleware->handle();
            
            $userId = AuthMiddleware::getCurrentUserId();
            if (!$userId) {
                Response::unauthorized(\'请先登录\');
                return;
            }

            $sql = "SELECT * FROM user_addresses WHERE user_id = ? AND is_default = 1 LIMIT 1";
            $stmt = $this->db->query($sql, [$userId]);
            $addresses = $stmt->fetchAll();

            if (empty($addresses)) {
                Response::success(null, \'暂无默认地址\');
                return;
            }

            $address = $addresses[0];
            $address[\'id\'] = (int)$address[\'id\'];
            $address[\'user_id\'] = (int)$address[\'user_id\'];
            $address[\'is_default\'] = (bool)$address[\'is_default\'];
            $address[\'full_address\'] = $address[\'province\'] . $address[\'city\'] . $address[\'district\'] . $address[\'detail\'];

            Response::success($address, \'获取默认地址成功\');

        } catch (Exception $e) {
            error_log("获取默认地址失败: " . $e->getMessage());
            Response::businessError(\'获取默认地址失败，请稍后重试\');
        }
    }
}
';

    // 写入新文件
    $result = file_put_contents($targetPath, $newControllerCode);
    
    if ($result === false) {
        throw new Exception('无法写入AddressController.php文件');
    }
    
    echo json_encode([
        'status' => 'success',
        'message' => '新的AddressController.php创建成功',
        'bytes_written' => $result,
        'file_path' => $targetPath,
        'next_step' => '请恢复routes.php中的地址路由'
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        'status' => 'error',
        'message' => '创建AddressController失败: ' . $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ], JSON_UNESCAPED_UNICODE);
}
?>
