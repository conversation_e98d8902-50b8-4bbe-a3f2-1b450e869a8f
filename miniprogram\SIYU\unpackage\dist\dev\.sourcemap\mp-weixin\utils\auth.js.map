{"version": 3, "file": "auth.js", "sources": ["utils/auth.js"], "sourcesContent": ["/**\n * 登录认证工具类\n */\n\nimport { authAPI } from '@/api/index.js'\n\nconst AuthUtils = {\n\t/**\n\t * 检查是否已登录\n\t */\n\tisLoggedIn() {\n\t\tconst token = uni.getStorageSync('token')\n\t\tconst userInfo = uni.getStorageSync('userInfo')\n\t\treturn !!(token && userInfo)\n\t},\n\n\t/**\n\t * 获取当前用户信息\n\t */\n\tgetCurrentUser() {\n\t\treturn uni.getStorageSync('userInfo')\n\t},\n\n\t/**\n\t * 获取当前token\n\t */\n\tgetToken() {\n\t\treturn uni.getStorageSync('token')\n\t},\n\n\t/**\n\t * 清除登录数据\n\t */\n\tclearLoginData() {\n\t\tuni.removeStorageSync('token')\n\t\tuni.removeStorageSync('userInfo')\n\t},\n\n\t/**\n\t * 保存登录数据\n\t */\n\tsaveLoginData(token, userInfo) {\n\t\tuni.setStorageSync('token', token)\n\t\tuni.setStorageSync('userInfo', userInfo)\n\t},\n\n\t/**\n\t * 检查登录状态，如果未登录则跳转到登录页\n\t */\n\tcheckLoginAndRedirect(showModal = true) {\n\t\tif (!this.isLoggedIn()) {\n\t\t\tif (showModal) {\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '提示',\n\t\t\t\t\tcontent: '请先登录',\n\t\t\t\t\tconfirmText: '去登录',\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\tthis.navigateToLogin()\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t} else {\n\t\t\t\tthis.navigateToLogin()\n\t\t\t}\n\t\t\treturn false\n\t\t}\n\t\treturn true\n\t},\n\n\t/**\n\t * 跳转到登录页\n\t */\n\tnavigateToLogin() {\n\t\tuni.navigateTo({\n\t\t\turl: '/pages/login/login'\n\t\t})\n\t},\n\n\t/**\n\t * 验证token有效性\n\t */\n\tasync verifyToken() {\n\t\ttry {\n\t\t\tconst token = this.getToken()\n\t\t\tif (!token) {\n\t\t\t\treturn false\n\t\t\t}\n\n\t\t\tconst result = await authAPI.verifyToken(token)\n\t\t\tif (result.data && result.data.valid) {\n\t\t\t\t// 更新用户信息\n\t\t\t\tif (result.data.user) {\n\t\t\t\t\tuni.setStorageSync('userInfo', result.data.user)\n\t\t\t\t}\n\t\t\t\treturn true\n\t\t\t} else {\n\t\t\t\tthis.clearLoginData()\n\t\t\t\treturn false\n\t\t\t}\n\t\t} catch (error) {\n\t\t\tconsole.error('验证token失败:', error)\n\t\t\t// 网络错误时不清除数据\n\t\t\treturn this.isLoggedIn()\n\t\t}\n\t},\n\n\t/**\n\t * 微信登录\n\t */\n\tasync wechatLogin(userInfo = null) {\n\t\ttry {\n\t\t\t// 获取微信登录code\n\t\t\tconst loginRes = await this.getWechatCode()\n\n\t\t\t// 格式化用户信息\n\t\t\tconst formattedUserInfo = userInfo ? {\n\t\t\t\tnickname: userInfo.nickName,\n\t\t\t\tavatar: userInfo.avatarUrl,\n\t\t\t\tgender: userInfo.gender,\n\t\t\t\tcity: userInfo.city,\n\t\t\t\tprovince: userInfo.province,\n\t\t\t\tcountry: userInfo.country\n\t\t\t} : null\n\n\t\t\t// 调用登录API\n\t\t\tconst result = await authAPI.login({\n\t\t\t\tcode: loginRes.code,\n\t\t\t\tuserInfo: formattedUserInfo\n\t\t\t})\n\n\t\t\t// 保存登录数据\n\t\t\tthis.saveLoginData(result.data.token, result.data.user)\n\n\t\t\treturn result.data\n\t\t} catch (error) {\n\t\t\tconsole.error('微信登录失败:', error)\n\t\t\tthrow error\n\t\t}\n\t},\n\n\t/**\n\t * 获取微信登录code\n\t */\n\tgetWechatCode() {\n\t\treturn new Promise((resolve, reject) => {\n\t\t\tuni.login({\n\t\t\t\tprovider: 'weixin',\n\t\t\t\tsuccess: resolve,\n\t\t\t\tfail: reject\n\t\t\t})\n\t\t})\n\t},\n\n\t/**\n\t * 获取微信用户信息\n\t */\n\tgetWechatUserInfo() {\n\t\treturn new Promise((resolve, reject) => {\n\t\t\tuni.getUserProfile({\n\t\t\t\tdesc: '用于完善用户资料',\n\t\t\t\tsuccess: resolve,\n\t\t\t\tfail: reject\n\t\t\t})\n\t\t})\n\t},\n\n\t/**\n\t * 退出登录\n\t */\n\tasync logout() {\n\t\ttry {\n\t\t\tconst token = this.getToken()\n\t\t\tif (token) {\n\t\t\t\tawait authAPI.logout(token)\n\t\t\t}\n\t\t} catch (error) {\n\t\t\tconsole.error('退出登录API调用失败:', error)\n\t\t} finally {\n\t\t\tthis.clearLoginData()\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '已退出登录',\n\t\t\t\ticon: 'success'\n\t\t\t})\n\t\t}\n\t},\n\n\t/**\n\t * 刷新用户信息\n\t */\n\tasync refreshUserInfo() {\n\t\ttry {\n\t\t\tif (!this.isLoggedIn()) {\n\t\t\t\treturn null\n\t\t\t}\n\n\t\t\tconst result = await authAPI.getUserInfo()\n\t\t\tif (result.data) {\n\t\t\t\tuni.setStorageSync('userInfo', result.data)\n\t\t\t\treturn result.data\n\t\t\t}\n\t\t} catch (error) {\n\t\t\tconsole.error('刷新用户信息失败:', error)\n\t\t}\n\t\treturn null\n\t},\n\n\t/**\n\t * 显示登录提示\n\t */\n\tshowLoginTip(title = '需要登录', content = '请先登录后再进行此操作') {\n\t\tuni.showModal({\n\t\t\ttitle,\n\t\t\tcontent,\n\t\t\tconfirmText: '去登录',\n\t\t\tsuccess: (res) => {\n\t\t\t\tif (res.confirm) {\n\t\t\t\t\tthis.navigateToLogin()\n\t\t\t\t}\n\t\t\t}\n\t\t})\n\t}\n}\n\nexport default AuthUtils\n"], "names": ["uni", "authAPI"], "mappings": ";;;AAMK,MAAC,YAAY;AAAA;AAAA;AAAA;AAAA,EAIjB,aAAa;AACZ,UAAM,QAAQA,cAAAA,MAAI,eAAe,OAAO;AACxC,UAAM,WAAWA,cAAAA,MAAI,eAAe,UAAU;AAC9C,WAAO,CAAC,EAAE,SAAS;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA,EAKD,iBAAiB;AAChB,WAAOA,cAAG,MAAC,eAAe,UAAU;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA,EAKD,WAAW;AACV,WAAOA,cAAG,MAAC,eAAe,OAAO;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA,EAKD,iBAAiB;AAChBA,kBAAG,MAAC,kBAAkB,OAAO;AAC7BA,kBAAG,MAAC,kBAAkB,UAAU;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA,EAKD,cAAc,OAAO,UAAU;AAC9BA,wBAAI,eAAe,SAAS,KAAK;AACjCA,wBAAI,eAAe,YAAY,QAAQ;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA,EAKD,sBAAsB,YAAY,MAAM;AACvC,QAAI,CAAC,KAAK,cAAc;AACvB,UAAI,WAAW;AACdA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,SAAS;AAAA,UACT,aAAa;AAAA,UACb,SAAS,CAAC,QAAQ;AACjB,gBAAI,IAAI,SAAS;AAChB,mBAAK,gBAAiB;AAAA,YACtB;AAAA,UACD;AAAA,QACN,CAAK;AAAA,MACL,OAAU;AACN,aAAK,gBAAiB;AAAA,MACtB;AACD,aAAO;AAAA,IACP;AACD,WAAO;AAAA,EACP;AAAA;AAAA;AAAA;AAAA,EAKD,kBAAkB;AACjBA,kBAAAA,MAAI,WAAW;AAAA,MACd,KAAK;AAAA,IACR,CAAG;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,cAAc;AACnB,QAAI;AACH,YAAM,QAAQ,KAAK,SAAU;AAC7B,UAAI,CAAC,OAAO;AACX,eAAO;AAAA,MACP;AAED,YAAM,SAAS,MAAMC,kBAAQ,YAAY,KAAK;AAC9C,UAAI,OAAO,QAAQ,OAAO,KAAK,OAAO;AAErC,YAAI,OAAO,KAAK,MAAM;AACrBD,wBAAAA,MAAI,eAAe,YAAY,OAAO,KAAK,IAAI;AAAA,QAC/C;AACD,eAAO;AAAA,MACX,OAAU;AACN,aAAK,eAAgB;AACrB,eAAO;AAAA,MACP;AAAA,IACD,SAAQ,OAAO;AACfA,oBAAAA,MAAc,MAAA,SAAA,wBAAA,cAAc,KAAK;AAEjC,aAAO,KAAK,WAAY;AAAA,IACxB;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,YAAY,WAAW,MAAM;AAClC,QAAI;AAEH,YAAM,WAAW,MAAM,KAAK,cAAe;AAG3C,YAAM,oBAAoB,WAAW;AAAA,QACpC,UAAU,SAAS;AAAA,QACnB,QAAQ,SAAS;AAAA,QACjB,QAAQ,SAAS;AAAA,QACjB,MAAM,SAAS;AAAA,QACf,UAAU,SAAS;AAAA,QACnB,SAAS,SAAS;AAAA,MACtB,IAAO;AAGJ,YAAM,SAAS,MAAMC,UAAO,QAAC,MAAM;AAAA,QAClC,MAAM,SAAS;AAAA,QACf,UAAU;AAAA,MACd,CAAI;AAGD,WAAK,cAAc,OAAO,KAAK,OAAO,OAAO,KAAK,IAAI;AAEtD,aAAO,OAAO;AAAA,IACd,SAAQ,OAAO;AACfD,oBAAAA,MAAA,MAAA,SAAA,wBAAc,WAAW,KAAK;AAC9B,YAAM;AAAA,IACN;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKD,gBAAgB;AACf,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACvCA,oBAAAA,MAAI,MAAM;AAAA,QACT,UAAU;AAAA,QACV,SAAS;AAAA,QACT,MAAM;AAAA,MACV,CAAI;AAAA,IACJ,CAAG;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKD,oBAAoB;AACnB,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACvCA,oBAAAA,MAAI,eAAe;AAAA,QAClB,MAAM;AAAA,QACN,SAAS;AAAA,QACT,MAAM;AAAA,MACV,CAAI;AAAA,IACJ,CAAG;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,SAAS;AACd,QAAI;AACH,YAAM,QAAQ,KAAK,SAAU;AAC7B,UAAI,OAAO;AACV,cAAMC,UAAO,QAAC,OAAO,KAAK;AAAA,MAC1B;AAAA,IACD,SAAQ,OAAO;AACfD,oBAAAA,MAAA,MAAA,SAAA,wBAAc,gBAAgB,KAAK;AAAA,IACtC,UAAY;AACT,WAAK,eAAgB;AACrBA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAI;AAAA,IACD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,kBAAkB;AACvB,QAAI;AACH,UAAI,CAAC,KAAK,cAAc;AACvB,eAAO;AAAA,MACP;AAED,YAAM,SAAS,MAAMC,UAAO,QAAC,YAAa;AAC1C,UAAI,OAAO,MAAM;AAChBD,sBAAAA,MAAI,eAAe,YAAY,OAAO,IAAI;AAC1C,eAAO,OAAO;AAAA,MACd;AAAA,IACD,SAAQ,OAAO;AACfA,oBAAAA,MAAc,MAAA,SAAA,wBAAA,aAAa,KAAK;AAAA,IAChC;AACD,WAAO;AAAA,EACP;AAAA;AAAA;AAAA;AAAA,EAKD,aAAa,QAAQ,QAAQ,UAAU,eAAe;AACrDA,kBAAAA,MAAI,UAAU;AAAA,MACb;AAAA,MACA;AAAA,MACA,aAAa;AAAA,MACb,SAAS,CAAC,QAAQ;AACjB,YAAI,IAAI,SAAS;AAChB,eAAK,gBAAiB;AAAA,QACtB;AAAA,MACD;AAAA,IACJ,CAAG;AAAA,EACD;AACF;;"}