{"version": 3, "file": "detail.js", "sources": ["pages/mall/detail.vue", "../../../Program Files/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvbWFsbC9kZXRhaWwudnVl"], "sourcesContent": ["<template>\n\t<view class=\"detail-container\">\n\t\t<!-- 商品主图 -->\n\t\t<view class=\"product-images\">\n\t\t\t<view class=\"main-image\" @click=\"previewImages(0)\">\n\t\t\t\t<!-- 如果有主图，显示主图；否则显示emoji -->\n\t\t\t\t<image v-if=\"product.image\"\n\t\t\t\t\t   :src=\"product.image\"\n\t\t\t\t\t   class=\"product-image\"\n\t\t\t\t\t   mode=\"aspectFill\"\n\t\t\t\t\t   @error=\"handleImageError\" />\n\t\t\t\t<text v-else class=\"product-emoji\">{{ product.emoji }}</text>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 商品信息 -->\n\t\t<view class=\"product-info\">\n\t\t\t<view class=\"product-header\">\n\t\t\t\t<text class=\"product-name\">{{ product.name }}</text>\n\t\t\t\t<!-- 商品描述 -->\n\t\t\t\t<text class=\"product-description\">{{ product.description || '暂无详细描述' }}</text>\n\t\t\t\t<view class=\"product-price\">\n\t\t\t\t\t<text class=\"points-price\">{{ product.points_price }}积分</text>\n\t\t\t\t\t<text class=\"market-price\" v-if=\"product.market_price\">¥{{ product.market_price }}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<view class=\"product-tags\" v-if=\"product.tags && product.tags.length\">\n\t\t\t\t<text class=\"tag\" v-for=\"tag in product.tags\" :key=\"tag\">{{ tag }}</text>\n\t\t\t</view>\n\n\t\t\t<view class=\"product-stats\">\n\t\t\t\t<view class=\"stat-item\">\n\t\t\t\t\t<text class=\"stat-label\">库存</text>\n\t\t\t\t\t<text class=\"stat-value\">{{ product.stock || 0 }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"stat-item\">\n\t\t\t\t\t<text class=\"stat-label\">已兑换</text>\n\t\t\t\t\t<text class=\"stat-value\">{{ product.sold || 0 }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"stat-item\">\n\t\t\t\t\t<text class=\"stat-label\">评分</text>\n\t\t\t\t\t<text class=\"stat-value\">{{ product.rating || '5.0' }}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 商品详情 -->\n\t\t<view v-if=\"product.images && product.images.length > 0\" class=\"product-detail\">\n\t\t\t<view class=\"section-title\">\n\t\t\t\t<text class=\"title-text\">商品详情</text>\n\t\t\t</view>\n\t\t\t<view class=\"detail-content\">\n\t\t\t\t<!-- 详情图片 -->\n\t\t\t\t<view class=\"detail-images\">\n\t\t\t\t\t<view v-for=\"(img, index) in product.images\" :key=\"index\"\n\t\t\t\t\t\t  class=\"detail-image-item\" @click=\"previewImages(index, 'detail')\">\n\t\t\t\t\t\t<image :src=\"img\" class=\"detail-image\" mode=\"widthFix\" />\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 兑换须知 -->\n\t\t<view class=\"exchange-rules\">\n\t\t\t<view class=\"section-title\">\n\t\t\t\t<text class=\"title-text\">兑换须知</text>\n\t\t\t</view>\n\t\t\t<view class=\"rules-list\">\n\t\t\t\t<view class=\"rule-item\" v-for=\"(rule, index) in exchangeRules\" :key=\"index\">\n\t\t\t\t\t<text class=\"rule-text\">{{ index + 1 }}. {{ rule }}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 底部操作栏 -->\n\t\t<view class=\"bottom-actions\">\n\t\t\t<view class=\"action-left\">\n\t\t\t\t<view class=\"action-btn\" @click=\"toggleFavorite\">\n\t\t\t\t\t<svg-icon :name=\"isFavorite ? 'heart-filled' : 'heart'\" :size=\"20\" :color=\"isFavorite ? '#f56c6c' : '#999'\"></svg-icon>\n\t\t\t\t\t<text class=\"action-text\">{{ isFavorite ? '已收藏' : '收藏' }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"action-btn\" @click=\"shareProduct\">\n\t\t\t\t\t<svg-icon name=\"share\" :size=\"20\" color=\"#999\"></svg-icon>\n\t\t\t\t\t<text class=\"action-text\">分享</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"action-right\">\n\t\t\t\t<button class=\"exchange-btn\" @click=\"handleExchange\" :disabled=\"!canExchange\" :loading=\"exchanging\">\n\t\t\t\t\t<text class=\"btn-text\">{{ exchangeButtonText }}</text>\n\t\t\t\t</button>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nimport { productAPI, pointsAPI } from '@/api/index.js'\nimport { getImageUrl } from '@/utils/request.js'\nimport SvgIcon from '@/components/svg-icon/svg-icon.vue'\n\nexport default {\n\tcomponents: {\n\t\tSvgIcon\n\t},\n\tdata() {\n\t\treturn {\n\t\t\tproductId: null,\n\t\t\tproduct: {\n\t\t\t\tid: 1,\n\t\t\t\tname: '商品名称',\n\t\t\t\tdescription: '这是一个很棒的商品，值得拥有。',\n\t\t\t\temoji: '🎁',\n\t\t\t\tpoints_price: 100,\n\t\t\t\tmarket_price: '50.00',\n\t\t\t\tstock: 99,\n\t\t\t\tsold: 10,\n\t\t\t\trating: '4.8',\n\t\t\t\ttags: ['热门', '推荐']\n\t\t\t},\n\t\t\tisFavorite: false,\n\t\t\texchanging: false,\n\t\t\tuserPoints: 0,\n\t\t\texchangeRules: [\n\t\t\t\t'积分兑换商品不支持退换货',\n\t\t\t\t'兑换成功后将在3-7个工作日内发货',\n\t\t\t\t'请确保收货地址准确无误',\n\t\t\t\t'如有质量问题请联系客服处理',\n\t\t\t\t'积分兑换商品不参与其他优惠活动'\n\t\t\t]\n\t\t}\n\t},\n\tcomputed: {\n\t\tcanExchange() {\n\t\t\treturn this.product.stock > 0 && this.userPoints >= this.product.points_price\n\t\t},\n\t\texchangeButtonText() {\n\t\t\tif (this.product.stock <= 0) {\n\t\t\t\treturn '库存不足'\n\t\t\t}\n\t\t\tif (this.userPoints < this.product.points_price) {\n\t\t\t\treturn '积分不足'\n\t\t\t}\n\t\t\treturn '立即兑换'\n\t\t}\n\t},\n\tonLoad(options) {\n\t\tif (options.id) {\n\t\t\tthis.productId = options.id\n\t\t\tthis.loadProductDetail()\n\t\t}\n\t\tthis.loadUserPoints()\n\t\tthis.checkFavoriteStatus()\n\t},\n\tonShareAppMessage() {\n\t\t// 获取分享图片\n\t\tconst shareImage = this.product.images && this.product.images.length > 0\n\t\t\t? this.product.images[0]\n\t\t\t: this.product.image || ''\n\n\t\treturn {\n\t\t\ttitle: this.product.name,\n\t\t\tpath: `/pages/mall/detail?id=${this.productId}`,\n\t\t\timageUrl: shareImage\n\t\t}\n\t},\n\tmethods: {\n\t\t// 加载商品详情\n\t\tasync loadProductDetail() {\n\t\t\ttry {\n\t\t\t\tconst response = await productAPI.getDetail(this.productId)\n\t\t\t\tthis.product = response.data\n\n\t\t\t\t// 处理图片URL\n\t\t\t\tif (this.product.image) {\n\t\t\t\t\tthis.product.image = getImageUrl(this.product.image)\n\t\t\t\t}\n\n\t\t\t\tif (this.product.images && Array.isArray(this.product.images)) {\n\t\t\t\t\tthis.product.images = this.product.images.map(img => getImageUrl(img))\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('获取商品详情失败:', error)\n\t\t\t\t// 使用模拟数据作为降级方案\n\t\t\t\tconst emojis = ['🎁', '☕', '🎧', '🔋', '📱', '🖱️', '⌨️', '💻', '📺', '🎮'];\n\t\t\t\tconst names = ['精美礼品', '精美水杯', '蓝牙耳机', '充电宝', '手机支架', '无线鼠标', '键盘膜', '笔记本电脑', '智能电视', '游戏手柄'];\n\n\t\t\t\tconst index = (this.productId - 1) % emojis.length;\n\t\t\t\tthis.product = {\n\t\t\t\t\tid: this.productId,\n\t\t\t\t\tname: names[index] || `商品${this.productId}`,\n\t\t\t\t\tdescription: '这是一个很棒的商品，采用优质材料制作，工艺精良，性价比极高。适合日常使用，是您生活的好帮手。',\n\t\t\t\t\temoji: emojis[index],\n\t\t\t\t\tpoints_price: Math.floor(Math.random() * 500) + 100,\n\t\t\t\t\tmarket_price: (Math.random() * 100 + 20).toFixed(2),\n\t\t\t\t\tstock: Math.floor(Math.random() * 100) + 10,\n\t\t\t\t\tsold: Math.floor(Math.random() * 50),\n\t\t\t\t\trating: (Math.random() * 1 + 4).toFixed(1),\n\t\t\t\t\ttags: ['热门', '推荐', '限时']\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\n\t\t// 加载用户积分\n\t\tasync loadUserPoints() {\n\t\t\ttry {\n\t\t\t\t// 检查是否已登录\n\t\t\t\tconst token = uni.getStorageSync('token')\n\t\t\t\tif (!token) {\n\t\t\t\t\tthis.userPoints = 0\n\t\t\t\t\treturn\n\t\t\t\t}\n\n\t\t\t\tconst response = await pointsAPI.getBalance()\n\t\t\t\tthis.userPoints = response.data.available_points || 0\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('获取积分余额失败:', error)\n\t\t\t\t// 使用模拟数据作为降级方案\n\t\t\t\tthis.userPoints = 1580\n\t\t\t}\n\t\t},\n\n\t\t// 检查收藏状态\n\t\tasync checkFavoriteStatus() {\n\t\t\ttry {\n\t\t\t\t// 模拟检查收藏状态\n\t\t\t\tthis.isFavorite = false\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('检查收藏状态失败:', error)\n\t\t\t}\n\t\t},\n\n\t\t// 预览图片\n\t\tpreviewImages(current, type = 'main') {\n\t\t\tlet images = []\n\t\t\tlet currentIndex = current\n\n\t\t\tif (type === 'detail') {\n\t\t\t\t// 预览详情图片\n\t\t\t\timages = this.product.images || []\n\t\t\t\tcurrentIndex = current\n\t\t\t} else {\n\t\t\t\t// 预览主图，如果有详情图片也包含进来\n\t\t\t\tif (this.product.image) {\n\t\t\t\t\timages.push(this.product.image)\n\t\t\t\t}\n\t\t\t\tif (this.product.images && this.product.images.length > 0) {\n\t\t\t\t\timages = images.concat(this.product.images)\n\t\t\t\t}\n\t\t\t\tcurrentIndex = 0 // 主图总是第一张\n\t\t\t}\n\n\t\t\tif (images.length === 0) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '暂无图片',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t})\n\t\t\t\treturn\n\t\t\t}\n\n\t\t\tuni.previewImage({\n\t\t\t\turls: images,\n\t\t\t\tcurrent: images[currentIndex] || images[0]\n\t\t\t})\n\t\t},\n\n\t\t// 切换收藏状态\n\t\tasync toggleFavorite() {\n\t\t\ttry {\n\t\t\t\t// 检查是否已登录\n\t\t\t\tconst token = uni.getStorageSync('token')\n\t\t\t\tif (!token) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请先登录',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl: '/pages/login/login'\n\t\t\t\t\t})\n\t\t\t\t\treturn\n\t\t\t\t}\n\n\t\t\t\t// 模拟收藏操作\n\t\t\t\tif (this.isFavorite) {\n\t\t\t\t\tthis.isFavorite = false\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '已取消收藏',\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t})\n\t\t\t\t} else {\n\t\t\t\t\tthis.isFavorite = true\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '收藏成功',\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t})\n\t\t\t\t}\n\n\t\t\t\t// TODO: 集成真实API\n\t\t\t\t// const { favoriteAPI } = await import('@/api/index.js')\n\t\t\t\t// if (this.isFavorite) {\n\t\t\t\t//     await favoriteAPI.remove(this.productId)\n\t\t\t\t// } else {\n\t\t\t\t//     await favoriteAPI.add(this.productId)\n\t\t\t\t// }\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('收藏操作失败:', error)\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '操作失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t})\n\t\t\t}\n\t\t},\n\n\t\t// 分享商品\n\t\tshareProduct() {\n\t\t\tuni.showShareMenu({\n\t\t\t\twithShareTicket: true\n\t\t\t})\n\t\t},\n\n\t\t// 处理兑换\n\t\tasync handleExchange() {\n\t\t\tif (!this.canExchange || this.exchanging) return\n\n\t\t\t// 检查是否已登录\n\t\t\tconst token = uni.getStorageSync('token')\n\t\t\tif (!token) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请先登录',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t})\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/login/login'\n\t\t\t\t})\n\t\t\t\treturn\n\t\t\t}\n\n\t\t\t// 跳转到订单确认页面（包含地址选择）\n\t\t\tuni.navigateTo({\n\t\t\t\turl: `/pages/order/confirm?productId=${this.productId}&quantity=1&type=exchange`\n\t\t\t})\n\t\t},\n\n\t\t// 处理图片加载错误\n\t\thandleImageError() {\n\t\t\tconsole.log('图片加载失败，显示emoji')\n\t\t\t// 图片加载失败时，可以设置一个标志来显示emoji\n\t\t}\n\t}\n}\n</script>\n\n<style scoped>\n.detail-container {\n\tbackground: #f8f8f8;\n\tmin-height: 100vh;\n\tpadding-bottom: 80px;\n}\n\n.product-images {\n\tbackground: #ffffff;\n\tmargin-bottom: 10px;\n}\n\n.main-image {\n\theight: 300px;\n\tbackground: #f5f5f5;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.product-emoji {\n\tfont-size: 120px;\n}\n\n.product-image {\n\twidth: 100%;\n\theight: 100%;\n}\n\n.product-info {\n\tbackground: #ffffff;\n\tpadding: 20px;\n\tmargin-bottom: 10px;\n}\n\n.product-header {\n\tmargin-bottom: 15px;\n}\n\n.product-name {\n\tdisplay: block;\n\tfont-size: 18px;\n\tfont-weight: 500;\n\tcolor: #333;\n\tmargin-bottom: 8px;\n\tline-height: 1.4;\n}\n\n.product-description {\n\tdisplay: block;\n\tfont-size: 14px;\n\tcolor: #666;\n\tline-height: 1.5;\n\tmargin-bottom: 15px;\n}\n\n.product-price {\n\tdisplay: flex;\n\talign-items: baseline;\n\tgap: 10px;\n}\n\n.points-price {\n\tfont-size: 24px;\n\tfont-weight: bold;\n\tcolor: #667eea;\n}\n\n.market-price {\n\tfont-size: 14px;\n\tcolor: #999;\n\ttext-decoration: line-through;\n}\n\n.product-tags {\n\tmargin-bottom: 15px;\n}\n\n.tag {\n\tdisplay: inline-block;\n\tpadding: 4px 8px;\n\tbackground: rgba(102, 126, 234, 0.1);\n\tcolor: #667eea;\n\tfont-size: 12px;\n\tborder-radius: 4px;\n\tmargin-right: 8px;\n}\n\n.product-stats {\n\tdisplay: flex;\n\tjustify-content: space-around;\n\tpadding: 15px 0;\n\tborder-top: 1px solid #f5f5f5;\n}\n\n.stat-item {\n\ttext-align: center;\n}\n\n.stat-label {\n\tdisplay: block;\n\tfont-size: 12px;\n\tcolor: #999;\n\tmargin-bottom: 4px;\n}\n\n.stat-value {\n\tdisplay: block;\n\tfont-size: 16px;\n\tfont-weight: 500;\n\tcolor: #333;\n}\n\n.product-detail,\n.exchange-rules {\n\tbackground: #ffffff;\n\tpadding: 20px;\n\tmargin-bottom: 10px;\n}\n\n.section-title {\n\tmargin-bottom: 15px;\n\tpadding: 0 5px;\n}\n\n.title-text {\n\tfont-size: 16px;\n\tfont-weight: 500;\n\tcolor: #333;\n}\n\n.detail-content {\n\tline-height: 1.6;\n\tpadding: 0 5px 15px 5px;\n}\n\n.detail-images {\n\tmargin-top: 0;\n}\n\n.detail-image-item {\n\tmargin-bottom: 15px;\n\tbackground: #f5f5f5;\n}\n\n.detail-image {\n\twidth: 100%;\n\tdisplay: block;\n}\n\n.rules-list {\n\tpadding: 0;\n\tmargin: 0;\n}\n\n.rule-item {\n\tmargin-bottom: 8px;\n}\n\n.rule-text {\n\tfont-size: 14px;\n\tcolor: #666;\n\tline-height: 1.5;\n}\n\n.bottom-actions {\n\tposition: fixed;\n\tbottom: 0;\n\tleft: 0;\n\tright: 0;\n\tbackground: #ffffff;\n\tpadding: 10px 15px;\n\tborder-top: 1px solid #f0f0f0;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tz-index: 100;\n}\n\n.action-left {\n\tdisplay: flex;\n\tgap: 20px;\n}\n\n.action-btn {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tgap: 4px;\n}\n\n.action-text {\n\tfont-size: 12px;\n\tcolor: #999;\n}\n\n.action-right {\n\tflex: 1;\n\tmargin-left: 20px;\n}\n\n.exchange-btn {\n\twidth: 100%;\n\theight: 44px;\n\tbackground: #667eea;\n\tborder-radius: 22px;\n\tborder: none;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.exchange-btn[disabled] {\n\tbackground: #ccc;\n}\n\n.btn-text {\n\tfont-size: 16px;\n\tfont-weight: 500;\n\tcolor: #ffffff;\n}\n</style>\n", "import MiniProgramPage from 'D:/app/miniprogram/SIYU/pages/mall/detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["getImageUrl", "uni", "pointsAPI"], "mappings": ";;;;AAmGA,MAAA,UAAA,MAAA;AAEA,MAAA,YAAA;AAAA;IAEE;AAAA;EAED,OAAA;AACC,WAAA;AAAA,MACC,WAAA;AAAA,MACA,SAAA;AAAA;;;;QAKC,cAAA;AAAA,QACA,cAAA;AAAA,QACA,OAAA;AAAA,QACA,MAAA;AAAA;QAEA,MAAA,CAAA,MAAA,IAAA;AAAA;MAED,YAAA;AAAA,MACA,YAAA;AAAA;MAEA,eAAA;AAAA,QACC;AAAA,QACA;AAAA;QAEA;AAAA,QACA;AAAA,MACD;AAAA,IACD;AAAA;EAED,UAAA;AAAA;AAEE,aAAA,KAAA,QAAA,QAAA,KAAA,KAAA,cAAA,KAAA,QAAA;AAAA;IAED,qBAAA;;;MAGC;AACA,UAAA,KAAA,aAAA,KAAA,QAAA,cAAA;;MAEA;;IAED;AAAA;EAED,OAAA,SAAA;AACC,QAAA,QAAA,IAAA;;;IAGA;AACA,SAAA,eAAA;;;EAGD,oBAAA;AAEC,UAAA,aAAA,KAAA,QAAA,UAAA,KAAA,QAAA,OAAA,SAAA;AAIA,WAAA;AAAA;;MAGC,UAAA;AAAA,IACD;AAAA;EAED,SAAA;AAAA;AAAA;AAGE,UAAA;;;;;QAOC;;AAGC,eAAA,QAAA,SAAA,KAAA,QAAA,OAAA,IAAA,SAAAA,0BAAA,GAAA,CAAA;AAAA,QACD;AAAA;AAEAC,sBAAAA,MAAA,MAAA,SAAA,gCAAA,aAAA,KAAA;AAEA,cAAA,SAAA,CAAA,MAAA,KAAA,MAAA,MAAA,MAAA,OAAA,MAAA,MAAA,MAAA,IAAA;AACA,cAAA,QAAA,CAAA,QAAA,QAAA,QAAA,OAAA,QAAA,QAAA,OAAA,SAAA,QAAA,MAAA;AAEA,cAAA,SAAA,KAAA,YAAA,KAAA,OAAA;;UAEC,IAAA,KAAA;AAAA,UACA,MAAA,MAAA,KAAA,KAAA,KAAA,KAAA,SAAA;AAAA;UAEA,OAAA,OAAA,KAAA;AAAA;;UAGA,OAAA,KAAA,MAAA,KAAA,OAAA,IAAA,GAAA,IAAA;AAAA,UACA,MAAA,KAAA,MAAA,KAAA,OAAA,IAAA,EAAA;AAAA,UACA,SAAA,KAAA,OAAA,IAAA,IAAA,GAAA,QAAA,CAAA;AAAA,UACA,MAAA,CAAA,MAAA,MAAA,IAAA;AAAA,QACD;AAAA,MACD;AAAA;;IAID,MAAA,iBAAA;AACC,UAAA;AAEC,cAAA,QAAAA,cAAAA,MAAA,eAAA,OAAA;;AAEC,eAAA,aAAA;;QAED;AAEA,cAAA,WAAA,MAAAC,UAAA,UAAA,WAAA;;;AAGAD,sBAAAA,MAAA,MAAA,SAAA,gCAAA,aAAA,KAAA;AAEA,aAAA,aAAA;AAAA,MACD;AAAA;;;AAKA,UAAA;AAEC,aAAA,aAAA;AAAA;AAEAA,sBAAAA,MAAA,MAAA,SAAA,gCAAA,aAAA,KAAA;AAAA,MACD;AAAA;;IAID,cAAA,SAAA,OAAA,QAAA;;;AAIC,UAAA,SAAA,UAAA;;AAGC,uBAAA;AAAA;;AAIC,iBAAA,KAAA,KAAA,QAAA,KAAA;AAAA,QACD;;AAEC,mBAAA,OAAA,OAAA,KAAA,QAAA,MAAA;AAAA,QACD;;MAED;AAEA,UAAA,OAAA,WAAA,GAAA;AACCA,sBAAAA,MAAA,UAAA;AAAA;;;;MAKD;AAEAA,oBAAAA,MAAA,aAAA;AAAA;QAEC,SAAA,OAAA,YAAA,KAAA,OAAA,CAAA;AAAA;;;IAKF,MAAA,iBAAA;AACC,UAAA;AAEC,cAAA,QAAAA,cAAAA,MAAA,eAAA,OAAA;;AAECA,wBAAAA,MAAA,UAAA;AAAA;;;AAIAA,wBAAAA,MAAA,WAAA;AAAA;;;QAID;AAGA,YAAA,KAAA,YAAA;AACC,eAAA,aAAA;AACAA,wBAAAA,MAAA,UAAA;AAAA;;;;AAKA,eAAA,aAAA;AACAA,wBAAAA,MAAA,UAAA;AAAA;;;QAID;AAAA;AAUAA,sBAAAA,MAAA,MAAA,SAAA,gCAAA,WAAA,KAAA;AACAA,sBAAAA,MAAA,UAAA;AAAA;;;MAID;AAAA;;IAID,eAAA;AACCA,oBAAAA,MAAA,cAAA;AAAA,QACC,iBAAA;AAAA;;;IAKF,MAAA,iBAAA;AACC,UAAA,CAAA,KAAA,eAAA,KAAA;AAAA;AAGA,YAAA,QAAAA,cAAAA,MAAA,eAAA,OAAA;;AAECA,sBAAAA,MAAA,UAAA;AAAA;;;AAIAA,sBAAAA,MAAA,WAAA;AAAA;;;MAID;AAGAA,oBAAAA,MAAA,WAAA;AAAA,QACC,KAAA,kCAAA,KAAA,SAAA;AAAA;;;IAKF,mBAAA;AACCA,oBAAAA,MAAA,MAAA,OAAA,gCAAA,gBAAA;AAAA,IAED;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5VA,GAAG,WAAW,eAAe;"}