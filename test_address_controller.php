<?php
/**
 * 测试 AddressController 文件状态
 */

header('Content-Type: application/json; charset=utf-8');

try {
    $filePath = __DIR__ . '/api/controllers/api/AddressController.php';
    
    // 检查文件是否存在
    if (!file_exists($filePath)) {
        echo json_encode([
            'status' => 'error',
            'message' => 'AddressController.php 文件不存在'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 读取文件前10行
    $lines = file($filePath, FILE_IGNORE_NEW_LINES);
    $first10Lines = array_slice($lines, 0, 10);
    
    // 检查第7行的内容
    $line7 = isset($lines[6]) ? $lines[6] : '';
    
    // 检查第246行附近的内容
    $line246Area = [];
    for ($i = 240; $i <= 250; $i++) {
        if (isset($lines[$i-1])) {
            $line246Area[$i] = $lines[$i-1];
        }
    }
    
    echo json_encode([
        'status' => 'success',
        'file_exists' => true,
        'total_lines' => count($lines),
        'first_10_lines' => $first10Lines,
        'line_7' => $line7,
        'line_246_area' => $line246Area,
        'file_modified_time' => date('Y-m-d H:i:s', filemtime($filePath))
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ], JSON_UNESCAPED_UNICODE);
}
?>
