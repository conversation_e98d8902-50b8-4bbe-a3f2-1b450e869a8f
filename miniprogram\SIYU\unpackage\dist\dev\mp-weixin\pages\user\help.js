"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      searchKeyword: "",
      faqList: [
        {
          question: "如何使用积分兑换商品？",
          answer: '在商品详情页点击"立即兑换"，选择收货地址，确认订单即可完成兑换。',
          expanded: false
        },
        {
          question: "积分有有效期吗？",
          answer: "积分永久有效，不会过期。您可以随时使用积分兑换心仪的商品。",
          expanded: false
        },
        {
          question: "如何获得更多积分？",
          answer: "您可以通过每日签到、完成任务、邀请好友等方式获得积分奖励。",
          expanded: false
        },
        {
          question: "订单发货后多久能收到？",
          answer: "一般情况下，订单发货后3-7个工作日内可以收到商品，具体时间根据地区而定。",
          expanded: false
        },
        {
          question: "可以退换货吗？",
          answer: "商品如有质量问题，支持7天无理由退换货。请联系客服处理。",
          expanded: false
        },
        {
          question: "如何修改收货地址？",
          answer: '在"我的-收货地址"中可以添加、编辑或删除收货地址。',
          expanded: false
        }
      ]
    };
  },
  computed: {
    filteredFaqList() {
      if (!this.searchKeyword) {
        return this.faqList;
      }
      return this.faqList.filter(
        (item) => item.question.includes(this.searchKeyword) || item.answer.includes(this.searchKeyword)
      );
    }
  },
  methods: {
    // 搜索
    onSearch() {
    },
    // 切换FAQ展开状态
    toggleFaq(index) {
      this.faqList[index].expanded = !this.faqList[index].expanded;
    },
    // 拨打客服电话
    callService() {
      common_vendor.index.makePhoneCall({
        phoneNumber: "************"
      });
    },
    // 复制邮箱
    copyEmail() {
      common_vendor.index.setClipboardData({
        data: "<EMAIL>",
        success: () => {
          common_vendor.index.showToast({
            title: "邮箱已复制",
            icon: "success"
          });
        }
      });
    },
    // 意见反馈
    goToFeedback() {
      common_vendor.index.showToast({
        title: "功能开发中",
        icon: "none"
      });
    }
  }
};
if (!Array) {
  const _component_uni_icons = common_vendor.resolveComponent("uni-icons");
  _component_uni_icons();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.p({
      type: "search",
      size: "16",
      color: "#999"
    }),
    b: common_vendor.o([($event) => $data.searchKeyword = $event.detail.value, (...args) => $options.onSearch && $options.onSearch(...args)]),
    c: $data.searchKeyword,
    d: common_vendor.f($options.filteredFaqList, (item, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(item.question),
        b: "7818abf9-1-" + i0,
        c: common_vendor.p({
          type: item.expanded ? "up" : "down",
          size: "14",
          color: "#999"
        }),
        d: item.expanded
      }, item.expanded ? {
        e: common_vendor.t(item.answer)
      } : {}, {
        f: index,
        g: common_vendor.o(($event) => $options.toggleFaq(index), index)
      });
    }),
    e: common_vendor.p({
      type: "phone",
      size: "20",
      color: "#667eea"
    }),
    f: common_vendor.o((...args) => $options.callService && $options.callService(...args)),
    g: common_vendor.p({
      type: "email",
      size: "20",
      color: "#e74c3c"
    }),
    h: common_vendor.o((...args) => $options.copyEmail && $options.copyEmail(...args)),
    i: common_vendor.p({
      type: "clock",
      size: "20",
      color: "#f39c12"
    }),
    j: common_vendor.p({
      type: "compose",
      size: "18",
      color: "white"
    }),
    k: common_vendor.o((...args) => $options.goToFeedback && $options.goToFeedback(...args))
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-7818abf9"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/user/help.js.map
