
.region-picker.data-v-fef920e2 {
	position: relative;
}
.mask.data-v-fef920e2 {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	z-index: 999;
}
.picker-container.data-v-fef920e2 {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: white;
	border-radius: 20px 20px 0 0;
	max-height: 70vh;
	transform: translateY(100%);
	transition: transform 0.3s ease;
	z-index: 1000;
}
.picker-container.show.data-v-fef920e2 {
	transform: translateY(0);
}
.picker-header.data-v-fef920e2 {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20px;
	border-bottom: 1px solid #f0f0f0;
}
.cancel-btn.data-v-fef920e2, .confirm-btn.data-v-fef920e2 {
	font-size: 16px;
	color: #667eea;
}
.title.data-v-fef920e2 {
	font-size: 18px;
	font-weight: bold;
	color: #333;
}
.tabs.data-v-fef920e2 {
	display: flex;
	border-bottom: 1px solid #f0f0f0;
}
.tab-item.data-v-fef920e2 {
	flex: 1;
	padding: 15px;
	text-align: center;
	border-bottom: 2px solid transparent;
	color: #666;
}
.tab-item.active.data-v-fef920e2 {
	color: #667eea;
	border-bottom-color: #667eea;
}
.picker-content.data-v-fef920e2 {
	height: 400px;
}
.list-item.data-v-fef920e2 {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 15px 20px;
	border-bottom: 1px solid #f8f8f8;
}
.list-item.selected.data-v-fef920e2 {
	background: #f8f9ff;
}
.item-name.data-v-fef920e2 {
	font-size: 16px;
	color: #333;
}
.list-item.selected .item-name.data-v-fef920e2 {
	color: #667eea;
}
.check-icon.data-v-fef920e2 {
	color: #667eea;
	font-size: 18px;
	font-weight: bold;
}
