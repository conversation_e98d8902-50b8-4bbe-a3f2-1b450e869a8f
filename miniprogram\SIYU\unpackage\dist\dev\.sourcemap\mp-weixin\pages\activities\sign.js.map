{"version": 3, "file": "sign.js", "sources": ["pages/activities/sign.vue", "../../../Program Files/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvYWN0aXZpdGllcy9zaWduLnZ1ZQ"], "sourcesContent": ["<template>\n\t<view class=\"sign-container\">\n\t\t<!-- 签到卡片 -->\n\t\t<view class=\"sign-card\">\n\t\t\t<view class=\"card-header\">\n\t\t\t\t<text class=\"title\">每日签到</text>\n\t\t\t\t<text class=\"subtitle\">坚持签到，获得更多积分奖励</text>\n\t\t\t</view>\n\n\t\t\t<view class=\"sign-calendar\">\n\t\t\t\t<view class=\"calendar-header\">\n\t\t\t\t\t<text class=\"month-text\">{{ currentMonth }}</text>\n\t\t\t\t</view>\n\n\t\t\t\t<view class=\"calendar-grid\">\n\t\t\t\t\t<view class=\"week-header\">\n\t\t\t\t\t\t<text class=\"week-day\" v-for=\"day in weekDays\" :key=\"day\">{{ day }}</text>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<view class=\"calendar-days\">\n\t\t\t\t\t\t<view\n\t\t\t\t\t\t\tclass=\"day-item\"\n\t\t\t\t\t\t\t:class=\"{\n\t\t\t\t\t\t\t\t'signed': isSignedDay(day),\n\t\t\t\t\t\t\t\t'today': isToday(day),\n\t\t\t\t\t\t\t\t'future': isFutureDay(day)\n\t\t\t\t\t\t\t}\"\n\t\t\t\t\t\t\tv-for=\"day in calendarDays\"\n\t\t\t\t\t\t\t:key=\"day.date\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<text class=\"day-number\">{{ day.day }}</text>\n\t\t\t\t\t\t\t<view class=\"sign-status\" v-if=\"isSignedDay(day)\">\n\t\t\t\t\t\t\t\t<text class=\"points\">+{{ day.points || 10 }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<view class=\"sign-action\">\n\t\t\t\t<button\n\t\t\t\t\tclass=\"sign-btn\"\n\t\t\t\t\t:class=\"{ 'signed': todaySignStatus }\"\n\t\t\t\t\t:disabled=\"todaySignStatus\"\n\t\t\t\t\t@click=\"handleSign\"\n\t\t\t\t\t:loading=\"signing\"\n\t\t\t\t>\n\t\t\t\t\t<text class=\"btn-text\">{{ todaySignStatus ? '今日已签到' : '立即签到' }}</text>\n\t\t\t\t</button>\n\n\t\t\t\t<view class=\"sign-info\">\n\t\t\t\t\t<text class=\"info-text\">连续签到 {{ continuousDays }} 天</text>\n\t\t\t\t\t<text class=\"info-text\">本月签到 {{ monthSignDays }} 天</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 签到奖励规则 -->\n\t\t<view class=\"reward-rules\">\n\t\t\t<view class=\"section-title\">\n\t\t\t\t<text class=\"title-text\">签到奖励规则</text>\n\t\t\t</view>\n\n\t\t\t<view class=\"rules-list\">\n\t\t\t\t<view class=\"rule-item\" v-for=\"rule in signRules\" :key=\"rule.id\">\n\t\t\t\t\t<view class=\"rule-icon\">\n\t\t\t\t\t\t<text class=\"day-text\">{{ rule.days }}天</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"rule-info\">\n\t\t\t\t\t\t<text class=\"rule-title\">{{ rule.title }}</text>\n\t\t\t\t\t\t<text class=\"rule-desc\">{{ rule.description }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"rule-reward\">\n\t\t\t\t\t\t<text class=\"points-text\">+{{ rule.points }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 签到记录 -->\n\t\t<view class=\"sign-records\">\n\t\t\t<view class=\"section-title\">\n\t\t\t\t<text class=\"title-text\">最近签到记录</text>\n\t\t\t</view>\n\n\t\t\t<view class=\"records-list\">\n\t\t\t\t<view class=\"record-item\" v-for=\"record in signRecords\" :key=\"record.id\">\n\t\t\t\t\t<view class=\"record-date\">\n\t\t\t\t\t\t<text class=\"date-text\">{{ formatDate(record.created_at) }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"record-info\">\n\t\t\t\t\t\t<text class=\"record-title\">每日签到</text>\n\t\t\t\t\t\t<text class=\"record-desc\">连续签到第{{ record.continuous_days }}天</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"record-points\">\n\t\t\t\t\t\t<text class=\"points-text\">+{{ record.points }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tsigning: false,\n\t\t\ttodaySignStatus: false,\n\t\t\tcontinuousDays: 0,\n\t\t\tmonthSignDays: 0,\n\t\t\tcurrentMonth: '',\n\t\t\tweekDays: ['日', '一', '二', '三', '四', '五', '六'],\n\t\t\tcalendarDays: [],\n\t\t\tsignedDays: [],\n\t\t\tsignRules: [\n\t\t\t\t{\n\t\t\t\t\tid: 1,\n\t\t\t\t\tdays: 1,\n\t\t\t\t\ttitle: '每日签到',\n\t\t\t\t\tdescription: '每天签到可获得基础积分',\n\t\t\t\t\tpoints: 10\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tid: 2,\n\t\t\t\t\tdays: 7,\n\t\t\t\t\ttitle: '连续7天',\n\t\t\t\t\tdescription: '连续签到7天额外奖励',\n\t\t\t\t\tpoints: 50\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tid: 3,\n\t\t\t\t\tdays: 15,\n\t\t\t\t\ttitle: '连续15天',\n\t\t\t\t\tdescription: '连续签到15天额外奖励',\n\t\t\t\t\tpoints: 100\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tid: 4,\n\t\t\t\t\tdays: 30,\n\t\t\t\t\ttitle: '连续30天',\n\t\t\t\t\tdescription: '连续签到30天额外奖励',\n\t\t\t\t\tpoints: 300\n\t\t\t\t}\n\t\t\t],\n\t\t\tsignRecords: []\n\t\t}\n\t},\n\tonLoad() {\n\t\tthis.initCalendar()\n\t\tthis.loadSignStatus()\n\t\tthis.loadSignRecords()\n\t},\n\tmethods: {\n\t\t// 初始化日历\n\t\tinitCalendar() {\n\t\t\tconst now = new Date()\n\t\t\tconst year = now.getFullYear()\n\t\t\tconst month = now.getMonth()\n\n\t\t\tthis.currentMonth = `${year}年${month + 1}月`\n\n\t\t\t// 获取当月第一天和最后一天\n\t\t\tconst firstDay = new Date(year, month, 1)\n\t\t\tconst lastDay = new Date(year, month + 1, 0)\n\n\t\t\t// 生成日历数据\n\t\t\tconst days = []\n\t\t\tfor (let i = 1; i <= lastDay.getDate(); i++) {\n\t\t\t\tdays.push({\n\t\t\t\t\tdate: `${year}-${String(month + 1).padStart(2, '0')}-${String(i).padStart(2, '0')}`,\n\t\t\t\t\tday: i\n\t\t\t\t})\n\t\t\t}\n\n\t\t\tthis.calendarDays = days\n\t\t},\n\n\t\t// 加载签到状态\n\t\tasync loadSignStatus() {\n\t\t\ttry {\n\t\t\t\t// 检查是否已登录\n\t\t\t\tconst token = uni.getStorageSync('token')\n\t\t\t\tif (!token) {\n\t\t\t\t\treturn\n\t\t\t\t}\n\n\t\t\t\t// 使用模拟数据\n\t\t\t\tthis.todaySignStatus = false\n\t\t\t\tthis.continuousDays = 5\n\t\t\t\tthis.monthSignDays = 12\n\t\t\t\tthis.signedDays = [\n\t\t\t\t\t'2024-01-10', '2024-01-11', '2024-01-12',\n\t\t\t\t\t'2024-01-13', '2024-01-14'\n\t\t\t\t]\n\n\t\t\t\t// TODO: 集成真实API\n\t\t\t\t// const { pointsAPI } = await import('@/api/index.js')\n\t\t\t\t// const response = await pointsAPI.getSignStatus()\n\t\t\t\t// this.todaySignStatus = response.data.today_signed\n\t\t\t\t// this.continuousDays = response.data.continuous_days\n\t\t\t\t// this.monthSignDays = response.data.month_sign_days\n\t\t\t\t// this.signedDays = response.data.signed_days || []\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('获取签到状态失败:', error)\n\t\t\t}\n\t\t},\n\n\t\t// 加载签到记录\n\t\tasync loadSignRecords() {\n\t\t\ttry {\n\t\t\t\t// 检查是否已登录\n\t\t\t\tconst token = uni.getStorageSync('token')\n\t\t\t\tif (!token) {\n\t\t\t\t\treturn\n\t\t\t\t}\n\n\t\t\t\t// 使用模拟数据\n\t\t\t\tthis.signRecords = [\n\t\t\t\t\t{\n\t\t\t\t\t\tid: 1,\n\t\t\t\t\t\tcreated_at: '2024-01-14 09:30:00',\n\t\t\t\t\t\tcontinuous_days: 5,\n\t\t\t\t\t\tpoints: 10\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tid: 2,\n\t\t\t\t\t\tcreated_at: '2024-01-13 08:45:00',\n\t\t\t\t\t\tcontinuous_days: 4,\n\t\t\t\t\t\tpoints: 10\n\t\t\t\t\t}\n\t\t\t\t]\n\n\t\t\t\t// TODO: 集成真实API\n\t\t\t\t// const { activityAPI } = await import('@/api/index.js')\n\t\t\t\t// const response = await activityAPI.getSignRecords()\n\t\t\t\t// this.signRecords = response.data.list || []\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('获取签到记录失败:', error)\n\t\t\t}\n\t\t},\n\n\t\t// 执行签到\n\t\tasync handleSign() {\n\t\t\tif (this.todaySignStatus || this.signing) return\n\n\t\t\t// 检查是否已登录\n\t\t\tconst token = uni.getStorageSync('token')\n\t\t\tif (!token) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请先登录',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t})\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/login/login'\n\t\t\t\t})\n\t\t\t\treturn\n\t\t\t}\n\n\t\t\tthis.signing = true\n\n\t\t\ttry {\n\t\t\t\t// 模拟签到成功\n\t\t\t\tconst mockResponse = {\n\t\t\t\t\tdata: {\n\t\t\t\t\t\tcontinuous_days: this.continuousDays + 1,\n\t\t\t\t\t\tpoints: 10\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// 更新签到状态\n\t\t\t\tthis.todaySignStatus = true\n\t\t\t\tthis.continuousDays = mockResponse.data.continuous_days\n\t\t\t\tthis.monthSignDays += 1\n\n\t\t\t\t// 添加今天到已签到列表\n\t\t\t\tconst today = this.formatDate(new Date(), 'YYYY-MM-DD')\n\t\t\t\tthis.signedDays.push(today)\n\n\t\t\t\t// 显示签到成功\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: `签到成功，获得${mockResponse.data.points}积分`,\n\t\t\t\t\ticon: 'success',\n\t\t\t\t\tduration: 2000\n\t\t\t\t})\n\n\t\t\t\t// 刷新签到记录\n\t\t\t\tthis.loadSignRecords()\n\n\t\t\t\t// TODO: 集成真实API\n\t\t\t\t// const { pointsAPI } = await import('@/api/index.js')\n\t\t\t\t// const response = await pointsAPI.signIn()\n\t\t\t\t// this.todaySignStatus = true\n\t\t\t\t// this.continuousDays = response.data.continuous_days\n\t\t\t\t// this.monthSignDays += 1\n\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('签到失败:', error)\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: error.message || '签到失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t})\n\t\t\t} finally {\n\t\t\t\tthis.signing = false\n\t\t\t}\n\t\t},\n\n\t\t// 判断是否已签到\n\t\tisSignedDay(day) {\n\t\t\treturn this.signedDays.includes(day.date)\n\t\t},\n\n\t\t// 判断是否今天\n\t\tisToday(day) {\n\t\t\tconst today = this.formatDate(new Date(), 'YYYY-MM-DD')\n\t\t\treturn day.date === today\n\t\t},\n\n\t\t// 判断是否未来日期\n\t\tisFutureDay(day) {\n\t\t\tconst today = new Date()\n\t\t\tconst dayDate = new Date(day.date)\n\t\t\treturn dayDate > today\n\t\t},\n\n\t\t// 格式化日期\n\t\tformatDate(date, format = 'MM-DD') {\n\t\t\tconst d = new Date(date)\n\t\t\tconst year = d.getFullYear()\n\t\t\tconst month = String(d.getMonth() + 1).padStart(2, '0')\n\t\t\tconst day = String(d.getDate()).padStart(2, '0')\n\n\t\t\treturn format\n\t\t\t\t.replace('YYYY', year)\n\t\t\t\t.replace('MM', month)\n\t\t\t\t.replace('DD', day)\n\t\t}\n\t}\n}\n</script>\n\n<style scoped>\n.sign-container {\n\tbackground: #f8f8f8;\n\tmin-height: 100vh;\n\tpadding: 15px;\n}\n\n.sign-card {\n\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n\tborder-radius: 12px;\n\tpadding: 20px;\n\tmargin-bottom: 15px;\n\tcolor: #ffffff;\n}\n\n.card-header {\n\ttext-align: center;\n\tmargin-bottom: 20px;\n}\n\n.title {\n\tdisplay: block;\n\tfont-size: 20px;\n\tfont-weight: bold;\n\tmargin-bottom: 5px;\n}\n\n.subtitle {\n\tdisplay: block;\n\tfont-size: 12px;\n\topacity: 0.8;\n}\n\n.sign-calendar {\n\tbackground: rgba(255, 255, 255, 0.1);\n\tborder-radius: 8px;\n\tpadding: 15px;\n\tmargin-bottom: 20px;\n}\n\n.calendar-header {\n\ttext-align: center;\n\tmargin-bottom: 15px;\n}\n\n.month-text {\n\tfont-size: 16px;\n\tfont-weight: 500;\n}\n\n.week-header {\n\tdisplay: grid;\n\tgrid-template-columns: repeat(7, 1fr);\n\tgap: 5px;\n\tmargin-bottom: 10px;\n}\n\n.week-day {\n\ttext-align: center;\n\tfont-size: 12px;\n\topacity: 0.8;\n}\n\n.calendar-days {\n\tdisplay: grid;\n\tgrid-template-columns: repeat(7, 1fr);\n\tgap: 5px;\n}\n\n.day-item {\n\taspect-ratio: 1;\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: center;\n\tborder-radius: 4px;\n\tposition: relative;\n}\n\n.day-item.signed {\n\tbackground: rgba(103, 194, 58, 0.3);\n}\n\n.day-item.today {\n\tbackground: rgba(255, 255, 255, 0.2);\n\tborder: 1px solid rgba(255, 255, 255, 0.5);\n}\n\n.day-item.future {\n\topacity: 0.5;\n}\n\n.day-number {\n\tfont-size: 12px;\n}\n\n.sign-status {\n\tposition: absolute;\n\tbottom: 2px;\n\tfont-size: 8px;\n}\n\n.sign-action {\n\ttext-align: center;\n}\n\n.sign-btn {\n\twidth: 200px;\n\theight: 44px;\n\tbackground: #ffffff;\n\tborder-radius: 22px;\n\tborder: none;\n\tmargin-bottom: 15px;\n}\n\n.sign-btn.signed {\n\tbackground: rgba(255, 255, 255, 0.3);\n}\n\n.btn-text {\n\tfont-size: 16px;\n\tfont-weight: 500;\n\tcolor: #667eea;\n}\n\n.sign-btn.signed .btn-text {\n\tcolor: rgba(255, 255, 255, 0.8);\n}\n\n.sign-info {\n\tdisplay: flex;\n\tjustify-content: space-around;\n}\n\n.info-text {\n\tfont-size: 12px;\n\topacity: 0.8;\n}\n\n.reward-rules,\n.sign-records {\n\tbackground: #ffffff;\n\tborder-radius: 12px;\n\tpadding: 20px;\n\tmargin-bottom: 15px;\n}\n\n.section-title {\n\tmargin-bottom: 15px;\n}\n\n.title-text {\n\tfont-size: 16px;\n\tfont-weight: 500;\n\tcolor: #333;\n}\n\n.rules-list,\n.records-list {\n\t/* 列表样式 */\n}\n\n.rule-item,\n.record-item {\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 12px 0;\n\tborder-bottom: 1px solid #f5f5f5;\n}\n\n.rule-item:last-child,\n.record-item:last-child {\n\tborder-bottom: none;\n}\n\n.rule-icon {\n\twidth: 40px;\n\theight: 40px;\n\tbackground: rgba(102, 126, 234, 0.1);\n\tborder-radius: 20px;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tmargin-right: 15px;\n}\n\n.day-text {\n\tfont-size: 12px;\n\tcolor: #667eea;\n\tfont-weight: 500;\n}\n\n.rule-info,\n.record-info {\n\tflex: 1;\n}\n\n.rule-title,\n.record-title {\n\tdisplay: block;\n\tfont-size: 14px;\n\tcolor: #333;\n\tmargin-bottom: 4px;\n}\n\n.rule-desc,\n.record-desc {\n\tdisplay: block;\n\tfont-size: 12px;\n\tcolor: #999;\n}\n\n.rule-reward,\n.record-points {\n\tfont-size: 14px;\n\tfont-weight: 500;\n\tcolor: #67c23a;\n}\n\n.record-date {\n\twidth: 60px;\n\tmargin-right: 15px;\n}\n\n.date-text {\n\tfont-size: 12px;\n\tcolor: #999;\n}\n</style>\n", "import MiniProgramPage from 'D:/app/miniprogram/SIYU/pages/activities/sign.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AAwGA,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,SAAS;AAAA,MACT,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,MAChB,eAAe;AAAA,MACf,cAAc;AAAA,MACd,UAAU,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,MAC5C,cAAc,CAAE;AAAA,MAChB,YAAY,CAAE;AAAA,MACd,WAAW;AAAA,QACV;AAAA,UACC,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,OAAO;AAAA,UACP,aAAa;AAAA,UACb,QAAQ;AAAA,QACR;AAAA,QACD;AAAA,UACC,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,OAAO;AAAA,UACP,aAAa;AAAA,UACb,QAAQ;AAAA,QACR;AAAA,QACD;AAAA,UACC,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,OAAO;AAAA,UACP,aAAa;AAAA,UACb,QAAQ;AAAA,QACR;AAAA,QACD;AAAA,UACC,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,OAAO;AAAA,UACP,aAAa;AAAA,UACb,QAAQ;AAAA,QACT;AAAA,MACA;AAAA,MACD,aAAa,CAAC;AAAA,IACf;AAAA,EACA;AAAA,EACD,SAAS;AACR,SAAK,aAAa;AAClB,SAAK,eAAe;AACpB,SAAK,gBAAgB;AAAA,EACrB;AAAA,EACD,SAAS;AAAA;AAAA,IAER,eAAe;AACd,YAAM,MAAM,oBAAI,KAAK;AACrB,YAAM,OAAO,IAAI,YAAY;AAC7B,YAAM,QAAQ,IAAI,SAAS;AAE3B,WAAK,eAAe,GAAG,IAAI,IAAI,QAAQ,CAAC;AAIxC,YAAM,UAAU,IAAI,KAAK,MAAM,QAAQ,GAAG,CAAC;AAG3C,YAAM,OAAO,CAAC;AACd,eAAS,IAAI,GAAG,KAAK,QAAQ,QAAO,GAAI,KAAK;AAC5C,aAAK,KAAK;AAAA,UACT,MAAM,GAAG,IAAI,IAAI,OAAO,QAAQ,CAAC,EAAE,SAAS,GAAG,GAAG,CAAC,IAAI,OAAO,CAAC,EAAE,SAAS,GAAG,GAAG,CAAC;AAAA,UACjF,KAAK;AAAA,SACL;AAAA,MACF;AAEA,WAAK,eAAe;AAAA,IACpB;AAAA;AAAA,IAGD,MAAM,iBAAiB;AACtB,UAAI;AAEH,cAAM,QAAQA,cAAAA,MAAI,eAAe,OAAO;AACxC,YAAI,CAAC,OAAO;AACX;AAAA,QACD;AAGA,aAAK,kBAAkB;AACvB,aAAK,iBAAiB;AACtB,aAAK,gBAAgB;AACrB,aAAK,aAAa;AAAA,UACjB;AAAA,UAAc;AAAA,UAAc;AAAA,UAC5B;AAAA,UAAc;AAAA,QACf;AAAA,MASC,SAAO,OAAO;AACfA,sBAAAA,MAAA,MAAA,SAAA,oCAAc,aAAa,KAAK;AAAA,MACjC;AAAA,IACA;AAAA;AAAA,IAGD,MAAM,kBAAkB;AACvB,UAAI;AAEH,cAAM,QAAQA,cAAAA,MAAI,eAAe,OAAO;AACxC,YAAI,CAAC,OAAO;AACX;AAAA,QACD;AAGA,aAAK,cAAc;AAAA,UAClB;AAAA,YACC,IAAI;AAAA,YACJ,YAAY;AAAA,YACZ,iBAAiB;AAAA,YACjB,QAAQ;AAAA,UACR;AAAA,UACD;AAAA,YACC,IAAI;AAAA,YACJ,YAAY;AAAA,YACZ,iBAAiB;AAAA,YACjB,QAAQ;AAAA,UACT;AAAA,QACD;AAAA,MAMC,SAAO,OAAO;AACfA,sBAAAA,MAAA,MAAA,SAAA,oCAAc,aAAa,KAAK;AAAA,MACjC;AAAA,IACA;AAAA;AAAA,IAGD,MAAM,aAAa;AAClB,UAAI,KAAK,mBAAmB,KAAK;AAAS;AAG1C,YAAM,QAAQA,cAAAA,MAAI,eAAe,OAAO;AACxC,UAAI,CAAC,OAAO;AACXA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,SACN;AACDA,sBAAAA,MAAI,WAAW;AAAA,UACd,KAAK;AAAA,SACL;AACD;AAAA,MACD;AAEA,WAAK,UAAU;AAEf,UAAI;AAEH,cAAM,eAAe;AAAA,UACpB,MAAM;AAAA,YACL,iBAAiB,KAAK,iBAAiB;AAAA,YACvC,QAAQ;AAAA,UACT;AAAA,QACD;AAGA,aAAK,kBAAkB;AACvB,aAAK,iBAAiB,aAAa,KAAK;AACxC,aAAK,iBAAiB;AAGtB,cAAM,QAAQ,KAAK,WAAW,oBAAI,KAAI,GAAI,YAAY;AACtD,aAAK,WAAW,KAAK,KAAK;AAG1BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO,UAAU,aAAa,KAAK,MAAM;AAAA,UACzC,MAAM;AAAA,UACN,UAAU;AAAA,SACV;AAGD,aAAK,gBAAgB;AAAA,MASpB,SAAO,OAAO;AACfA,sBAAAA,yDAAc,SAAS,KAAK;AAC5BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO,MAAM,WAAW;AAAA,UACxB,MAAM;AAAA,SACN;AAAA,MACF,UAAU;AACT,aAAK,UAAU;AAAA,MAChB;AAAA,IACA;AAAA;AAAA,IAGD,YAAY,KAAK;AAChB,aAAO,KAAK,WAAW,SAAS,IAAI,IAAI;AAAA,IACxC;AAAA;AAAA,IAGD,QAAQ,KAAK;AACZ,YAAM,QAAQ,KAAK,WAAW,oBAAI,KAAI,GAAI,YAAY;AACtD,aAAO,IAAI,SAAS;AAAA,IACpB;AAAA;AAAA,IAGD,YAAY,KAAK;AAChB,YAAM,QAAQ,oBAAI,KAAK;AACvB,YAAM,UAAU,IAAI,KAAK,IAAI,IAAI;AACjC,aAAO,UAAU;AAAA,IACjB;AAAA;AAAA,IAGD,WAAW,MAAM,SAAS,SAAS;AAClC,YAAM,IAAI,IAAI,KAAK,IAAI;AACvB,YAAM,OAAO,EAAE,YAAY;AAC3B,YAAM,QAAQ,OAAO,EAAE,SAAQ,IAAK,CAAC,EAAE,SAAS,GAAG,GAAG;AACtD,YAAM,MAAM,OAAO,EAAE,QAAS,CAAA,EAAE,SAAS,GAAG,GAAG;AAE/C,aAAO,OACL,QAAQ,QAAQ,IAAI,EACpB,QAAQ,MAAM,KAAK,EACnB,QAAQ,MAAM,GAAG;AAAA,IACpB;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjVA,GAAG,WAAW,eAAe;"}