"use strict";
const common_vendor = require("../common/vendor.js");
const api_index = require("../api/index.js");
const AuthUtils = {
  /**
   * 检查是否已登录
   */
  isLoggedIn() {
    const token = common_vendor.index.getStorageSync("token");
    const userInfo = common_vendor.index.getStorageSync("userInfo");
    return !!(token && userInfo);
  },
  /**
   * 获取当前用户信息
   */
  getCurrentUser() {
    return common_vendor.index.getStorageSync("userInfo");
  },
  /**
   * 获取当前token
   */
  getToken() {
    return common_vendor.index.getStorageSync("token");
  },
  /**
   * 清除登录数据
   */
  clearLoginData() {
    common_vendor.index.removeStorageSync("token");
    common_vendor.index.removeStorageSync("userInfo");
  },
  /**
   * 保存登录数据
   */
  saveLoginData(token, userInfo) {
    common_vendor.index.setStorageSync("token", token);
    common_vendor.index.setStorageSync("userInfo", userInfo);
  },
  /**
   * 检查登录状态，如果未登录则跳转到登录页
   */
  checkLoginAndRedirect(showModal = true) {
    if (!this.isLoggedIn()) {
      if (showModal) {
        common_vendor.index.showModal({
          title: "提示",
          content: "请先登录",
          confirmText: "去登录",
          success: (res) => {
            if (res.confirm) {
              this.navigateToLogin();
            }
          }
        });
      } else {
        this.navigateToLogin();
      }
      return false;
    }
    return true;
  },
  /**
   * 跳转到登录页
   */
  navigateToLogin() {
    common_vendor.index.navigateTo({
      url: "/pages/login/login"
    });
  },
  /**
   * 验证token有效性
   */
  async verifyToken() {
    try {
      const token = this.getToken();
      if (!token) {
        return false;
      }
      const result = await api_index.authAPI.verifyToken(token);
      if (result.data && result.data.valid) {
        if (result.data.user) {
          common_vendor.index.setStorageSync("userInfo", result.data.user);
        }
        return true;
      } else {
        this.clearLoginData();
        return false;
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/auth.js:102", "验证token失败:", error);
      return this.isLoggedIn();
    }
  },
  /**
   * 微信登录
   */
  async wechatLogin(userInfo = null) {
    try {
      const loginRes = await this.getWechatCode();
      const formattedUserInfo = userInfo ? {
        nickname: userInfo.nickName,
        avatar: userInfo.avatarUrl,
        gender: userInfo.gender,
        city: userInfo.city,
        province: userInfo.province,
        country: userInfo.country
      } : null;
      const result = await api_index.authAPI.login({
        code: loginRes.code,
        userInfo: formattedUserInfo
      });
      this.saveLoginData(result.data.token, result.data.user);
      return result.data;
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/auth.js:137", "微信登录失败:", error);
      throw error;
    }
  },
  /**
   * 获取微信登录code
   */
  getWechatCode() {
    return new Promise((resolve, reject) => {
      common_vendor.index.login({
        provider: "weixin",
        success: resolve,
        fail: reject
      });
    });
  },
  /**
   * 获取微信用户信息
   */
  getWechatUserInfo() {
    return new Promise((resolve, reject) => {
      common_vendor.index.getUserProfile({
        desc: "用于完善用户资料",
        success: resolve,
        fail: reject
      });
    });
  },
  /**
   * 退出登录
   */
  async logout() {
    try {
      const token = this.getToken();
      if (token) {
        await api_index.authAPI.logout(token);
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/auth.js:178", "退出登录API调用失败:", error);
    } finally {
      this.clearLoginData();
      common_vendor.index.showToast({
        title: "已退出登录",
        icon: "success"
      });
    }
  },
  /**
   * 刷新用户信息
   */
  async refreshUserInfo() {
    try {
      if (!this.isLoggedIn()) {
        return null;
      }
      const result = await api_index.authAPI.getUserInfo();
      if (result.data) {
        common_vendor.index.setStorageSync("userInfo", result.data);
        return result.data;
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/auth.js:203", "刷新用户信息失败:", error);
    }
    return null;
  },
  /**
   * 显示登录提示
   */
  showLoginTip(title = "需要登录", content = "请先登录后再进行此操作") {
    common_vendor.index.showModal({
      title,
      content,
      confirmText: "去登录",
      success: (res) => {
        if (res.confirm) {
          this.navigateToLogin();
        }
      }
    });
  }
};
exports.AuthUtils = AuthUtils;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/auth.js.map
