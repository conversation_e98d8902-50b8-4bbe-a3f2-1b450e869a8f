<?php
/**
 * 检查地址表是否存在
 */

header('Content-Type: application/json; charset=utf-8');

try {
    // 加载数据库
    require_once __DIR__ . '/api/core/Database.php';
    
    $db = Database::getInstance();
    
    // 检查 user_addresses 表是否存在
    $sql = "SHOW TABLES LIKE 'user_addresses'";
    $result = $db->query($sql);
    $tables = $result->fetchAll();
    
    if (empty($tables)) {
        echo json_encode([
            'status' => 'error',
            'message' => 'user_addresses 表不存在',
            'solution' => '需要创建 user_addresses 表'
        ], JSON_UNESCAPED_UNICODE);
    } else {
        // 检查表结构
        $sql = "DESCRIBE user_addresses";
        $result = $db->query($sql);
        $columns = $result->fetchAll();
        
        echo json_encode([
            'status' => 'success',
            'message' => 'user_addresses 表存在',
            'columns' => $columns
        ], JSON_UNESCAPED_UNICODE);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ], JSON_UNESCAPED_UNICODE);
}
?>
