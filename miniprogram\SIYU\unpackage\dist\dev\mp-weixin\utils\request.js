"use strict";
const common_vendor = require("../common/vendor.js");
const BASE_URL = "https://siyu.vezii.com/api";
const STATIC_URL = "https://siyu.vezii.com";
const getImageUrl = (imagePath) => {
  if (!imagePath)
    return "";
  if (imagePath.startsWith("http://") || imagePath.startsWith("https://")) {
    return imagePath;
  }
  if (imagePath.startsWith("/")) {
    return STATIC_URL + imagePath;
  }
  return STATIC_URL + "/" + imagePath;
};
const requestInterceptor = (options) => {
  const token = common_vendor.index.getStorageSync("token");
  if (token) {
    options.header = {
      ...options.header,
      "Authorization": `Bearer ${token}`
    };
  }
  options.header = {
    "Content-Type": "application/json",
    ...options.header
  };
  if (options.loading !== false) {
    common_vendor.index.showLoading({
      title: "加载中...",
      mask: true
    });
  }
  return options;
};
const responseInterceptor = (response, options) => {
  if (options.loading !== false) {
    common_vendor.index.hideLoading();
  }
  const { statusCode, data } = response;
  if (statusCode !== 200) {
    common_vendor.index.showToast({
      title: `网络错误 ${statusCode}`,
      icon: "none"
    });
    return Promise.reject(response);
  }
  if (data.code !== 200) {
    if (data.code === 401) {
      common_vendor.index.removeStorageSync("token");
      common_vendor.index.removeStorageSync("userInfo");
      common_vendor.index.navigateTo({
        url: "/pages/login/login"
      });
      return Promise.reject(data);
    }
    if (options.showError !== false) {
      common_vendor.index.showToast({
        title: data.message || "请求失败",
        icon: "none"
      });
    }
    return Promise.reject(data);
  }
  return Promise.resolve(data);
};
const request = (options) => {
  if (!options.url.startsWith("http")) {
    options.url = BASE_URL + options.url;
  }
  options = requestInterceptor(options);
  return new Promise((resolve, reject) => {
    common_vendor.index.request({
      ...options,
      success: (response) => {
        responseInterceptor(response, options).then(resolve).catch(reject);
      },
      fail: (error) => {
        if (options.loading !== false) {
          common_vendor.index.hideLoading();
        }
        common_vendor.index.showToast({
          title: "网络连接失败",
          icon: "none"
        });
        reject(error);
      }
    });
  });
};
const get = (url, data = {}, options = {}) => {
  return request({
    url,
    method: "GET",
    data,
    ...options
  });
};
const post = (url, data = {}, options = {}) => {
  return request({
    url,
    method: "POST",
    data,
    ...options
  });
};
const put = (url, data = {}, options = {}) => {
  return request({
    url,
    method: "PUT",
    data,
    ...options
  });
};
const del = (url, data = {}, options = {}) => {
  return request({
    url,
    method: "DELETE",
    data,
    ...options
  });
};
exports.del = del;
exports.get = get;
exports.getImageUrl = getImageUrl;
exports.post = post;
exports.put = put;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/request.js.map
