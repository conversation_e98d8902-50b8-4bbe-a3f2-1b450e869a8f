
.points-container.data-v-dd6e20da {
	background: #f8f8f8;
	min-height: 100vh;
}
.balance-card.data-v-dd6e20da {
	margin: 15px;
	border-radius: 12px;
	overflow: hidden;
	box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
}
.card-bg.data-v-dd6e20da {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 25px 20px;
	color: #ffffff;
}
.balance-info.data-v-dd6e20da {
	text-align: center;
	margin-bottom: 25px;
}
.balance-label.data-v-dd6e20da {
	display: block;
	font-size: 14px;
	opacity: 0.9;
	margin-bottom: 8px;
}
.balance-amount.data-v-dd6e20da {
	display: block;
	font-size: 36px;
	font-weight: bold;
}
.card-actions.data-v-dd6e20da {
	display: flex;
	justify-content: space-around;
}
.action-btn.data-v-dd6e20da {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 10px 20px;
	background: rgba(255, 255, 255, 0.2);
	border-radius: 8px;
	min-width: 80px;
}
.action-text.data-v-dd6e20da {
	font-size: 12px;
	margin-top: 5px;
}
.function-grid.data-v-dd6e20da {
	display: grid;
	grid-template-columns: repeat(4, 1fr);
	gap: 15px;
	padding: 20px 15px;
	background: #ffffff;
	margin: 15px;
	border-radius: 12px;
}
.grid-item.data-v-dd6e20da {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 15px 5px;
}
.grid-icon.data-v-dd6e20da {
	width: 50px;
	height: 50px;
	background: rgba(102, 126, 234, 0.1);
	border-radius: 25px;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 8px;
}
.grid-text.data-v-dd6e20da {
	font-size: 12px;
	color: #333;
}
.records-section.data-v-dd6e20da {
	background: #ffffff;
	margin: 15px;
	border-radius: 12px;
	padding: 20px;
}
.section-header.data-v-dd6e20da {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 15px;
}
.section-title.data-v-dd6e20da {
	font-size: 16px;
	font-weight: 500;
	color: #333;
}
.more-btn.data-v-dd6e20da {
	font-size: 12px;
	color: #667eea;
}
.records-list.data-v-dd6e20da {
	/* 记录列表样式 */
}
.record-item.data-v-dd6e20da {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 12px 0;
	border-bottom: 1px solid #f5f5f5;
}
.record-item.data-v-dd6e20da:last-child {
	border-bottom: none;
}
.record-info.data-v-dd6e20da {
	flex: 1;
}
.record-title.data-v-dd6e20da {
	display: block;
	font-size: 14px;
	color: #333;
	margin-bottom: 4px;
}
.record-time.data-v-dd6e20da {
	display: block;
	font-size: 12px;
	color: #999;
}
.record-points.data-v-dd6e20da {
	font-size: 16px;
	font-weight: 500;
	color: #f56c6c;
}
.record-points.positive.data-v-dd6e20da {
	color: #67c23a;
}
