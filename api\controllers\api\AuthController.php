<?php
/**
 * 认证控制器
 * 处理用户登录、注册、Token相关操作
 */

declare(strict_types=1);

require_once __DIR__ . '/../../core/Response.php';
require_once __DIR__ . '/../../services/AuthService.php';
require_once __DIR__ . '/../../middleware/AuthMiddleware.php';

class AuthController
{
    private AuthService $authService;
    private AuthMiddleware $authMiddleware;

    public function __construct()
    {
        $this->authService = new AuthService();
        $this->authMiddleware = new AuthMiddleware();
    }

    /**
     * 微信小程序登录
     * POST /api/auth/login
     */
    public function login(): void
    {
        try {
            // 获取请求参数 - 支持GET和POST
            if ($_SERVER['REQUEST_METHOD'] === 'GET') {
                $input = $_GET;
            } else {
                $input = $this->getJsonInput();
            }

            // 验证必需参数
            if (empty($input['code'])) {
                Response::validationError(['code' => '微信登录code不能为空']);
            }

            // 记录登录尝试
            $this->logLoginAttempt($input);

            // 执行登录
            $result = $this->authService->wechatLogin(
                $input['code'],
                $input['userInfo'] ?? null
            );

            // 添加登录成功的额外信息
            $result['login_time'] = date('Y-m-d H:i:s');

            // 检查是否为新用户
            $user = $result['user'];
            if (is_object($user) && method_exists($user, 'isNewUser')) {
                // 如果是 User 对象，使用其 isNewUser 方法
                $result['is_new_user'] = $user->isNewUser();
                $result['user'] = $user->toArray(); // 确保返回数组格式
            } else if (is_array($user)) {
                // 如果已经是数组，直接比较时间戳
                $result['is_new_user'] = isset($user['created_at']) && isset($user['updated_at']) &&
                                        $user['created_at'] === $user['updated_at'];
            } else {
                // 默认情况
                $result['is_new_user'] = false;
            }

            // 如果是新用户，给予注册奖励
            if ($result['is_new_user']) {
                $this->giveNewUserReward($result['user']['id']);
                $result['new_user_reward'] = true;
            }

            Response::success($result, $result['is_new_user'] ? '注册成功' : '登录成功');

        } catch (Exception $e) {
            // 记录详细错误信息
            error_log('登录失败详细信息: ' . $e->getMessage());
            error_log('登录失败堆栈: ' . $e->getTraceAsString());

            // 返回更详细的错误信息用于调试
            if ($_ENV['APP_DEBUG'] === 'true') {
                Response::businessError($e->getMessage() . ' (Debug: ' . $e->getFile() . ':' . $e->getLine() . ')');
            } else {
                Response::businessError($e->getMessage());
            }
        }
    }

    /**
     * 使用加密用户信息登录
     * POST /api/auth/login-with-userinfo
     */
    public function loginWithUserInfo(): void
    {
        try {
            $input = $this->getJsonInput();

            // 验证必需参数
            $required = ['code', 'encryptedData', 'iv'];
            $errors = [];

            foreach ($required as $field) {
                if (empty($input[$field])) {
                    $errors[$field] = "{$field}不能为空";
                }
            }

            if (!empty($errors)) {
                Response::validationError($errors);
            }

            // 执行登录
            $result = $this->authService->wechatLoginWithEncryptedData(
                $input['code'],
                $input['encryptedData'],
                $input['iv']
            );

            Response::success($result, '登录成功');

        } catch (Exception $e) {
            Response::businessError($e->getMessage());
        }
    }

    /**
     * 刷新Token
     * POST /api/auth/refresh
     */
    public function refresh(): void
    {
        try {
            $input = $this->getJsonInput();

            if (empty($input['token'])) {
                Response::validationError(['token' => 'Token不能为空']);
            }

            $newToken = $this->authService->refreshToken($input['token']);

            Response::success([
                'token' => $newToken
            ], 'Token刷新成功');

        } catch (Exception $e) {
            Response::businessError($e->getMessage());
        }
    }

    /**
     * 退出登录
     * POST /api/auth/logout
     */
    public function logout(): void
    {
        try {
            // 验证认证
            $this->authMiddleware->handle();

            $input = $this->getJsonInput();
            $token = $input['token'] ?? $this->getTokenFromHeader();

            if ($token) {
                $this->authService->logout($token);
            }

            Response::success(null, '退出成功');

        } catch (Exception $e) {
            Response::success(null, '退出成功'); // 即使失败也返回成功
        }
    }

    /**
     * 获取当前用户信息
     * GET /api/auth/me
     */
    public function me(): void
    {
        try {
            // 验证认证
            $authInfo = $this->authMiddleware->handle();

            $user = $authInfo['user'];
            $userArray = $user->toArray();

            // 手动转换DateTime字段为字符串
            foreach (['last_login_at', 'created_at', 'updated_at'] as $dateField) {
                if (isset($userArray[$dateField]) && $userArray[$dateField] instanceof DateTime) {
                    $userArray[$dateField] = $userArray[$dateField]->format('Y-m-d H:i:s');
                }
            }

            // 添加积分信息
            $userArray['points'] = $user->getPointsStats();

            Response::success($userArray, '获取用户信息成功');

        } catch (Exception $e) {
            Response::businessError($e->getMessage());
        }
    }

    /**
     * 验证Token
     * POST /api/auth/verify
     */
    public function verify(): void
    {
        try {
            $input = $this->getJsonInput();

            if (empty($input['token'])) {
                Response::validationError(['token' => 'Token不能为空']);
            }

            $authInfo = $this->authService->validateToken($input['token']);

            Response::success([
                'valid' => true,
                'user_id' => $authInfo['user_id'],
                'expires_at' => $authInfo['exp'] ?? null
            ], 'Token有效');

        } catch (Exception $e) {
            Response::success([
                'valid' => false,
                'error' => $e->getMessage()
            ], 'Token无效');
        }
    }

    /**
     * 更新用户信息
     * PUT /api/auth/profile
     */
    public function updateProfile(): void
    {
        try {
            // 验证认证
            $this->authMiddleware->handle();

            $input = $this->getJsonInput();
            $user = AuthMiddleware::getCurrentUser();

            // 可更新的字段
            $allowedFields = ['nickname', 'avatar', 'phone'];
            $updated = false;

            foreach ($allowedFields as $field) {
                if (isset($input[$field]) && $input[$field] !== $user->$field) {
                    $user->$field = $input[$field];
                    $updated = true;
                }
            }

            if ($updated) {
                $user->save();
            }

            Response::success($user->toArray(), '用户信息更新成功');

        } catch (Exception $e) {
            Response::businessError($e->getMessage());
        }
    }

    /**
     * 绑定手机号（可选功能）
     * POST /api/auth/bind-phone
     */
    public function bindPhone(): void
    {
        try {
            // 验证认证
            $authInfo = $this->authMiddleware->handle();

            $input = $this->getJsonInput();

            if (empty($input['phone'])) {
                Response::validationError(['phone' => '手机号不能为空']);
            }

            // 验证手机号格式
            if (!preg_match('/^1[3-9]\d{9}$/', $input['phone'])) {
                Response::validationError(['phone' => '手机号格式不正确']);
            }

            $user = $authInfo['user'];

            // 检查手机号是否已被其他用户使用
            $existingUser = User::findByPhone($input['phone']);
            if ($existingUser && $existingUser->id !== $user->id) {
                Response::businessError('该手机号已被其他用户绑定');
            }

            $user->phone = $input['phone'];
            $user->save();

            // 记录绑定操作
            $this->logUserAction($user->id, 'bind_phone', ['phone' => $input['phone']]);

            Response::success($user->toArray(), '手机号绑定成功');

        } catch (Exception $e) {
            Response::businessError($e->getMessage());
        }
    }



    /**
     * 获取用户完整信息
     * GET /api/auth/user-info
     */
    public function getUserInfo(): void
    {
        try {
            // 验证认证
            $authInfo = $this->authMiddleware->handle();

            $user = $authInfo['user'];
            $userArray = $user->toArray();

            // 添加积分信息
            $userArray['points'] = $user->getPointsStats();

            // 添加用户统计
            $userArray['stats'] = $this->getUserStats($user->id);

            // 检查资料完整度
            $userArray['profile'] = $this->checkUserProfileComplete($userArray);

            Response::success($userArray, '获取用户信息成功');

        } catch (Exception $e) {
            Response::businessError($e->getMessage());
        }
    }

    /**
     * 获取JSON输入
     */
    private function getJsonInput(): array
    {
        $input = file_get_contents('php://input');

        if (empty($input)) {
            return $_POST;
        }

        $data = json_decode($input, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            Response::validationError(['json' => 'JSON格式错误']);
        }

        return $data ?? [];
    }

    /**
     * 从请求头获取Token
     */
    private function getTokenFromHeader(): ?string
    {
        $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';

        if (str_starts_with($authHeader, 'Bearer ')) {
            return substr($authHeader, 7);
        }

        return null;
    }

    /**
     * 记录登录尝试
     */
    private function logLoginAttempt(array $input): void
    {
        try {
            $logData = [
                'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
                'has_userinfo' => !empty($input['userInfo']),
                'timestamp' => date('Y-m-d H:i:s')
            ];

            // 这里可以记录到日志文件或数据库
            error_log('微信登录尝试: ' . json_encode($logData));

        } catch (Exception $e) {
            // 日志记录失败不影响主流程
        }
    }

    /**
     * 给新用户奖励
     */
    private function giveNewUserReward(int $userId): void
    {
        try {
            require_once __DIR__ . '/../../services/PointsService.php';

            $pointsService = new PointsService();

            // 新用户注册奖励100积分
            $pointsService->addPoints($userId, 100, 'register', null, '新用户注册奖励');

        } catch (Exception $e) {
            // 奖励发放失败不影响登录流程
            error_log('新用户奖励发放失败: ' . $e->getMessage());
        }
    }

    /**
     * 检查用户是否需要完善信息
     */
    private function checkUserProfileComplete(array $user): array
    {
        $incomplete = [];

        if (empty($user['nickname'])) {
            $incomplete[] = 'nickname';
        }

        if (empty($user['avatar'])) {
            $incomplete[] = 'avatar';
        }

        if (empty($user['phone'])) {
            $incomplete[] = 'phone';
        }

        return [
            'is_complete' => empty($incomplete),
            'missing_fields' => $incomplete
        ];
    }

    /**
     * 获取用户统计信息
     */
    private function getUserStats(int $userId): array
    {
        try {
            $db = Database::getInstance();

            // 获取订单统计
            $orderStats = $db->find(
                'orders',
                'user_id = ?',
                [$userId],
                'COUNT(*) as total_orders, SUM(total_points) as total_spent'
            );

            // 获取积分统计
            $pointsStats = $db->find(
                'points_records',
                'user_id = ?',
                [$userId],
                'SUM(CASE WHEN points > 0 THEN points ELSE 0 END) as total_earned,
                 SUM(CASE WHEN points < 0 THEN ABS(points) ELSE 0 END) as total_spent'
            );

            return [
                'total_orders' => (int)($orderStats['total_orders'] ?? 0),
                'total_points_spent' => (int)($orderStats['total_spent'] ?? 0),
                'total_points_earned' => (int)($pointsStats['total_earned'] ?? 0),
                'days_since_register' => 0 // 可以根据注册时间计算
            ];

        } catch (Exception $e) {
            return [
                'total_orders' => 0,
                'total_points_spent' => 0,
                'total_points_earned' => 0,
                'days_since_register' => 0
            ];
        }
    }



    /**
     * 记录用户操作
     */
    private function logUserAction(int $userId, string $action, array $data = []): void
    {
        try {
            $db = Database::getInstance();
            $db->insert('user_logs', [
                'user_id' => $userId,
                'action' => $action,
                'data' => json_encode($data),
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? null,
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null,
                'created_at' => date('Y-m-d H:i:s')
            ]);
        } catch (Exception $e) {
            // 日志记录失败不影响主流程
            error_log('用户操作日志记录失败: ' . $e->getMessage());
        }
    }
}
