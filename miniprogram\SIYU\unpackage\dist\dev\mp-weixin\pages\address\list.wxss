
.address-list-container.data-v-90a3874e {
	background: #f8f8f8;
	min-height: 100vh;
	padding: 10px;
}
.address-list.data-v-90a3874e {
	margin-bottom: 80px;
}
.address-item.data-v-90a3874e {
	background: white;
	border-radius: 12px;
	margin-bottom: 10px;
	padding: 20px;
	position: relative;
	box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}
.address-content.data-v-90a3874e {
	margin-bottom: 15px;
}
.address-header.data-v-90a3874e {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 10px;
}
.name-phone.data-v-90a3874e {
	display: flex;
	align-items: center;
}
.name.data-v-90a3874e {
	font-size: 16px;
	font-weight: bold;
	color: #333;
	margin-right: 15px;
}
.phone.data-v-90a3874e {
	font-size: 14px;
	color: #666;
}
.default-badge.data-v-90a3874e {
	background: #667eea;
	color: white;
	padding: 4px 8px;
	border-radius: 4px;
	font-size: 12px;
}
.address-detail.data-v-90a3874e {
	font-size: 14px;
	color: #666;
	line-height: 1.4;
}
.address-actions.data-v-90a3874e {
	display: flex;
	justify-content: flex-end;
	gap: 20px;
}
.action-btn.data-v-90a3874e {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 8px;
	min-width: 50px;
}
.action-btn text.data-v-90a3874e:first-child {
	font-size: 18px;
	margin-bottom: 4px;
}
.action-btn text.data-v-90a3874e:last-child {
	font-size: 12px;
	color: #666;
}
.empty-state.data-v-90a3874e {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 80px 20px;
	text-align: center;
}
.empty-icon.data-v-90a3874e {
	font-size: 60px;
	margin-bottom: 20px;
}
.empty-text.data-v-90a3874e {
	font-size: 16px;
	color: #333;
	margin-bottom: 8px;
}
.empty-desc.data-v-90a3874e {
	font-size: 14px;
	color: #999;
}
.add-address-btn.data-v-90a3874e {
	position: fixed;
	bottom: 30px;
	left: 20px;
	right: 20px;
	background: #667eea;
	color: white;
	padding: 15px;
	border-radius: 25px;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}
.btn-icon.data-v-90a3874e {
	font-size: 20px;
	margin-right: 8px;
}
.btn-text.data-v-90a3874e {
	font-size: 16px;
	font-weight: bold;
}

/* 图标字体样式 */
.iconfont.data-v-90a3874e {
	font-family: 'iconfont';
}
.icon-edit.data-v-90a3874e::before { content: '✏️';
}
.icon-delete.data-v-90a3874e::before { content: '🗑️';
}
.icon-star.data-v-90a3874e::before { content: '⭐';
}
