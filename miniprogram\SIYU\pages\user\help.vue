<template>
	<view class="help-container">
		<!-- 搜索框 -->
		<view class="search-section">
			<view class="search-box">
				<uni-icons type="search" size="16" color="#999"></uni-icons>
				<input 
					class="search-input" 
					placeholder="搜索帮助内容"
					v-model="searchKeyword"
					@input="onSearch"
				/>
			</view>
		</view>

		<!-- 常见问题 -->
		<view class="section">
			<view class="section-title">常见问题</view>
			<view class="faq-list">
				<view 
					v-for="(item, index) in filteredFaqList" 
					:key="index"
					class="faq-item"
					@click="toggleFaq(index)"
				>
					<view class="faq-question">
						<text class="question-text">{{ item.question }}</text>
						<uni-icons 
							:type="item.expanded ? 'up' : 'down'" 
							size="14" 
							color="#999"
						></uni-icons>
					</view>
					<view v-if="item.expanded" class="faq-answer">
						<text class="answer-text">{{ item.answer }}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 联系我们 -->
		<view class="section">
			<view class="section-title">联系我们</view>
			<view class="contact-list">
				<view class="contact-item" @click="callService">
					<view class="contact-left">
						<uni-icons type="phone" size="20" color="#667eea"></uni-icons>
						<text class="contact-text">客服电话</text>
					</view>
					<text class="contact-value">************</text>
				</view>
				
				<view class="contact-item" @click="copyEmail">
					<view class="contact-left">
						<uni-icons type="email" size="20" color="#e74c3c"></uni-icons>
						<text class="contact-text">客服邮箱</text>
					</view>
					<text class="contact-value"><EMAIL></text>
				</view>
				
				<view class="contact-item">
					<view class="contact-left">
						<uni-icons type="clock" size="20" color="#f39c12"></uni-icons>
						<text class="contact-text">服务时间</text>
					</view>
					<text class="contact-value">9:00-18:00</text>
				</view>
			</view>
		</view>

		<!-- 意见反馈 -->
		<view class="section">
			<button class="feedback-btn" @click="goToFeedback">
				<uni-icons type="compose" size="18" color="white"></uni-icons>
				<text class="feedback-text">意见反馈</text>
			</button>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			searchKeyword: '',
			faqList: [
				{
					question: '如何使用积分兑换商品？',
					answer: '在商品详情页点击"立即兑换"，选择收货地址，确认订单即可完成兑换。',
					expanded: false
				},
				{
					question: '积分有有效期吗？',
					answer: '积分永久有效，不会过期。您可以随时使用积分兑换心仪的商品。',
					expanded: false
				},
				{
					question: '如何获得更多积分？',
					answer: '您可以通过每日签到、完成任务、邀请好友等方式获得积分奖励。',
					expanded: false
				},
				{
					question: '订单发货后多久能收到？',
					answer: '一般情况下，订单发货后3-7个工作日内可以收到商品，具体时间根据地区而定。',
					expanded: false
				},
				{
					question: '可以退换货吗？',
					answer: '商品如有质量问题，支持7天无理由退换货。请联系客服处理。',
					expanded: false
				},
				{
					question: '如何修改收货地址？',
					answer: '在"我的-收货地址"中可以添加、编辑或删除收货地址。',
					expanded: false
				}
			]
		}
	},
	
	computed: {
		filteredFaqList() {
			if (!this.searchKeyword) {
				return this.faqList
			}
			return this.faqList.filter(item => 
				item.question.includes(this.searchKeyword) || 
				item.answer.includes(this.searchKeyword)
			)
		}
	},
	
	methods: {
		// 搜索
		onSearch() {
			// 搜索逻辑已在computed中处理
		},
		
		// 切换FAQ展开状态
		toggleFaq(index) {
			this.faqList[index].expanded = !this.faqList[index].expanded
		},
		
		// 拨打客服电话
		callService() {
			uni.makePhoneCall({
				phoneNumber: '************'
			})
		},
		
		// 复制邮箱
		copyEmail() {
			uni.setClipboardData({
				data: '<EMAIL>',
				success: () => {
					uni.showToast({
						title: '邮箱已复制',
						icon: 'success'
					})
				}
			})
		},
		
		// 意见反馈
		goToFeedback() {
			uni.showToast({
				title: '功能开发中',
				icon: 'none'
			})
		}
	}
}
</script>

<style scoped>
.help-container {
	min-height: 100vh;
	background-color: #f5f5f5;
}

/* 搜索区域 */
.search-section {
	background: white;
	padding: 30rpx;
	margin-bottom: 20rpx;
}

.search-box {
	display: flex;
	align-items: center;
	background: #f8f9fa;
	border-radius: 50rpx;
	padding: 20rpx 30rpx;
}

.search-input {
	flex: 1;
	margin-left: 20rpx;
	font-size: 28rpx;
	color: #333;
}

/* 通用区域样式 */
.section {
	background: white;
	margin-bottom: 20rpx;
	padding: 30rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 30rpx;
}

/* FAQ样式 */
.faq-list {
	
}

.faq-item {
	border-bottom: 1rpx solid #f0f0f0;
	padding: 30rpx 0;
}

.faq-item:last-child {
	border-bottom: none;
}

.faq-question {
	display: flex;
	justify-content: space-between;
	align-items: center;
	cursor: pointer;
}

.question-text {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
}

.faq-answer {
	margin-top: 20rpx;
	padding-top: 20rpx;
	border-top: 1rpx solid #f8f9fa;
}

.answer-text {
	font-size: 26rpx;
	color: #666;
	line-height: 1.6;
}

/* 联系我们样式 */
.contact-list {
	
}

.contact-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
}

.contact-item:last-child {
	border-bottom: none;
}

.contact-left {
	display: flex;
	align-items: center;
}

.contact-text {
	font-size: 28rpx;
	color: #333;
	margin-left: 20rpx;
}

.contact-value {
	font-size: 26rpx;
	color: #667eea;
}

/* 反馈按钮 */
.feedback-btn {
	display: flex;
	align-items: center;
	justify-content: center;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	border: none;
	border-radius: 50rpx;
	padding: 30rpx;
	width: 100%;
}

.feedback-text {
	margin-left: 10rpx;
	font-size: 28rpx;
}
</style>
