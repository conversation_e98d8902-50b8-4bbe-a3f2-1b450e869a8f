<view class="region-picker data-v-fef920e2"><view wx:if="{{a}}" class="mask data-v-fef920e2" bindtap="{{b}}"></view><view class="{{['picker-container', 'data-v-fef920e2', w && 'show']}}"><view class="picker-header data-v-fef920e2"><text class="cancel-btn data-v-fef920e2" bindtap="{{c}}">取消</text><text class="title data-v-fef920e2">选择地区</text><text class="confirm-btn data-v-fef920e2" bindtap="{{d}}">确定</text></view><view class="tabs data-v-fef920e2"><view class="{{['tab-item', 'data-v-fef920e2', f && 'active']}}" bindtap="{{g}}"><text class="data-v-fef920e2">{{e}}</text></view><view wx:if="{{h}}" class="{{['tab-item', 'data-v-fef920e2', j && 'active']}}" bindtap="{{k}}"><text class="data-v-fef920e2">{{i}}</text></view><view wx:if="{{l}}" class="{{['tab-item', 'data-v-fef920e2', n && 'active']}}" bindtap="{{o}}"><text class="data-v-fef920e2">{{m}}</text></view></view><scroll-view class="picker-content data-v-fef920e2" scroll-y><view wx:if="{{p}}" class="data-v-fef920e2"><view wx:for="{{q}}" wx:for-item="item" wx:key="d" class="{{['list-item', 'data-v-fef920e2', item.c && 'selected']}}" bindtap="{{item.e}}"><text class="item-name data-v-fef920e2">{{item.a}}</text><text wx:if="{{item.b}}" class="check-icon data-v-fef920e2">✓</text></view></view><view wx:if="{{r}}" class="data-v-fef920e2"><view wx:for="{{s}}" wx:for-item="item" wx:key="d" class="{{['list-item', 'data-v-fef920e2', item.c && 'selected']}}" bindtap="{{item.e}}"><text class="item-name data-v-fef920e2">{{item.a}}</text><text wx:if="{{item.b}}" class="check-icon data-v-fef920e2">✓</text></view></view><view wx:if="{{t}}" class="data-v-fef920e2"><view wx:for="{{v}}" wx:for-item="item" wx:key="d" class="{{['list-item', 'data-v-fef920e2', item.c && 'selected']}}" bindtap="{{item.e}}"><text class="item-name data-v-fef920e2">{{item.a}}</text><text wx:if="{{item.b}}" class="check-icon data-v-fef920e2">✓</text></view></view></scroll-view></view></view>