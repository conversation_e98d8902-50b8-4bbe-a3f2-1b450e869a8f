<?php
/**
 * 直接测试地址API
 */

header('Content-Type: application/json; charset=utf-8');

try {
    // 模拟创建地址的请求
    $testData = [
        'name' => '测试用户',
        'phone' => '13800138000',
        'province' => '北京市',
        'city' => '市辖区',
        'district' => '朝阳区',
        'detail' => '测试详细地址',
        'is_default' => true
    ];
    
    // 设置请求方法和数据
    $_SERVER['REQUEST_METHOD'] = 'POST';
    $_SERVER['REQUEST_URI'] = '/api/addresses';
    
    // 模拟JSON输入
    $jsonInput = json_encode($testData);
    
    // 加载必要的文件
    require_once __DIR__ . '/api/core/Database.php';
    require_once __DIR__ . '/api/core/Response.php';
    require_once __DIR__ . '/api/middleware/AuthMiddleware.php';
    
    // 检查文件是否能正常加载
    echo json_encode([
        'status' => 'success',
        'message' => '基础文件加载成功',
        'step' => 1
    ], JSON_UNESCAPED_UNICODE);
    
    // 尝试加载 AddressController
    require_once __DIR__ . '/api/controllers/api/AddressController.php';
    
    echo json_encode([
        'status' => 'success',
        'message' => 'AddressController 加载成功',
        'step' => 2
    ], JSON_UNESCAPED_UNICODE);
    
    // 尝试创建实例
    $controller = new AddressController();
    
    echo json_encode([
        'status' => 'success',
        'message' => 'AddressController 实例创建成功',
        'step' => 3
    ], JSON_UNESCAPED_UNICODE);
    
} catch (ParseError $e) {
    echo json_encode([
        'status' => 'parse_error',
        'message' => '语法错误: ' . $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'step' => 'parse_error'
    ], JSON_UNESCAPED_UNICODE);
} catch (Error $e) {
    echo json_encode([
        'status' => 'fatal_error',
        'message' => '致命错误: ' . $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'step' => 'fatal_error'
    ], JSON_UNESCAPED_UNICODE);
} catch (Exception $e) {
    echo json_encode([
        'status' => 'exception',
        'message' => '异常: ' . $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'step' => 'exception'
    ], JSON_UNESCAPED_UNICODE);
}
?>
