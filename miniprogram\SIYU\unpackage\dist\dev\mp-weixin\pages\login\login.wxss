
.login-container.data-v-e4e4508d {
	min-height: 100vh;
	background: #f8f9fa;
	display: flex;
	flex-direction: column;
}

/* 顶部导航 */
.nav-bar.data-v-e4e4508d {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 44rpx 40rpx 20rpx;
	background: #fff;
}
.nav-left.data-v-e4e4508d {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}
.nav-title.data-v-e4e4508d {
	font-size: 36rpx;
	font-weight: 500;
	color: #333;
}
.nav-right.data-v-e4e4508d {
	width: 60rpx; /* 保持布局平衡 */
}

/* 主要内容 */
.main-content.data-v-e4e4508d {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 0 60rpx;
}

/* Logo区域 */
.logo-section.data-v-e4e4508d {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-top: 100rpx;
}
.logo-container.data-v-e4e4508d {
	position: relative;
	display: flex;
	align-items: center;
	justify-content: center;
}
.logo-bg.data-v-e4e4508d {
	width: 200rpx;
	height: 120rpx;
	background: #ff4757;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 8rpx;
	z-index: 2;
	position: relative;
}
.logo-text.data-v-e4e4508d {
	font-size: 48rpx;
	font-weight: bold;
	color: white;
}
.logo-decoration.data-v-e4e4508d {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 1;
}
.decoration-left.data-v-e4e4508d,
.decoration-right.data-v-e4e4508d {
	position: absolute;
	width: 120rpx;
	height: 60rpx;
	border: 6rpx solid #ddd;
	border-radius: 30rpx;
}
.decoration-left.data-v-e4e4508d {
	top: -20rpx;
	left: -80rpx;
	transform: rotate(-30deg);
}
.decoration-right.data-v-e4e4508d {
	bottom: -20rpx;
	right: -80rpx;
	transform: rotate(30deg);
}

/* 协议区域 */
.agreement-section.data-v-e4e4508d {
	display: flex;
	align-items: center;
	margin-bottom: 60rpx;
	flex-wrap: wrap;
	justify-content: center;
}
.agreement-checkbox.data-v-e4e4508d {
	margin-right: 16rpx;
}
.checkbox-square.data-v-e4e4508d {
	width: 32rpx;
	height: 32rpx;
	border: 2rpx solid #ff4757;
	border-radius: 4rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background: white;
}
.checkbox-square.checked.data-v-e4e4508d {
	background: #ff4757;
}
.checkbox-mark.data-v-e4e4508d {
	width: 16rpx;
	height: 16rpx;
	background: white;
	border-radius: 2rpx;
}
.agreement-text.data-v-e4e4508d {
	font-size: 28rpx;
	color: #666;
	margin-right: 8rpx;
}
.agreement-link.data-v-e4e4508d {
	font-size: 28rpx;
	color: #007aff;
	margin-right: 8rpx;
}

/* 登录按钮 */
.wechat-login-btn.data-v-e4e4508d {
	width: 100%;
	height: 96rpx;
	background: #ff4757;
	border-radius: 48rpx;
	border: none;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 40rpx;
	font-size: 32rpx;
	font-weight: 500;
	color: white;
}
.wechat-login-btn.disabled.data-v-e4e4508d {
	background: #ccc;
	color: #999;
}

/* 先去逛逛按钮 */
.browse-btn.data-v-e4e4508d {
	width: 100%;
	height: 96rpx;
	background: transparent;
	border: 2rpx solid #ddd;
	border-radius: 48rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 60rpx;
	font-size: 32rpx;
	color: #666;
}
