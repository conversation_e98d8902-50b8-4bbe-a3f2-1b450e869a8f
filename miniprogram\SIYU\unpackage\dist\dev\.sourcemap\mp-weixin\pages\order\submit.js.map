{"version": 3, "file": "submit.js", "sources": ["pages/order/submit.vue", "../../../Program Files/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvb3JkZXIvc3VibWl0LnZ1ZQ"], "sourcesContent": ["<template>\n\t<view class=\"submit-container\">\n\t\t<!-- 页面标题 -->\n\t\t<view class=\"header\">\n\t\t\t<text class=\"title\">订单积分转换</text>\n\t\t\t<text class=\"subtitle\">通过ERP系统自动验证订单，实时获得积分奖励</text>\n\t\t</view>\n\n\t\t<!-- 订单信息表单 -->\n\t\t<view class=\"form-section\">\n\t\t\t<view class=\"form-item\">\n\t\t\t\t<text class=\"label\">订单平台 *</text>\n\t\t\t\t<picker @change=\"onPlatformChange\" :value=\"platformIndex\" :range=\"platforms\">\n\t\t\t\t\t<view class=\"picker\">\n\t\t\t\t\t\t<text>{{ platforms[platformIndex] || '请选择平台' }}</text>\n\t\t\t\t\t\t<text class=\"arrow\">›</text>\n\t\t\t\t\t</view>\n\t\t\t\t</picker>\n\t\t\t</view>\n\n\t\t\t<view class=\"form-item\">\n\t\t\t\t<text class=\"label\">订单号 *</text>\n\t\t\t\t<input\n\t\t\t\t\tclass=\"input\"\n\t\t\t\t\ttype=\"text\"\n\t\t\t\t\tplaceholder=\"请输入订单号\"\n\t\t\t\t\tv-model=\"orderNumber\"\n\t\t\t\t/>\n\t\t\t</view>\n\n\n\t\t</view>\n\n\t\t<!-- 积分规则说明 -->\n\t\t<view class=\"rules-section\">\n\t\t\t<view class=\"rules-title\">\n\t\t\t\t<text class=\"rules-icon\">📋</text>\n\t\t\t\t<text class=\"rules-text\">转换规则</text>\n\t\t\t</view>\n\t\t\t<rich-text class=\"rules-content\" :nodes=\"rulesContent\"></rich-text>\n\t\t</view>\n\n\t\t<!-- 提交按钮 -->\n\t\t<view class=\"submit-section\">\n\t\t\t<button\n\t\t\t\tclass=\"submit-btn\"\n\t\t\t\t:class=\"{ disabled: !canSubmit }\"\n\t\t\t\t:disabled=\"!canSubmit\"\n\t\t\t\t@click=\"submitOrder\"\n\t\t\t>\n\t\t\t\t{{ submitting ? '验证中...' : '立即转换' }}\n\t\t\t</button>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport { orderAPI, systemAPI } from '@/api/index.js'\n\timport AuthUtils from '@/utils/auth.js'\n\timport { processHtmlImages } from '@/utils/common.js'\n\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tplatforms: ['淘宝', '天猫', '京东', '拼多多', '抖音', '快手', '其他'],\n\t\t\t\tplatformIndex: 0,\n\t\t\t\torderNumber: '',\n\t\t\t\tsubmitting: false,\n\t\t\t\trulesContent: ''\n\t\t\t}\n\t\t},\n\t\tonLoad() {\n\t\t\tthis.loadRules()\n\t\t},\n\t\tcomputed: {\n\t\t\tcanSubmit() {\n\t\t\t\treturn this.platformIndex >= 0 &&\n\t\t\t\t\t   this.orderNumber.trim() &&\n\t\t\t\t\t   !this.submitting\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\t// 加载转换规则\n\t\t\tasync loadRules() {\n\t\t\t\ttry {\n\t\t\t\t\tconst result = await systemAPI.getConfig()\n\t\t\t\t\tconst config = result.data || {}\n\n\t\t\t\t\t// 获取订单转换规则内容\n\t\t\t\t\tlet rulesContent = config.order_exchange_rules || `\n\t\t\t\t\t\t<div class=\"rule-item\">• 通过ERP系统自动验证订单真实性</div>\n\t\t\t\t\t\t<div class=\"rule-item\">• 系统自动获取订单金额并计算积分</div>\n\t\t\t\t\t\t<div class=\"rule-item\">• 每消费1元可获得1积分</div>\n\t\t\t\t\t\t<div class=\"rule-item\">• 验证通过后积分立即到账</div>\n\t\t\t\t\t\t<div class=\"rule-item\">• 每个订单号只能转换一次</div>\n\t\t\t\t\t`\n\n\t\t\t\t\t// 处理图片路径，将相对路径转换为完整URL\n\t\t\t\t\trulesContent = processHtmlImages(rulesContent)\n\t\t\t\t\tthis.rulesContent = rulesContent\n\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('获取转换规则失败:', error)\n\t\t\t\t\t// 使用默认规则\n\t\t\t\t\tthis.rulesContent = `\n\t\t\t\t\t\t<div class=\"rule-item\">• 通过ERP系统自动验证订单真实性</div>\n\t\t\t\t\t\t<div class=\"rule-item\">• 系统自动获取订单金额并计算积分</div>\n\t\t\t\t\t\t<div class=\"rule-item\">• 每消费1元可获得1积分</div>\n\t\t\t\t\t\t<div class=\"rule-item\">• 验证通过后积分立即到账</div>\n\t\t\t\t\t\t<div class=\"rule-item\">• 每个订单号只能转换一次</div>\n\t\t\t\t\t`\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 平台选择\n\t\t\tonPlatformChange(e) {\n\t\t\t\tthis.platformIndex = e.detail.value\n\t\t\t},\n\n\n\n\t\t\t// 提交订单\n\t\t\tasync submitOrder() {\n\t\t\t\tif (!this.canSubmit) return\n\n\t\t\t\t// 检查登录状态\n\t\t\t\tif (!AuthUtils.isLoggedIn()) {\n\t\t\t\t\tAuthUtils.showLoginTip('提交订单需要登录', '请先登录后再提交订单')\n\t\t\t\t\treturn\n\t\t\t\t}\n\n\t\t\t\tthis.submitting = true\n\n\t\t\t\ttry {\n\t\t\t\t\tuni.showLoading({ title: '验证中...' })\n\n\t\t\t\t\t// 提交订单数据到ERP验证\n\t\t\t\t\tconst orderData = {\n\t\t\t\t\t\tplatform: this.platforms[this.platformIndex],\n\t\t\t\t\t\torder_number: this.orderNumber.trim()\n\t\t\t\t\t}\n\n\t\t\t\t\tconst result = await orderAPI.submitPublicOrder(orderData)\n\n\t\t\t\t\tuni.hideLoading()\n\n\t\t\t\t\tif (result.code === 200) {\n\t\t\t\t\t\tconst earnedPoints = result.data.points || 0\n\t\t\t\t\t\tconst orderAmount = result.data.amount || 0\n\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\ttitle: '申请提交成功',\n\t\t\t\t\t\t\tcontent: `订单验证成功！订单金额${orderAmount}元，预计获得${earnedPoints}积分。申请已提交，请等待管理员审核。`,\n\t\t\t\t\t\t\tshowCancel: false,\n\t\t\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\t\t\t// 跳转到积分页面\n\t\t\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\t\t\turl: '/pages/points/index'\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t})\n\t\t\t\t\t} else {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: result.message || '提交失败',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\n\t\t\t\t} catch (error) {\n\t\t\t\t\tuni.hideLoading()\n\t\t\t\t\tconsole.error('提交订单失败:', error)\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '提交失败，请重试',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t} finally {\n\t\t\t\t\tthis.submitting = false\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style scoped>\n\t.submit-container {\n\t\tbackground: white;\n\t\tmin-height: 100vh;\n\t\tpadding: 20px 0 100px 0;\n\t\toverflow-x: hidden; /* 防止水平滚动 */\n\t\tbox-sizing: border-box;\n\t}\n\n\t.header {\n\t\ttext-align: center;\n\t\tmargin-bottom: 30px;\n\t\tpadding: 0 15px;\n\t}\n\n\t.title {\n\t\tdisplay: block;\n\t\tfont-size: 20px;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t\tmargin-bottom: 8px;\n\t}\n\n\t.subtitle {\n\t\tfont-size: 14px;\n\t\tcolor: #666;\n\t}\n\n\t.form-section {\n\t\tmargin-bottom: 30px;\n\t\tpadding: 0 15px;\n\t}\n\n\t.form-item {\n\t\tpadding: 15px 0;\n\t\tborder-bottom: 1px solid #f0f0f0;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t}\n\n\t.form-item:last-child {\n\t\tborder-bottom: none;\n\t}\n\n\t.label {\n\t\tfont-size: 16px;\n\t\tcolor: #333;\n\t\twidth: 100px;\n\t\tflex-shrink: 0;\n\t}\n\n\t.picker {\n\t\tflex: 1;\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tcolor: #333;\n\t}\n\n\t.arrow {\n\t\tcolor: #999;\n\t\tfont-size: 18px;\n\t}\n\n\t.input {\n\t\tflex: 1;\n\t\tfont-size: 16px;\n\t\tcolor: #333;\n\t}\n\n\n\n\t.points-preview {\n\t\tflex: 1;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t}\n\n\t.points {\n\t\tfont-size: 18px;\n\t\tfont-weight: bold;\n\t\tcolor: #ff6b35;\n\t}\n\n\t.points-unit {\n\t\tcolor: #666;\n\t\tmargin-left: 4px;\n\t}\n\n\t.upload-area {\n\t\tflex: 1;\n\t\tmargin-left: 10px;\n\t}\n\n\t.upload-placeholder {\n\t\tborder: 2px dashed #ddd;\n\t\tborder-radius: 8px;\n\t\tpadding: 30px;\n\t\ttext-align: center;\n\t\tbackground: #fafafa;\n\t}\n\n\t.upload-icon {\n\t\tdisplay: block;\n\t\tfont-size: 32px;\n\t\tmargin-bottom: 10px;\n\t}\n\n\t.upload-text {\n\t\tdisplay: block;\n\t\tfont-size: 16px;\n\t\tcolor: #333;\n\t\tmargin-bottom: 5px;\n\t}\n\n\t.upload-tip {\n\t\tfont-size: 12px;\n\t\tcolor: #999;\n\t}\n\n\t.image-list {\n\t\tdisplay: flex;\n\t\tflex-wrap: wrap;\n\t\tgap: 10px;\n\t}\n\n\t.image-item {\n\t\tposition: relative;\n\t\twidth: 80px;\n\t\theight: 80px;\n\t}\n\n\t.preview-image {\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tborder-radius: 6px;\n\t}\n\n\t.delete-btn {\n\t\tposition: absolute;\n\t\ttop: -8px;\n\t\tright: -8px;\n\t\twidth: 20px;\n\t\theight: 20px;\n\t\tbackground: #ff4757;\n\t\tcolor: white;\n\t\tborder-radius: 50%;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tfont-size: 14px;\n\t\tline-height: 1;\n\t}\n\n\t.add-image {\n\t\twidth: 80px;\n\t\theight: 80px;\n\t\tborder: 2px dashed #ddd;\n\t\tborder-radius: 6px;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tbackground: #fafafa;\n\t}\n\n\t.add-icon {\n\t\tfont-size: 24px;\n\t\tcolor: #999;\n\t}\n\n\n\n\t.rules-section {\n\t\tmargin-bottom: 30px;\n\t}\n\n\t.rules-title {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-bottom: 15px;\n\t\tpadding: 0 15px;\n\t}\n\n\t.rules-icon {\n\t\tfont-size: 18px;\n\t\tmargin-right: 8px;\n\t}\n\n\t.rules-text {\n\t\tfont-size: 16px;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t}\n\n\t.rules-content {\n\t\tline-height: 1.6;\n\t\twidth: 100%;\n\t\tbox-sizing: border-box;\n\t}\n\n\t.submit-section {\n\t\tposition: fixed;\n\t\tbottom: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbackground: white;\n\t\tpadding: 15px 15px;\n\t\tborder-top: 1px solid #f0f0f0;\n\t}\n\n\t.submit-btn {\n\t\twidth: 100%;\n\t\theight: 50px;\n\t\tbackground: #ff6b35;\n\t\tcolor: white;\n\t\tborder: none;\n\t\tborder-radius: 25px;\n\t\tfont-size: 18px;\n\t\tfont-weight: bold;\n\t}\n\n\t.submit-btn.disabled {\n\t\tbackground: #ccc;\n\t\tcolor: #999;\n\t}\n</style>\n", "import MiniProgramPage from 'D:/app/miniprogram/SIYU/pages/order/submit.vue'\nwx.createPage(MiniProgramPage)"], "names": ["systemAPI", "processHtmlImages", "uni", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "orderAPI"], "mappings": ";;;;;AA6DC,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,WAAW,CAAC,MAAM,MAAM,MAAM,OAAO,MAAM,MAAM,IAAI;AAAA,MACrD,eAAe;AAAA,MACf,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,IACf;AAAA,EACA;AAAA,EACD,SAAS;AACR,SAAK,UAAU;AAAA,EACf;AAAA,EACD,UAAU;AAAA,IACT,YAAY;AACX,aAAO,KAAK,iBAAiB,KACzB,KAAK,YAAY,KAAK,KACtB,CAAC,KAAK;AAAA,IACX;AAAA,EACA;AAAA,EACD,SAAS;AAAA;AAAA,IAER,MAAM,YAAY;AACjB,UAAI;AACH,cAAM,SAAS,MAAMA,UAAS,UAAC,UAAU;AACzC,cAAM,SAAS,OAAO,QAAQ,CAAC;AAG/B,YAAI,eAAe,OAAO,wBAAwB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASlD,uBAAeC,aAAiB,kBAAC,YAAY;AAC7C,aAAK,eAAe;AAAA,MAEnB,SAAO,OAAO;AACfC,sBAAAA,sDAAc,aAAa,KAAK;AAEhC,aAAK,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOrB;AAAA,IACA;AAAA;AAAA,IAGD,iBAAiB,GAAG;AACnB,WAAK,gBAAgB,EAAE,OAAO;AAAA,IAC9B;AAAA;AAAA,IAKD,MAAM,cAAc;AACnB,UAAI,CAAC,KAAK;AAAW;AAGrB,UAAI,CAACC,WAAAA,UAAU,cAAc;AAC5BA,6BAAU,aAAa,YAAY,YAAY;AAC/C;AAAA,MACD;AAEA,WAAK,aAAa;AAElB,UAAI;AACHD,sBAAAA,MAAI,YAAY,EAAE,OAAO,UAAU;AAGnC,cAAM,YAAY;AAAA,UACjB,UAAU,KAAK,UAAU,KAAK,aAAa;AAAA,UAC3C,cAAc,KAAK,YAAY,KAAK;AAAA,QACrC;AAEA,cAAM,SAAS,MAAME,mBAAS,kBAAkB,SAAS;AAEzDF,sBAAAA,MAAI,YAAY;AAEhB,YAAI,OAAO,SAAS,KAAK;AACxB,gBAAM,eAAe,OAAO,KAAK,UAAU;AAC3C,gBAAM,cAAc,OAAO,KAAK,UAAU;AAC1CA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,SAAS,cAAc,WAAW,SAAS,YAAY;AAAA,YACvD,YAAY;AAAA,YACZ,SAAS,MAAM;AAEdA,4BAAAA,MAAI,WAAW;AAAA,gBACd,KAAK;AAAA,eACL;AAAA,YACF;AAAA,WACA;AAAA,eACK;AACNA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO,OAAO,WAAW;AAAA,YACzB,MAAM;AAAA,WACN;AAAA,QACF;AAAA,MAEC,SAAO,OAAO;AACfA,sBAAAA,MAAI,YAAY;AAChBA,sBAAAA,sDAAc,WAAW,KAAK;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,SACN;AAAA,MACF,UAAU;AACT,aAAK,aAAa;AAAA,MACnB;AAAA,IACD;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;AClLD,GAAG,WAAW,eAAe;"}