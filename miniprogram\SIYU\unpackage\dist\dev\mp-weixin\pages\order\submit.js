"use strict";
const common_vendor = require("../../common/vendor.js");
const api_index = require("../../api/index.js");
const utils_auth = require("../../utils/auth.js");
const utils_common = require("../../utils/common.js");
const _sfc_main = {
  data() {
    return {
      platforms: ["淘宝", "天猫", "京东", "拼多多", "抖音", "快手", "其他"],
      platformIndex: 0,
      orderNumber: "",
      submitting: false,
      rulesContent: ""
    };
  },
  onLoad() {
    this.loadRules();
  },
  computed: {
    canSubmit() {
      return this.platformIndex >= 0 && this.orderNumber.trim() && !this.submitting;
    }
  },
  methods: {
    // 加载转换规则
    async loadRules() {
      try {
        const result = await api_index.systemAPI.getConfig();
        const config = result.data || {};
        let rulesContent = config.order_exchange_rules || `
						<div class="rule-item">• 通过ERP系统自动验证订单真实性</div>
						<div class="rule-item">• 系统自动获取订单金额并计算积分</div>
						<div class="rule-item">• 每消费1元可获得1积分</div>
						<div class="rule-item">• 验证通过后积分立即到账</div>
						<div class="rule-item">• 每个订单号只能转换一次</div>
					`;
        rulesContent = utils_common.processHtmlImages(rulesContent);
        this.rulesContent = rulesContent;
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/order/submit.vue:103", "获取转换规则失败:", error);
        this.rulesContent = `
						<div class="rule-item">• 通过ERP系统自动验证订单真实性</div>
						<div class="rule-item">• 系统自动获取订单金额并计算积分</div>
						<div class="rule-item">• 每消费1元可获得1积分</div>
						<div class="rule-item">• 验证通过后积分立即到账</div>
						<div class="rule-item">• 每个订单号只能转换一次</div>
					`;
      }
    },
    // 平台选择
    onPlatformChange(e) {
      this.platformIndex = e.detail.value;
    },
    // 提交订单
    async submitOrder() {
      if (!this.canSubmit)
        return;
      if (!utils_auth.AuthUtils.isLoggedIn()) {
        utils_auth.AuthUtils.showLoginTip("提交订单需要登录", "请先登录后再提交订单");
        return;
      }
      this.submitting = true;
      try {
        common_vendor.index.showLoading({ title: "验证中..." });
        const orderData = {
          platform: this.platforms[this.platformIndex],
          order_number: this.orderNumber.trim()
        };
        const result = await api_index.orderAPI.submitPublicOrder(orderData);
        common_vendor.index.hideLoading();
        if (result.code === 200) {
          const earnedPoints = result.data.points || 0;
          const orderAmount = result.data.amount || 0;
          common_vendor.index.showModal({
            title: "申请提交成功",
            content: `订单验证成功！订单金额${orderAmount}元，预计获得${earnedPoints}积分。申请已提交，请等待管理员审核。`,
            showCancel: false,
            success: () => {
              common_vendor.index.navigateTo({
                url: "/pages/points/index"
              });
            }
          });
        } else {
          common_vendor.index.showToast({
            title: result.message || "提交失败",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages/order/submit.vue:170", "提交订单失败:", error);
        common_vendor.index.showToast({
          title: "提交失败，请重试",
          icon: "none"
        });
      } finally {
        this.submitting = false;
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.t($data.platforms[$data.platformIndex] || "请选择平台"),
    b: common_vendor.o((...args) => $options.onPlatformChange && $options.onPlatformChange(...args)),
    c: $data.platformIndex,
    d: $data.platforms,
    e: $data.orderNumber,
    f: common_vendor.o(($event) => $data.orderNumber = $event.detail.value),
    g: $data.rulesContent,
    h: common_vendor.t($data.submitting ? "验证中..." : "立即转换"),
    i: !$options.canSubmit ? 1 : "",
    j: !$options.canSubmit,
    k: common_vendor.o((...args) => $options.submitOrder && $options.submitOrder(...args))
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-c3fa8b8a"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/order/submit.js.map
