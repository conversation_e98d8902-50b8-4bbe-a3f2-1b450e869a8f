<?php

declare(strict_types=1);

require_once __DIR__ . '/../../core/Database.php';
require_once __DIR__ . '/../../core/Response.php';
require_once __DIR__ . '/../../middleware/AuthMiddleware.php';

class AddressController
{
    private Database $db;

    public function __construct()
    {
        $this->db = Database::getInstance();
    }

    /**
     * 获取用户地址列表
     */
    public function index(): void
    {
        try {
            // 验证认证
            $authMiddleware = new AuthMiddleware();
            $authMiddleware->handle();

            $userId = AuthMiddleware::getCurrentUserId();
            if (!$userId) {
                Response::unauthorized('请先登录');
                return;
            }

            $sql = "SELECT * FROM user_addresses WHERE user_id = ? ORDER BY is_default DESC, created_at DESC";
            $stmt = $this->db->query($sql, [$userId]);
            $addresses = $stmt->fetchAll();

            // 格式化数据
            foreach ($addresses as &$address) {
                $address['id'] = (int)$address['id'];
                $address['user_id'] = (int)$address['user_id'];
                $address['is_default'] = (bool)$address['is_default'];
                $address['full_address'] = $address['province'] . $address['city'] . $address['district'] . $address['detail'];
            }

            Response::success($addresses, '获取地址列表成功');

        } catch (Exception $e) {
            error_log("获取地址列表失败 - 用户ID: " . ($userId ?? 'unknown') . ", 错误: " . $e->getMessage());
            Response::businessError('获取地址列表失败，请稍后重试');
        }
    }

    /**
     * 创建地址
     */
    public function create(): void
    {
        try {
            // 验证认证
            $authMiddleware = new AuthMiddleware();
            $authMiddleware->handle();

            $userId = AuthMiddleware::getCurrentUserId();
            if (!$userId) {
                Response::unauthorized('请先登录');
                return;
            }

            $input = json_decode(file_get_contents('php://input'), true);

            // 验证必需参数
            $required = ['name', 'phone', 'province', 'city', 'district', 'detail'];
            foreach ($required as $field) {
                if (empty($input[$field])) {
                    Response::validationError([$field => '此字段不能为空']);
                    return;
                }
            }

            // 验证手机号格式
            if (!preg_match('/^1[3-9]\d{9}$/', $input['phone'])) {
                Response::validationError(['phone' => '手机号格式不正确']);
                return;
            }

            $this->db->beginTransaction();

            // 检查用户是否已有地址
            $countSql = "SELECT COUNT(*) as count FROM user_addresses WHERE user_id = ?";
            $countStmt = $this->db->query($countSql, [$userId]);
            $countResult = $countStmt->fetchAll();
            $addressCount = (int)($countResult[0]['count'] ?? 0);

            // 如果是第一个地址，自动设为默认
            $isDefault = !empty($input['is_default']) || $addressCount === 0;

            // 如果设置为默认地址，先取消其他默认地址
            if ($isDefault) {
                $this->db->query("UPDATE user_addresses SET is_default = 0 WHERE user_id = ?", [$userId]);
            }

            // 创建地址
            $addressData = [
                'user_id' => $userId,
                'name' => trim($input['name']),
                'phone' => trim($input['phone']),
                'province' => trim($input['province']),
                'city' => trim($input['city']),
                'district' => trim($input['district']),
                'detail' => trim($input['detail']),
                'is_default' => $isDefault ? 1 : 0,
                'created_at' => date('Y-m-d H:i:s')
            ];

            $addressId = $this->db->insert('user_addresses', $addressData);

            $this->db->commit();

            Response::success([
                'address_id' => $addressId
            ], '地址创建成功');

        } catch (Exception $e) {
            $this->db->rollback();
            error_log("创建地址失败 - 用户ID: " . ($userId ?? 'unknown') . ", 错误: " . $e->getMessage());
            Response::businessError('创建地址失败，请稍后重试');
        }
    }

    /**
     * 获取地址详情
     */
    public function show(int $id): void
    {
        try {
            // 验证认证
            $authMiddleware = new AuthMiddleware();
            $authMiddleware->handle();

            $userId = AuthMiddleware::getCurrentUserId();
            if (!$userId) {
                Response::unauthorized('请先登录');
                return;
            }

            $sql = "SELECT * FROM user_addresses WHERE id = ? AND user_id = ?";
            $stmt = $this->db->query($sql, [$id, $userId]);
            $addresses = $stmt->fetchAll();

            if (empty($addresses)) {
                Response::notFound('地址不存在');
                return;
            }

            $address = $addresses[0];
            $address['id'] = (int)$address['id'];
            $address['user_id'] = (int)$address['user_id'];
            $address['is_default'] = (bool)$address['is_default'];
            $address['full_address'] = $address['province'] . $address['city'] . $address['district'] . $address['detail'];

            Response::success($address, '获取地址详情成功');

        } catch (Exception $e) {
            error_log("获取地址详情失败 - 用户ID: " . ($userId ?? 'unknown') . ", 地址ID: " . ($id ?? 'unknown') . ", 错误: " . $e->getMessage());
            Response::businessError('获取地址详情失败，请稍后重试');
        }
    }

    /**
     * 获取默认地址
     */
    public function getDefault(): void
    {
        try {
            // 验证认证
            $authMiddleware = new AuthMiddleware();
            $authMiddleware->handle();

            $userId = AuthMiddleware::getCurrentUserId();
            if (!$userId) {
                Response::unauthorized('请先登录');
                return;
            }

            $sql = "SELECT * FROM user_addresses WHERE user_id = ? AND is_default = 1 LIMIT 1";
            $stmt = $this->db->query($sql, [$userId]);
            $addresses = $stmt->fetchAll();

            if (empty($addresses)) {
                Response::success(null, '暂无默认地址');
                return;
            }

            $address = $addresses[0];
            $address['id'] = (int)$address['id'];
            $address['user_id'] = (int)$address['user_id'];
            $address['is_default'] = (bool)$address['is_default'];
            $address['full_address'] = $address['province'] . $address['city'] . $address['district'] . $address['detail'];

            Response::success($address, '获取默认地址成功');

        } catch (Exception $e) {
            error_log("获取默认地址失败 - 用户ID: " . ($userId ?? 'unknown') . ", 错误: " . $e->getMessage());
            Response::businessError('获取默认地址失败，请稍后重试');
        }
    }

    /**
     * 更新地址
     */
    public function update(int $id): void
    {
        try {
            // 验证认证
            $authMiddleware = new AuthMiddleware();
            $authMiddleware->handle();

            $userId = AuthMiddleware::getCurrentUserId();
            if (!$userId) {
                Response::unauthorized('请先登录');
                return;
            }

            // 检查地址是否存在且属于当前用户
            $sql = "SELECT id FROM user_addresses WHERE id = ? AND user_id = ?";
            $stmt = $this->db->query($sql, [$id, $userId]);
            $addresses = $stmt->fetchAll();

            if (empty($addresses)) {
                Response::notFound('地址不存在');
                return;
            }

            $input = json_decode(file_get_contents('php://input'), true);

            // 验证必需参数
            $required = ['name', 'phone', 'province', 'city', 'district', 'detail'];
            foreach ($required as $field) {
                if (empty($input[$field])) {
                    Response::validationError([$field => '此字段不能为空']);
                    return;
                }
            }

            // 验证手机号格式
            if (!preg_match('/^1[3-9]\d{9}$/', $input['phone'])) {
                Response::validationError(['phone' => '手机号格式不正确']);
                return;
            }

            $this->db->beginTransaction();

            // 如果设置为默认地址，先取消其他默认地址
            $isDefault = !empty($input['is_default']);
            if ($isDefault) {
                $this->db->query("UPDATE user_addresses SET is_default = 0 WHERE user_id = ?", [$userId]);
            }

            // 更新地址
            $addressData = [
                'name' => trim($input['name']),
                'phone' => trim($input['phone']),
                'province' => trim($input['province']),
                'city' => trim($input['city']),
                'district' => trim($input['district']),
                'detail' => trim($input['detail']),
                'is_default' => $isDefault ? 1 : 0,
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $this->db->update('user_addresses', $addressData, 'id = ? AND user_id = ?', [$id, $userId]);

            $this->db->commit();

            Response::success(null, '地址更新成功');

        } catch (Exception $e) {
            $this->db->rollback();
            error_log("更新地址失败 - 用户ID: " . ($userId ?? 'unknown') . ", 地址ID: " . ($id ?? 'unknown') . ", 错误: " . $e->getMessage());
            Response::businessError('更新地址失败，请稍后重试');
        }
    }

    /**
     * 删除地址
     */
    public function delete(int $id): void
    {
        try {
            // 验证认证
            $authMiddleware = new AuthMiddleware();
            $authMiddleware->handle();

            $userId = AuthMiddleware::getCurrentUserId();
            if (!$userId) {
                Response::unauthorized('请先登录');
                return;
            }

            // 检查地址是否存在且属于当前用户
            $sql = "SELECT id, is_default FROM user_addresses WHERE id = ? AND user_id = ?";
            $stmt = $this->db->query($sql, [$id, $userId]);
            $addresses = $stmt->fetchAll();

            if (empty($addresses)) {
                Response::notFound('地址不存在');
                return;
            }

            $address = $addresses[0];
            $isDefault = (bool)$address['is_default'];

            $this->db->beginTransaction();

            // 删除地址
            $this->db->query("DELETE FROM user_addresses WHERE id = ? AND user_id = ?", [$id, $userId]);

            // 如果删除的是默认地址，设置第一个地址为默认
            if ($isDefault) {
                $this->db->query(
                    "UPDATE user_addresses SET is_default = 1 WHERE user_id = ? ORDER BY created_at ASC LIMIT 1",
                    [$userId]
                );
            }

            $this->db->commit();

            Response::success(null, '地址删除成功');

        } catch (Exception $e) {
            $this->db->rollback();
            error_log("删除地址失败 - 用户ID: " . ($userId ?? 'unknown') . ", 地址ID: " . ($id ?? 'unknown') . ", 错误: " . $e->getMessage());
            Response::businessError('删除地址失败，请稍后重试');
        }
    }

    /**
     * 设置默认地址
     */
    public function setDefault(int $id): void
    {
        try {
            // 验证认证
            $authMiddleware = new AuthMiddleware();
            $authMiddleware->handle();

            $userId = AuthMiddleware::getCurrentUserId();
            if (!$userId) {
                Response::unauthorized('请先登录');
                return;
            }

            // 检查地址是否存在且属于当前用户
            $sql = "SELECT id FROM user_addresses WHERE id = ? AND user_id = ?";
            $stmt = $this->db->query($sql, [$id, $userId]);
            $addresses = $stmt->fetchAll();

            if (empty($addresses)) {
                Response::notFound('地址不存在');
                return;
            }

            $this->db->beginTransaction();

            // 取消其他默认地址
            $this->db->query("UPDATE user_addresses SET is_default = 0 WHERE user_id = ?", [$userId]);

            // 设置当前地址为默认
            $this->db->query("UPDATE user_addresses SET is_default = 1 WHERE id = ? AND user_id = ?", [$id, $userId]);

            $this->db->commit();

            Response::success(null, '默认地址设置成功');

        } catch (Exception $e) {
            $this->db->rollback();
            error_log("设置默认地址失败 - 用户ID: " . ($userId ?? 'unknown') . ", 地址ID: " . ($id ?? 'unknown') . ", 错误: " . $e->getMessage());
            Response::businessError('设置默认地址失败，请稍后重试');
        }
    }
}
