
.earn-container.data-v-281ae9f1 {
		background: #f8f8f8;
		min-height: 100vh;
		padding-bottom: env(safe-area-inset-bottom);
}
.header.data-v-281ae9f1 {
		position: relative;
		height: 200px;
		overflow: hidden;
}
.header-bg.data-v-281ae9f1 {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.header-content.data-v-281ae9f1 {
		position: relative;
		z-index: 2;
		padding: 60px 20px 20px;
		color: white;
}
.title.data-v-281ae9f1 {
		font-size: 28px;
		font-weight: bold;
		display: block;
		margin-bottom: 8px;
}
.subtitle.data-v-281ae9f1 {
		font-size: 16px;
		opacity: 0.9;
		display: block;
		margin-bottom: 20px;
}
.points-info.data-v-281ae9f1 {
		background: rgba(255, 255, 255, 0.2);
		padding: 12px 16px;
		border-radius: 20px;
		display: inline-block;
}
.current-points.data-v-281ae9f1 {
		font-size: 16px;
		font-weight: bold;
}
.main-feature.data-v-281ae9f1 {
		margin: -30px 20px 20px;
		position: relative;
		z-index: 3;
}
.feature-card.data-v-281ae9f1 {
		background: white;
		border-radius: 16px;
		padding: 20px;
		display: flex;
		align-items: center;
		box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}
.feature-card.highlight.data-v-281ae9f1 {
		background: linear-gradient(135deg, #ff6b35 0%, #ff8c42 100%);
		color: white;
}
.feature-icon.data-v-281ae9f1 {
		width: 60px;
		height: 60px;
		border-radius: 30px;
		background: rgba(255, 255, 255, 0.2);
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 16px;
}
.icon-emoji.data-v-281ae9f1 {
		font-size: 28px;
}
.feature-content.data-v-281ae9f1 {
		flex: 1;
}
.feature-title.data-v-281ae9f1 {
		font-size: 18px;
		font-weight: bold;
		display: block;
		margin-bottom: 4px;
}
.feature-desc.data-v-281ae9f1 {
		font-size: 14px;
		opacity: 0.8;
		display: block;
		margin-bottom: 4px;
}
.feature-rate.data-v-281ae9f1 {
		font-size: 16px;
		font-weight: bold;
}
.feature-arrow.data-v-281ae9f1 {
		display: flex;
		flex-direction: column;
		align-items: center;
}
.arrow.data-v-281ae9f1 {
		font-size: 24px;
		opacity: 0.8;
}
.hot-badge.data-v-281ae9f1 {
		background: #ff4757;
		color: white;
		font-size: 10px;
		padding: 2px 6px;
		border-radius: 8px;
		margin-top: 4px;
}
.activities-section.data-v-281ae9f1 {
		padding: 20px;
}
.section-title.data-v-281ae9f1 {
		margin-bottom: 20px;
}
.title-text.data-v-281ae9f1 {
		font-size: 20px;
		font-weight: bold;
		color: #333;
		display: block;
		margin-bottom: 4px;
}
.title-desc.data-v-281ae9f1 {
		font-size: 14px;
		color: #666;
}
.activities-grid.data-v-281ae9f1 {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		gap: 15px;
}
.activity-card.data-v-281ae9f1 {
		background: white;
		border-radius: 12px;
		padding: 20px 16px;
		text-align: center;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
.activity-icon.data-v-281ae9f1 {
		width: 50px;
		height: 50px;
		border-radius: 25px;
		background: #f0f0f0;
		display: flex;
		align-items: center;
		justify-content: center;
		margin: 0 auto 12px;
}
.activity-title.data-v-281ae9f1 {
		font-size: 16px;
		font-weight: bold;
		color: #333;
		display: block;
		margin-bottom: 4px;
}
.activity-reward.data-v-281ae9f1 {
		font-size: 14px;
		color: #ff6b35;
		font-weight: bold;
		display: block;
		margin-bottom: 4px;
}
.activity-status.data-v-281ae9f1 {
		font-size: 12px;
		color: #999;
}
.usage-section.data-v-281ae9f1 {
		padding: 0 20px 40px;
}
.usage-card.data-v-281ae9f1 {
		background: white;
		border-radius: 12px;
		padding: 16px 20px;
		display: flex;
		align-items: center;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
.usage-icon.data-v-281ae9f1 {
		font-size: 24px;
		margin-right: 12px;
}
.usage-content.data-v-281ae9f1 {
		flex: 1;
}
.usage-title.data-v-281ae9f1 {
		font-size: 16px;
		font-weight: bold;
		color: #333;
		display: block;
		margin-bottom: 2px;
}
.usage-desc.data-v-281ae9f1 {
		font-size: 14px;
		color: #666;
}
.usage-arrow.data-v-281ae9f1 {
		font-size: 18px;
		color: #999;
}
