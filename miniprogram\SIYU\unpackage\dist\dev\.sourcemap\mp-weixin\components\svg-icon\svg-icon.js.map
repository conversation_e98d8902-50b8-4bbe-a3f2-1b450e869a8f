{"version": 3, "file": "svg-icon.js", "sources": ["components/svg-icon/svg-icon.vue", "../../../Program Files/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovYXBwL21pbmlwcm9ncmFtL1NJWVUvY29tcG9uZW50cy9zdmctaWNvbi9zdmctaWNvbi52dWU"], "sourcesContent": ["<template>\n\t<view class=\"svg-icon\" :style=\"iconStyle\">\n\t\t<!-- 收藏图标 -->\n\t\t<view v-if=\"name === 'heart'\" class=\"icon-heart\" :style=\"{ color: color, fontSize: size + 'px' }\">♡</view>\n\n\t\t<!-- 收藏填充图标 -->\n\t\t<view v-else-if=\"name === 'heart-filled'\" class=\"icon-heart-filled\" :style=\"{ color: color, fontSize: size + 'px' }\">♥</view>\n\n\t\t<!-- 分享图标 -->\n\t\t<view v-else-if=\"name === 'share'\" class=\"icon-share\" :style=\"{ color: color, fontSize: size + 'px' }\">⤴</view>\n\n\t\t<!-- 签到图标 -->\n\t\t<view v-else-if=\"name === 'calendar'\" class=\"icon-calendar\" :style=\"{ color: color, fontSize: size + 'px' }\">📅</view>\n\n\t\t<!-- 转盘图标 -->\n\t\t<view v-else-if=\"name === 'wheel'\" class=\"icon-wheel\" :style=\"{ color: color, fontSize: size + 'px' }\">🎰</view>\n\n\t\t<!-- 任务图标 -->\n\t\t<view v-else-if=\"name === 'tasks'\" class=\"icon-tasks\" :style=\"{ color: color, fontSize: size + 'px' }\">📋</view>\n\n\t\t<!-- 邀请图标 -->\n\t\t<view v-else-if=\"name === 'invite'\" class=\"icon-invite\" :style=\"{ color: color, fontSize: size + 'px' }\">👥</view>\n\n\t\t<!-- 待付款图标 -->\n\t\t<view v-else-if=\"name === 'pending'\" class=\"icon-pending\" :style=\"{ color: color, fontSize: size + 'px' }\">⏰</view>\n\n\t\t<!-- 处理中图标 -->\n\t\t<view v-else-if=\"name === 'processing'\" class=\"icon-processing\" :style=\"{ color: color, fontSize: size + 'px' }\">⭐</view>\n\n\t\t<!-- 已发货图标 -->\n\t\t<view v-else-if=\"name === 'shipped'\" class=\"icon-shipped\" :style=\"{ color: color, fontSize: size + 'px' }\">🚚</view>\n\n\t\t<!-- 已完成图标 -->\n\t\t<view v-else-if=\"name === 'completed'\" class=\"icon-completed\" :style=\"{ color: color, fontSize: size + 'px' }\">✅</view>\n\n\t\t<!-- 首页图标 -->\n\t\t<view v-else-if=\"name === 'home'\" class=\"icon-home\" :style=\"{ color: color, fontSize: size + 'px' }\">🏠</view>\n\n\t\t<!-- 商城图标 -->\n\t\t<view v-else-if=\"name === 'shop'\" class=\"icon-shop\" :style=\"{ color: color, fontSize: size + 'px' }\">🛒</view>\n\n\t\t<!-- 积分图标 -->\n\t\t<view v-else-if=\"name === 'points'\" class=\"icon-points\" :style=\"{ color: color, fontSize: size + 'px' }\">💰</view>\n\n\t\t<!-- 用户图标 -->\n\t\t<view v-else-if=\"name === 'user'\" class=\"icon-user\" :style=\"{ color: color, fontSize: size + 'px' }\">👤</view>\n\n\t\t<!-- 默认图标 -->\n\t\t<view v-else class=\"icon-default\" :style=\"{ color: color, fontSize: size + 'px' }\">●</view>\n\t</view>\n</template>\n\n<script>\nexport default {\n\tname: 'SvgIcon',\n\tprops: {\n\t\tname: {\n\t\t\ttype: String,\n\t\t\tdefault: 'default'\n\t\t},\n\t\tsize: {\n\t\t\ttype: [String, Number],\n\t\t\tdefault: 24\n\t\t},\n\t\tcolor: {\n\t\t\ttype: String,\n\t\t\tdefault: '#333'\n\t\t}\n\t},\n\tcomputed: {\n\t\ticonStyle() {\n\t\t\treturn {\n\t\t\t\twidth: this.size + 'px',\n\t\t\t\theight: this.size + 'px',\n\t\t\t\tdisplay: 'inline-flex',\n\t\t\t\talignItems: 'center',\n\t\t\t\tjustifyContent: 'center'\n\t\t\t}\n\t\t}\n\t}\n}\n</script>\n\n<style scoped>\n.svg-icon {\n\tdisplay: inline-flex;\n\talign-items: center;\n\tjustify-content: center;\n\tvertical-align: middle;\n}\n\n.svg-icon view {\n\tline-height: 1;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n</style>\n", "import Component from 'D:/app/miniprogram/SIYU/components/svg-icon/svg-icon.vue'\nwx.createComponent(Component)"], "names": [], "mappings": ";;AAqDA,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,OAAO;AAAA,IACN,MAAM;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,IACD,MAAM;AAAA,MACL,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS;AAAA,IACT;AAAA,IACD,OAAO;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA,EACA;AAAA,EACD,UAAU;AAAA,IACT,YAAY;AACX,aAAO;AAAA,QACN,OAAO,KAAK,OAAO;AAAA,QACnB,QAAQ,KAAK,OAAO;AAAA,QACpB,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,gBAAgB;AAAA,MACjB;AAAA,IACD;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/EA,GAAG,gBAAgB,SAAS;"}