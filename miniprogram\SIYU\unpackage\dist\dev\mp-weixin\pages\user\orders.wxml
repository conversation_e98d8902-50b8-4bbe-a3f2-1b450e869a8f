<view class="orders-container data-v-efd435e4"><view class="status-tabs data-v-efd435e4"><view wx:for="{{a}}" wx:for-item="item" wx:key="c" class="{{['tab-item', 'data-v-efd435e4', item.b && 'active']}}" bindtap="{{item.d}}"><text class="tab-text data-v-efd435e4">{{item.a}}</text></view></view><view class="orders-list data-v-efd435e4"><view wx:for="{{b}}" wx:for-item="order" wx:key="m" class="order-item data-v-efd435e4" bindtap="{{order.n}}"><view class="order-header data-v-efd435e4"><text class="order-number data-v-efd435e4">订单号：{{order.a}}</text><text class="{{['order-status', 'data-v-efd435e4', order.c]}}">{{order.b}}</text></view><view class="order-products data-v-efd435e4"><view wx:for="{{order.d}}" wx:for-item="product" wx:key="f" class="product-item data-v-efd435e4"><view class="product-image data-v-efd435e4"><text class="product-emoji data-v-efd435e4">{{product.a}}</text></view><view class="product-info data-v-efd435e4"><text class="product-name data-v-efd435e4">{{product.b}}</text><text class="product-spec data-v-efd435e4">{{product.c}}</text><text class="product-quantity data-v-efd435e4">x{{product.d}}</text></view><view class="product-price data-v-efd435e4"><text class="points-price data-v-efd435e4">{{product.e}}积分</text></view></view></view><view class="order-footer data-v-efd435e4"><view class="order-total data-v-efd435e4"><text class="total-text data-v-efd435e4">共{{order.e}}件商品，合计：{{order.f}}积分</text></view><view class="order-actions data-v-efd435e4"><button wx:if="{{order.g}}" class="action-btn cancel-btn data-v-efd435e4" catchtap="{{order.h}}">取消订单</button><button wx:if="{{order.i}}" class="action-btn confirm-btn data-v-efd435e4" catchtap="{{order.j}}">确认收货</button><button wx:if="{{order.k}}" class="action-btn review-btn data-v-efd435e4" catchtap="{{order.l}}">评价</button></view></view></view><view wx:if="{{c}}" class="empty-state data-v-efd435e4"><text class="empty-emoji data-v-efd435e4">📦</text><text class="empty-text data-v-efd435e4">暂无订单</text></view></view></view>