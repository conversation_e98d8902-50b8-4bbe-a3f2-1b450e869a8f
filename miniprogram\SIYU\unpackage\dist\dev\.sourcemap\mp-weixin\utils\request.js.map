{"version": 3, "file": "request.js", "sources": ["utils/request.js"], "sourcesContent": ["// 网络请求封装\nconst BASE_URL = 'https://siyu.vezii.com/api'\nconst STATIC_URL = 'https://siyu.vezii.com'\n\n// 处理图片URL\nexport const getImageUrl = (imagePath) => {\n\tif (!imagePath) return ''\n\n\t// 如果已经是完整URL，直接返回\n\tif (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {\n\t\treturn imagePath\n\t}\n\n\t// 如果是相对路径，拼接静态资源域名\n\tif (imagePath.startsWith('/')) {\n\t\treturn STATIC_URL + imagePath\n\t}\n\n\t// 其他情况，添加前缀\n\treturn STATIC_URL + '/' + imagePath\n}\n\n// 请求拦截器\nconst requestInterceptor = (options) => {\n\t// 添加token\n\tconst token = uni.getStorageSync('token')\n\tif (token) {\n\t\toptions.header = {\n\t\t\t...options.header,\n\t\t\t'Authorization': `Bearer ${token}`\n\t\t}\n\t}\n\n\t// 添加通用header\n\toptions.header = {\n\t\t'Content-Type': 'application/json',\n\t\t...options.header\n\t}\n\n\t// 显示加载提示\n\tif (options.loading !== false) {\n\t\tuni.showLoading({\n\t\t\ttitle: '加载中...',\n\t\t\tmask: true\n\t\t})\n\t}\n\n\treturn options\n}\n\n// 响应拦截器\nconst responseInterceptor = (response, options) => {\n\t// 隐藏加载提示\n\tif (options.loading !== false) {\n\t\tuni.hideLoading()\n\t}\n\n\tconst { statusCode, data } = response\n\n\t// HTTP状态码检查\n\tif (statusCode !== 200) {\n\t\tuni.showToast({\n\t\t\ttitle: `网络错误 ${statusCode}`,\n\t\t\ticon: 'none'\n\t\t})\n\t\treturn Promise.reject(response)\n\t}\n\n\t// 业务状态码检查\n\tif (data.code !== 200) {\n\t\t// token过期，跳转登录\n\t\tif (data.code === 401) {\n\t\t\tuni.removeStorageSync('token')\n\t\t\tuni.removeStorageSync('userInfo')\n\t\t\tuni.navigateTo({\n\t\t\t\turl: '/pages/login/login'\n\t\t\t})\n\t\t\treturn Promise.reject(data)\n\t\t}\n\n\t\t// 其他业务错误\n\t\tif (options.showError !== false) {\n\t\t\tuni.showToast({\n\t\t\t\ttitle: data.message || '请求失败',\n\t\t\t\ticon: 'none'\n\t\t\t})\n\t\t}\n\t\treturn Promise.reject(data)\n\t}\n\n\t// 成功情况下也返回Promise\n\treturn Promise.resolve(data)\n}\n\n// 基础请求函数\nconst request = (options) => {\n\t// 处理URL\n\tif (!options.url.startsWith('http')) {\n\t\toptions.url = BASE_URL + options.url\n\t}\n\n\t// 请求拦截\n\toptions = requestInterceptor(options)\n\n\treturn new Promise((resolve, reject) => {\n\t\tuni.request({\n\t\t\t...options,\n\t\t\tsuccess: (response) => {\n\t\t\t\tresponseInterceptor(response, options)\n\t\t\t\t\t.then(resolve)\n\t\t\t\t\t.catch(reject)\n\t\t\t},\n\t\t\tfail: (error) => {\n\t\t\t\t// 隐藏加载提示\n\t\t\t\tif (options.loading !== false) {\n\t\t\t\t\tuni.hideLoading()\n\t\t\t\t}\n\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '网络连接失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t})\n\t\t\t\treject(error)\n\t\t\t}\n\t\t})\n\t})\n}\n\n// GET请求\nexport const get = (url, data = {}, options = {}) => {\n\treturn request({\n\t\turl,\n\t\tmethod: 'GET',\n\t\tdata,\n\t\t...options\n\t})\n}\n\n// POST请求\nexport const post = (url, data = {}, options = {}) => {\n\treturn request({\n\t\turl,\n\t\tmethod: 'POST',\n\t\tdata,\n\t\t...options\n\t})\n}\n\n// PUT请求\nexport const put = (url, data = {}, options = {}) => {\n\treturn request({\n\t\turl,\n\t\tmethod: 'PUT',\n\t\tdata,\n\t\t...options\n\t})\n}\n\n// DELETE请求\nexport const del = (url, data = {}, options = {}) => {\n\treturn request({\n\t\turl,\n\t\tmethod: 'DELETE',\n\t\tdata,\n\t\t...options\n\t})\n}\n\n// 文件上传\nexport const upload = (url, filePath, options = {}) => {\n\tconst token = uni.getStorageSync('token')\n\n\treturn new Promise((resolve, reject) => {\n\t\tuni.uploadFile({\n\t\t\turl: BASE_URL + url,\n\t\t\tfilePath,\n\t\t\tname: 'file',\n\t\t\theader: {\n\t\t\t\t'Authorization': token ? `Bearer ${token}` : ''\n\t\t\t},\n\t\t\t...options,\n\t\t\tsuccess: (response) => {\n\t\t\t\ttry {\n\t\t\t\t\tconst data = JSON.parse(response.data)\n\t\t\t\t\tif (data.code === 200) {\n\t\t\t\t\t\tresolve(data)\n\t\t\t\t\t} else {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: data.message || '上传失败',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t})\n\t\t\t\t\t\treject(data)\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '上传失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t\treject(error)\n\t\t\t\t}\n\t\t\t},\n\t\t\tfail: (error) => {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '上传失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t})\n\t\t\t\treject(error)\n\t\t\t}\n\t\t})\n\t})\n}\n\nexport default {\n\tget,\n\tpost,\n\tput,\n\tdel,\n\tupload\n}\n"], "names": ["uni"], "mappings": ";;AACA,MAAM,WAAW;AACjB,MAAM,aAAa;AAGP,MAAC,cAAc,CAAC,cAAc;AACzC,MAAI,CAAC;AAAW,WAAO;AAGvB,MAAI,UAAU,WAAW,SAAS,KAAK,UAAU,WAAW,UAAU,GAAG;AACxE,WAAO;AAAA,EACP;AAGD,MAAI,UAAU,WAAW,GAAG,GAAG;AAC9B,WAAO,aAAa;AAAA,EACpB;AAGD,SAAO,aAAa,MAAM;AAC3B;AAGA,MAAM,qBAAqB,CAAC,YAAY;AAEvC,QAAM,QAAQA,cAAAA,MAAI,eAAe,OAAO;AACxC,MAAI,OAAO;AACV,YAAQ,SAAS;AAAA,MAChB,GAAG,QAAQ;AAAA,MACX,iBAAiB,UAAU,KAAK;AAAA,IAChC;AAAA,EACD;AAGD,UAAQ,SAAS;AAAA,IAChB,gBAAgB;AAAA,IAChB,GAAG,QAAQ;AAAA,EACX;AAGD,MAAI,QAAQ,YAAY,OAAO;AAC9BA,kBAAAA,MAAI,YAAY;AAAA,MACf,OAAO;AAAA,MACP,MAAM;AAAA,IACT,CAAG;AAAA,EACD;AAED,SAAO;AACR;AAGA,MAAM,sBAAsB,CAAC,UAAU,YAAY;AAElD,MAAI,QAAQ,YAAY,OAAO;AAC9BA,kBAAAA,MAAI,YAAa;AAAA,EACjB;AAED,QAAM,EAAE,YAAY,KAAI,IAAK;AAG7B,MAAI,eAAe,KAAK;AACvBA,kBAAAA,MAAI,UAAU;AAAA,MACb,OAAO,QAAQ,UAAU;AAAA,MACzB,MAAM;AAAA,IACT,CAAG;AACD,WAAO,QAAQ,OAAO,QAAQ;AAAA,EAC9B;AAGD,MAAI,KAAK,SAAS,KAAK;AAEtB,QAAI,KAAK,SAAS,KAAK;AACtBA,oBAAG,MAAC,kBAAkB,OAAO;AAC7BA,oBAAG,MAAC,kBAAkB,UAAU;AAChCA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACT,CAAI;AACD,aAAO,QAAQ,OAAO,IAAI;AAAA,IAC1B;AAGD,QAAI,QAAQ,cAAc,OAAO;AAChCA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO,KAAK,WAAW;AAAA,QACvB,MAAM;AAAA,MACV,CAAI;AAAA,IACD;AACD,WAAO,QAAQ,OAAO,IAAI;AAAA,EAC1B;AAGD,SAAO,QAAQ,QAAQ,IAAI;AAC5B;AAGA,MAAM,UAAU,CAAC,YAAY;AAE5B,MAAI,CAAC,QAAQ,IAAI,WAAW,MAAM,GAAG;AACpC,YAAQ,MAAM,WAAW,QAAQ;AAAA,EACjC;AAGD,YAAU,mBAAmB,OAAO;AAEpC,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACvCA,kBAAAA,MAAI,QAAQ;AAAA,MACX,GAAG;AAAA,MACH,SAAS,CAAC,aAAa;AACtB,4BAAoB,UAAU,OAAO,EACnC,KAAK,OAAO,EACZ,MAAM,MAAM;AAAA,MACd;AAAA,MACD,MAAM,CAAC,UAAU;AAEhB,YAAI,QAAQ,YAAY,OAAO;AAC9BA,wBAAAA,MAAI,YAAa;AAAA,QACjB;AAEDA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACX,CAAK;AACD,eAAO,KAAK;AAAA,MACZ;AAAA,IACJ,CAAG;AAAA,EACH,CAAE;AACF;AAGY,MAAC,MAAM,CAAC,KAAK,OAAO,CAAA,GAAI,UAAU,CAAA,MAAO;AACpD,SAAO,QAAQ;AAAA,IACd;AAAA,IACA,QAAQ;AAAA,IACR;AAAA,IACA,GAAG;AAAA,EACL,CAAE;AACF;AAGY,MAAC,OAAO,CAAC,KAAK,OAAO,CAAA,GAAI,UAAU,CAAA,MAAO;AACrD,SAAO,QAAQ;AAAA,IACd;AAAA,IACA,QAAQ;AAAA,IACR;AAAA,IACA,GAAG;AAAA,EACL,CAAE;AACF;AAGY,MAAC,MAAM,CAAC,KAAK,OAAO,CAAA,GAAI,UAAU,CAAA,MAAO;AACpD,SAAO,QAAQ;AAAA,IACd;AAAA,IACA,QAAQ;AAAA,IACR;AAAA,IACA,GAAG;AAAA,EACL,CAAE;AACF;AAGY,MAAC,MAAM,CAAC,KAAK,OAAO,CAAA,GAAI,UAAU,CAAA,MAAO;AACpD,SAAO,QAAQ;AAAA,IACd;AAAA,IACA,QAAQ;AAAA,IACR;AAAA,IACA,GAAG;AAAA,EACL,CAAE;AACF;;;;;;"}