"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  name: "SvgIcon",
  props: {
    name: {
      type: String,
      default: "default"
    },
    size: {
      type: [String, Number],
      default: 24
    },
    color: {
      type: String,
      default: "#333"
    }
  },
  computed: {
    iconStyle() {
      return {
        width: this.size + "px",
        height: this.size + "px",
        display: "inline-flex",
        alignItems: "center",
        justifyContent: "center"
      };
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $props.name === "heart"
  }, $props.name === "heart" ? {
    b: $props.color,
    c: $props.size + "px"
  } : $props.name === "heart-filled" ? {
    e: $props.color,
    f: $props.size + "px"
  } : $props.name === "share" ? {
    h: $props.color,
    i: $props.size + "px"
  } : $props.name === "calendar" ? {
    k: $props.color,
    l: $props.size + "px"
  } : $props.name === "wheel" ? {
    n: $props.color,
    o: $props.size + "px"
  } : $props.name === "tasks" ? {
    q: $props.color,
    r: $props.size + "px"
  } : $props.name === "invite" ? {
    t: $props.color,
    v: $props.size + "px"
  } : $props.name === "pending" ? {
    x: $props.color,
    y: $props.size + "px"
  } : $props.name === "processing" ? {
    A: $props.color,
    B: $props.size + "px"
  } : $props.name === "shipped" ? {
    D: $props.color,
    E: $props.size + "px"
  } : $props.name === "completed" ? {
    G: $props.color,
    H: $props.size + "px"
  } : $props.name === "home" ? {
    J: $props.color,
    K: $props.size + "px"
  } : $props.name === "shop" ? {
    M: $props.color,
    N: $props.size + "px"
  } : $props.name === "points" ? {
    P: $props.color,
    Q: $props.size + "px"
  } : $props.name === "user" ? {
    S: $props.color,
    T: $props.size + "px"
  } : {
    U: $props.color,
    V: $props.size + "px"
  }, {
    d: $props.name === "heart-filled",
    g: $props.name === "share",
    j: $props.name === "calendar",
    m: $props.name === "wheel",
    p: $props.name === "tasks",
    s: $props.name === "invite",
    w: $props.name === "pending",
    z: $props.name === "processing",
    C: $props.name === "shipped",
    F: $props.name === "completed",
    I: $props.name === "home",
    L: $props.name === "shop",
    O: $props.name === "points",
    R: $props.name === "user",
    W: common_vendor.s($options.iconStyle)
  });
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-e9423230"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/svg-icon/svg-icon.js.map
