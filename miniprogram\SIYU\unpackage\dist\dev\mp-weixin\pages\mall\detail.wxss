
.detail-container.data-v-da39d73f {
	background: #f8f8f8;
	min-height: 100vh;
	padding-bottom: 80px;
}
.product-images.data-v-da39d73f {
	background: #ffffff;
	margin-bottom: 10px;
}
.main-image.data-v-da39d73f {
	height: 300px;
	background: #f5f5f5;
	display: flex;
	align-items: center;
	justify-content: center;
}
.product-emoji.data-v-da39d73f {
	font-size: 120px;
}
.product-image.data-v-da39d73f {
	width: 100%;
	height: 100%;
}
.product-info.data-v-da39d73f {
	background: #ffffff;
	padding: 20px;
	margin-bottom: 10px;
}
.product-header.data-v-da39d73f {
	margin-bottom: 15px;
}
.product-name.data-v-da39d73f {
	display: block;
	font-size: 18px;
	font-weight: 500;
	color: #333;
	margin-bottom: 8px;
	line-height: 1.4;
}
.product-description.data-v-da39d73f {
	display: block;
	font-size: 14px;
	color: #666;
	line-height: 1.5;
	margin-bottom: 15px;
}
.product-price.data-v-da39d73f {
	display: flex;
	align-items: baseline;
	gap: 10px;
}
.points-price.data-v-da39d73f {
	font-size: 24px;
	font-weight: bold;
	color: #667eea;
}
.market-price.data-v-da39d73f {
	font-size: 14px;
	color: #999;
	text-decoration: line-through;
}
.product-tags.data-v-da39d73f {
	margin-bottom: 15px;
}
.tag.data-v-da39d73f {
	display: inline-block;
	padding: 4px 8px;
	background: rgba(102, 126, 234, 0.1);
	color: #667eea;
	font-size: 12px;
	border-radius: 4px;
	margin-right: 8px;
}
.product-stats.data-v-da39d73f {
	display: flex;
	justify-content: space-around;
	padding: 15px 0;
	border-top: 1px solid #f5f5f5;
}
.stat-item.data-v-da39d73f {
	text-align: center;
}
.stat-label.data-v-da39d73f {
	display: block;
	font-size: 12px;
	color: #999;
	margin-bottom: 4px;
}
.stat-value.data-v-da39d73f {
	display: block;
	font-size: 16px;
	font-weight: 500;
	color: #333;
}
.product-detail.data-v-da39d73f,
.exchange-rules.data-v-da39d73f {
	background: #ffffff;
	padding: 20px;
	margin-bottom: 10px;
}
.section-title.data-v-da39d73f {
	margin-bottom: 15px;
	padding: 0 5px;
}
.title-text.data-v-da39d73f {
	font-size: 16px;
	font-weight: 500;
	color: #333;
}
.detail-content.data-v-da39d73f {
	line-height: 1.6;
	padding: 0 5px 15px 5px;
}
.detail-images.data-v-da39d73f {
	margin-top: 0;
}
.detail-image-item.data-v-da39d73f {
	margin-bottom: 15px;
	background: #f5f5f5;
}
.detail-image.data-v-da39d73f {
	width: 100%;
	display: block;
}
.rules-list.data-v-da39d73f {
	padding: 0;
	margin: 0;
}
.rule-item.data-v-da39d73f {
	margin-bottom: 8px;
}
.rule-text.data-v-da39d73f {
	font-size: 14px;
	color: #666;
	line-height: 1.5;
}
.bottom-actions.data-v-da39d73f {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: #ffffff;
	padding: 10px 15px;
	border-top: 1px solid #f0f0f0;
	display: flex;
	align-items: center;
	justify-content: space-between;
	z-index: 100;
}
.action-left.data-v-da39d73f {
	display: flex;
	gap: 20px;
}
.action-btn.data-v-da39d73f {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 4px;
}
.action-text.data-v-da39d73f {
	font-size: 12px;
	color: #999;
}
.action-right.data-v-da39d73f {
	flex: 1;
	margin-left: 20px;
}
.exchange-btn.data-v-da39d73f {
	width: 100%;
	height: 44px;
	background: #667eea;
	border-radius: 22px;
	border: none;
	display: flex;
	align-items: center;
	justify-content: center;
}
.exchange-btn[disabled].data-v-da39d73f {
	background: #ccc;
}
.btn-text.data-v-da39d73f {
	font-size: 16px;
	font-weight: 500;
	color: #ffffff;
}
