<template>
	<view class="user-container">
		<!-- 用户信息卡片 -->
		<view class="user-card">
			<view class="user-bg">
				<view class="user-info" @click="handleUserClick">
					<view class="avatar">
						<image v-if="userInfo.avatarUrl" class="avatar-image" :src="userInfo.avatarUrl" mode="aspectFill" @error="onImageError" @load="onImageLoad"></image>
						<text v-else class="avatar-emoji">👤</text>
					</view>
					<view class="user-details">
						<text class="nickname">{{ isLoggedIn ? userInfo.nickName : '登录/注册' }}</text>
						<text class="user-id" v-if="isLoggedIn">ID: {{ userInfo.id || '000000' }}</text>
						<text class="user-desc" v-else>点击登录享受更多服务</text>
					</view>
					<text class="action-arrow">›</text>
				</view>

				<view class="user-stats">
					<view class="stat-item" @click="goToPoints">
						<text class="stat-number">{{ userStats.points }}</text>
						<text class="stat-label">积分</text>
					</view>
					<view class="stat-item" @click="goToOrders">
						<text class="stat-number">{{ userStats.orders }}</text>
						<text class="stat-label">订单</text>
					</view>
					<view class="stat-item" @click="goToCoupons">
						<text class="stat-number">{{ userStats.coupons }}</text>
						<text class="stat-label">优惠券</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 我的订单 -->
		<view class="section">
			<view class="section-header" @click="goToOrders">
				<text class="section-title">我的订单</text>
				<view class="section-more">
					<text class="more-text">查看全部</text>
					<uni-icons type="right" size="14" color="#999"></uni-icons>
				</view>
			</view>

			<view class="order-types">
				<view class="order-type" @click="goToOrders('pending')">
					<svg-icon name="pending" :size="24" color="#f39c12"></svg-icon>
					<text class="type-text">待付款</text>
					<view class="badge" v-if="orderCounts.pending > 0">{{ orderCounts.pending }}</view>
				</view>
				<view class="order-type" @click="goToOrders('processing')">
					<svg-icon name="processing" :size="24" color="#3498db"></svg-icon>
					<text class="type-text">处理中</text>
					<view class="badge" v-if="orderCounts.processing > 0">{{ orderCounts.processing }}</view>
				</view>
				<view class="order-type" @click="goToOrders('shipped')">
					<svg-icon name="shipped" :size="24" color="#9b59b6"></svg-icon>
					<text class="type-text">已发货</text>
					<view class="badge" v-if="orderCounts.shipped > 0">{{ orderCounts.shipped }}</view>
				</view>
				<view class="order-type" @click="goToOrders('completed')">
					<svg-icon name="completed" :size="24" color="#27ae60"></svg-icon>
					<text class="type-text">已完成</text>
				</view>
			</view>
		</view>

		<!-- 功能菜单 -->
		<view class="section">
			<view class="menu-list">
				<view class="menu-item" @click="goToAddress">
					<view class="menu-left">
						<uni-icons type="location" size="20" color="#667eea"></uni-icons>
						<text class="menu-text">收货地址</text>
					</view>
					<uni-icons type="right" size="14" color="#999"></uni-icons>
				</view>

				<view class="menu-item" @click="goToFavorites">
					<view class="menu-left">
						<uni-icons type="heart" size="20" color="#e74c3c"></uni-icons>
						<text class="menu-text">我的收藏</text>
					</view>
					<uni-icons type="right" size="14" color="#999"></uni-icons>
				</view>

				<view class="menu-item" @click="goToHelp">
					<view class="menu-left">
						<uni-icons type="help" size="20" color="#f39c12"></uni-icons>
						<text class="menu-text">帮助中心</text>
					</view>
					<uni-icons type="right" size="14" color="#999"></uni-icons>
				</view>


			</view>
		</view>

		<!-- 退出登录 -->
		<view class="section" v-if="isLoggedIn">
			<view class="logout-btn" @click="handleLogout">
				<text class="logout-text">退出登录</text>
			</view>
		</view>

		<!-- 头像昵称编辑弹窗 -->
		<view v-if="showProfileModal" class="profile-modal-overlay" @click="closeProfileModal">
			<view class="profile-modal" @click.stop>
				<view class="modal-header">
					<text class="modal-title">获取你的昵称、头像</text>
					<view class="modal-close" @click="closeProfileModal">
						<uni-icons type="close" size="20" color="#999"></uni-icons>
					</view>
				</view>

				<view class="modal-content">
					<text class="modal-desc">获取用户头像、昵称，主要用于向用户提供具有辨识度的用户中心页面。</text>

					<!-- 头像选择 -->
					<view class="avatar-section">
						<text class="section-label">头像</text>
						<!-- 主要头像选择按钮 - 直接使用可靠的图片选择 -->
						<button
							class="avatar-button"
							@click="chooseImageFallback"
						>
							<image
								:src="tempAvatarUrl || '/static/images/default-avatar.png'"
								class="avatar-preview"
								mode="aspectFill"
							/>
							<view class="avatar-arrow">
								<uni-icons type="right" size="14" color="#ccc"></uni-icons>
							</view>
						</button>

						<!-- 微信官方头像选择（备选方案） -->
						<button
							v-if="canUseChooseAvatar"
							class="wechat-avatar-button"
							open-type="chooseAvatar"
							@chooseavatar="onChooseAvatar"
							style="margin-top: 8px; font-size: 12px; color: #007aff; background: none; border: 1px solid #007aff; border-radius: 4px; padding: 4px 8px;"
						>
							或使用微信官方头像选择
						</button>
					</view>

					<!-- 昵称输入 -->
					<view class="nickname-section">
						<text class="section-label">昵称</text>
						<input
							class="nickname-input"
							type="nickname"
							v-model="tempNickname"
							placeholder="请输入昵称"
							maxlength="20"
							@blur="onBlurNickname"
						/>
					</view>
				</view>

				<view class="modal-footer">
					<button class="save-button" @click="saveProfile">保存</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { userAPI, authAPI } from '@/api/index.js'
import AuthUtils from '@/utils/auth.js'
import SvgIcon from '@/components/svg-icon/svg-icon.vue'

export default {
	components: {
		SvgIcon
	},
	data() {
		return {
			userInfo: {},
			userStats: {
				points: 0,
				orders: 0,
				coupons: 0
			},
			orderCounts: {
				pending: 0,
				processing: 0,
				shipped: 0,
				completed: 0
			},
			isLoggedIn: false,
			// 头像昵称编辑相关
			showProfileModal: false,
			tempNickname: '',
			tempAvatarUrl: '',
			canUseChooseAvatar: false, // 是否支持新版头像选择
			isLoadingStats: false // 是否正在加载统计数据
		}
	},
	async onLoad() {
		console.log('用户中心页面 onLoad');
		this.checkChooseAvatarSupport();
		this.loadUserInfo();
		await this.loadUserStats();
	},
	async onShow() {
		console.log('用户中心页面 onShow');
		// 只在页面显示时更新登录状态，避免重复请求
		this.loadUserInfo();

		// 如果用户信息为空或者登录状态改变，才重新加载
		if (!this.userInfo.id || this.isLoggedIn !== AuthUtils.isLoggedIn()) {
			await this.loadUserStats();
		}
	},
	methods: {
		// 加载用户信息
		loadUserInfo() {
			this.isLoggedIn = AuthUtils.isLoggedIn()
			// 只在没有用户信息时从本地存储加载，避免覆盖服务器数据
			if (!this.userInfo.id) {
				this.userInfo = AuthUtils.getCurrentUser() || {}
			}
		},

		// 加载用户统计数据
		async loadUserStats() {
			// 防止重复加载
			if (this.isLoadingStats) {
				console.log('正在加载中，跳过重复请求');
				return;
			}

			try {
				// 检查是否已登录
				const token = uni.getStorageSync('token')
				if (!token) {
					this.userStats = {
						points: 0,
						orders: 0,
						coupons: 0
					}
					this.orderCounts = {
						pending: 0,
						processing: 0,
						shipped: 0,
						completed: 0
					}
					return
				}

				this.isLoadingStats = true;
				console.log('开始加载用户统计数据');

				// 尝试获取用户详细信息
				try {
					const userInfoResult = await userAPI.getProfile()
					if (userInfoResult.data) {
						const userData = userInfoResult.data

						// 转换数据格式以匹配模板期望的字段名
						this.userInfo = {
							...userData,
							nickName: userData.nickname,  // 转换字段名
							avatarUrl: userData.avatar    // 转换字段名
						}

						uni.setStorageSync('userInfo', this.userInfo)

						this.userStats = {
							points: userData.available_points || 0,
							orders: userData.stats?.total_orders || 0,
							coupons: 0
						}

						console.log('用户信息加载成功');
					}
				} catch (userInfoError) {
					console.log('获取用户信息失败，尝试统计接口')
				}

				// 尝试获取统计数据
				try {
					const response = await userAPI.getStats()
					this.userStats = response.data.stats
					this.orderCounts = response.data.orderCounts
				} catch (statsError) {
					console.log('统计接口不存在，使用默认数据')
					// 如果统计接口不存在，使用用户信息中的数据
					const userInfo = this.userInfo || uni.getStorageSync('userInfo')
					if (userInfo) {
						this.userStats.points = userInfo.available_points || 0
					}
				}
			} catch (error) {
				console.error('获取用户统计数据失败:', error)
				// 使用模拟数据作为降级方案
				this.userStats = {
					points: 1580,
					orders: 12,
					coupons: 3
				}

				this.orderCounts = {
					pending: 1,
					processing: 2,
					shipped: 1,
					completed: 8
				}
			} finally {
				this.isLoadingStats = false;
				console.log('用户统计数据加载完成');
			}
		},

		// 处理用户信息点击
		handleUserClick() {
			if (!this.isLoggedIn) {
				this.goToLogin();
			} else {
				// 已登录用户显示头像昵称编辑弹窗
				this.showProfileModal = true;
				this.tempNickname = this.userInfo.nickName || '';
				this.tempAvatarUrl = this.userInfo.avatarUrl || '';
			}
		},

		// 跳转到积分页面
		goToPoints() {
			uni.switchTab({
				url: '/pages/points/index'
			});
		},

		// 跳转到订单页面
		goToOrders(status = '') {
			const url = status ? `/pages/user/orders?status=${status}` : '/pages/user/orders';
			uni.navigateTo({
				url: url
			});
		},

		// 跳转到优惠券
		goToCoupons() {
			uni.navigateTo({
				url: '/pages/user/coupons'
			});
		},

		// 跳转到收货地址
		goToAddress() {
			uni.navigateTo({
				url: '/pages/address/list'
			});
		},

		// 跳转到收藏
		goToFavorites() {
			uni.navigateTo({
				url: '/pages/user/favorites'
			});
		},

		// 跳转到帮助中心
		goToHelp() {
			uni.navigateTo({
				url: '/pages/user/help'
			});
		},



		// 跳转到登录
		goToLogin() {
			AuthUtils.navigateToLogin()
		},

		// 退出登录
		async handleLogout() {
			uni.showModal({
				title: '提示',
				content: '确定要退出登录吗？',
				success: async (res) => {
					if (res.confirm) {
						try {
							// 使用AuthUtils退出登录
							await AuthUtils.logout()

							// 重置页面数据
							this.userInfo = {}
							this.isLoggedIn = false
							this.userStats = {
								points: 0,
								orders: 0,
								coupons: 0
							}
							this.orderCounts = {
								pending: 0,
								processing: 0,
								shipped: 0,
								completed: 0
							}
						} catch (error) {
							console.error('退出登录失败:', error)
							uni.showToast({
								title: '退出失败，请重试',
								icon: 'none'
							})
						}
					}
				}
			})
		},

		// 关闭头像昵称编辑弹窗
		closeProfileModal() {
			this.showProfileModal = false;
			this.tempNickname = '';
			this.tempAvatarUrl = '';
		},

		// 微信官方头像选择回调
		onChooseAvatar(e) {
			console.log('微信官方头像选择事件触发:', e.detail);

			if (e.detail && e.detail.avatarUrl) {
				console.log('获取到微信官方头像URL:', e.detail.avatarUrl);
				this.tempAvatarUrl = e.detail.avatarUrl;

				uni.showToast({
					title: '头像选择成功',
					icon: 'success'
				});

				this.$forceUpdate();
			} else {
				console.log('微信官方头像选择失败');
				uni.showToast({
					title: '头像选择失败，请使用相册选择',
					icon: 'none'
				});
			}
		},

		// 昵称输入失焦回调
		onBlurNickname(e) {
			console.log('昵称输入失焦:', e.detail.value);
			// 这里可以添加昵称验证逻辑
			if (e.detail.value && e.detail.value.trim()) {
				this.tempNickname = e.detail.value.trim();
			}
		},

		// 保存头像昵称
		async saveProfile() {
			if (!this.tempNickname.trim()) {
				uni.showToast({
					title: '请输入昵称',
					icon: 'none'
				});
				return;
			}

			try {
				uni.showLoading({
					title: '保存中...'
				});

				// 准备更新数据
				const updateData = {
					nickname: this.tempNickname.trim()
				};

				// 只有当头像URL不为空时才包含头像字段
				if (this.tempAvatarUrl && this.tempAvatarUrl.trim()) {
					updateData.avatar = this.tempAvatarUrl.trim();
				}

				// 调用API更新用户信息
				const response = await userAPI.updateProfile(updateData);

				if (response.code === 200) {
					// 更新本地用户信息（字段映射）
					this.userInfo.nickName = this.tempNickname.trim();
					this.userInfo.avatarUrl = this.tempAvatarUrl;
					this.userInfo.nickname = this.tempNickname.trim(); // 后端字段
					this.userInfo.avatar = this.tempAvatarUrl; // 后端字段

					// 更新本地存储
					uni.setStorageSync('userInfo', this.userInfo);

					uni.hideLoading();
					uni.showToast({
						title: '保存成功',
						icon: 'success'
					});

					this.closeProfileModal();

					// 重新加载用户信息以确保数据同步
					this.loadUserStats();
				} else {
					throw new Error(response.message || '保存失败');
				}
			} catch (error) {
				uni.hideLoading();
				console.error('保存头像昵称失败:', error);
				uni.showToast({
					title: error.message || '保存失败',
					icon: 'none'
				});
			}
		},

		// 图片加载成功
		onImageLoad(e) {
			console.log('头像加载成功:', e);
		},

		// 图片加载失败
		onImageError(e) {
			console.log('头像加载失败:', e);
		},

		// 检查是否支持新版头像选择
		checkChooseAvatarSupport() {
			try {
				const systemInfo = uni.getSystemInfoSync();
				const SDKVersion = systemInfo.SDKVersion || '0.0.0';
				const version = SDKVersion.split('.').map(v => parseInt(v) || 0);

				// 需要基础库版本 2.21.2 或以上
				let isSupported = false;
				if (version[0] > 2) {
					isSupported = true;
				} else if (version[0] === 2) {
					if (version[1] > 21) {
						isSupported = true;
					} else if (version[1] === 21 && version[2] >= 2) {
						isSupported = true;
					}
				}

				this.canUseChooseAvatar = isSupported;
				console.log('微信官方头像选择支持状态:', isSupported ? '支持' : '不支持', '当前版本:', SDKVersion);

			} catch (error) {
				console.error('检查头像选择支持时出错:', error);
				this.canUseChooseAvatar = false;
			}
		},

		// 图片选择方案（主要方案）
		chooseImageFallback() {
			console.log('使用图片选择');
			uni.chooseImage({
				count: 1,
				sizeType: ['compressed'],
				sourceType: ['album', 'camera'],
				success: (res) => {
					console.log('图片选择成功:', res);
					if (res.tempFilePaths && res.tempFilePaths.length > 0) {
						this.tempAvatarUrl = res.tempFilePaths[0];
						console.log('设置临时头像URL:', this.tempAvatarUrl);

						uni.showToast({
							title: '头像选择成功',
							icon: 'success'
						});

						this.$forceUpdate();
					}
				},
				fail: (err) => {
					console.log('图片选择失败:', err);
					uni.showToast({
						title: '图片选择失败',
						icon: 'none'
					});
				}
			});
		}
	}
}
</script>

<style scoped>
.user-container {
	background: #f8f8f8;
	min-height: 100vh;
}

.user-card {
	margin-bottom: 15px;
}

.user-bg {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 25px 20px;
	color: #ffffff;
}

.user-info {
	display: flex;
	align-items: center;
	margin-bottom: 25px;
}

.avatar {
	width: 60px;
	height: 60px;
	border-radius: 30px;
	margin-right: 15px;
	background: rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
}

.avatar-image {
	width: 100%;
	height: 100%;
	border-radius: 30px;
}

.avatar-emoji {
	font-size: 32px;
}

.action-arrow {
	font-size: 20px;
	color: rgba(255, 255, 255, 0.8);
}

.user-details {
	flex: 1;
}

.nickname {
	display: block;
	font-size: 18px;
	font-weight: 500;
	margin-bottom: 5px;
}

.user-id {
	display: block;
	font-size: 12px;
	opacity: 0.8;
}

.user-desc {
	display: block;
	font-size: 12px;
	opacity: 0.8;
	color: rgba(255, 255, 255, 0.7);
}

.user-stats {
	display: flex;
	justify-content: space-around;
}

.stat-item {
	display: flex;
	flex-direction: column;
	align-items: center;
}

.stat-number {
	font-size: 20px;
	font-weight: bold;
	margin-bottom: 5px;
}

.stat-label {
	font-size: 12px;
	opacity: 0.8;
}

.section {
	background: #ffffff;
	margin-bottom: 15px;
}

.section-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 15px 20px;
	border-bottom: 1px solid #f5f5f5;
}

.section-title {
	font-size: 16px;
	font-weight: 500;
	color: #333;
}

.section-more {
	display: flex;
	align-items: center;
}

.more-text {
	font-size: 12px;
	color: #999;
	margin-right: 5px;
}

.order-types {
	display: flex;
	justify-content: space-around;
	padding: 20px;
}

.order-type {
	display: flex;
	flex-direction: column;
	align-items: center;
	position: relative;
}

.type-text {
	font-size: 12px;
	color: #333;
	margin-top: 8px;
}

.badge {
	position: absolute;
	top: -5px;
	right: -10px;
	background: #f56c6c;
	color: #ffffff;
	font-size: 10px;
	padding: 2px 6px;
	border-radius: 10px;
	min-width: 16px;
	text-align: center;
}

.menu-list {
	/* 菜单列表样式 */
}

.menu-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 15px 20px;
	border-bottom: 1px solid #f5f5f5;
}

.menu-item:last-child {
	border-bottom: none;
}

.menu-left {
	display: flex;
	align-items: center;
}

.menu-text {
	font-size: 14px;
	color: #333;
	margin-left: 12px;
}

.logout-btn {
	margin: 20px;
	background: #f56c6c;
	border-radius: 8px;
	padding: 15px;
	text-align: center;
}

.logout-text {
	color: #ffffff;
	font-size: 16px;
	font-weight: 500;
}

/* 头像昵称编辑弹窗样式 */
.profile-modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	z-index: 1000;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 40rpx;
}

.profile-modal {
	background: white;
	border-radius: 20rpx;
	width: 100%;
	max-width: 600rpx;
	max-height: 80vh;
	overflow: hidden;
}

.modal-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 40rpx 40rpx 20rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

.modal-close {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.modal-content {
	padding: 40rpx;
}

.modal-desc {
	font-size: 28rpx;
	color: #666;
	line-height: 1.6;
	margin-bottom: 40rpx;
}

.avatar-section,
.nickname-section {
	margin-bottom: 40rpx;
}

.section-label {
	display: block;
	font-size: 28rpx;
	color: #333;
	margin-bottom: 20rpx;
}

.avatar-button {
	display: flex;
	align-items: center;
	justify-content: space-between;
	background: #f8f9fa;
	border: none;
	border-radius: 16rpx;
	padding: 20rpx;
	width: 100%;
}

.avatar-preview {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	background: #ddd;
}

.nickname-input {
	width: 100%;
	height: 80rpx;
	background: #f8f9fa;
	border: none;
	border-radius: 16rpx;
	padding: 0 30rpx;
	font-size: 28rpx;
	color: #333;
}

.modal-footer {
	padding: 20rpx 40rpx 40rpx;
}

.save-button {
	width: 100%;
	height: 80rpx;
	background: #007aff;
	color: white;
	border: none;
	border-radius: 40rpx;
	font-size: 32rpx;
	font-weight: 500;
}
</style>
