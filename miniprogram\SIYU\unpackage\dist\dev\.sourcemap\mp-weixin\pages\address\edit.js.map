{"version": 3, "file": "edit.js", "sources": ["pages/address/edit.vue", "../../../Program Files/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvYWRkcmVzcy9lZGl0LnZ1ZQ"], "sourcesContent": ["<template>\n\t<view class=\"address-edit-container\">\n\t\t<form @submit=\"submitForm\">\n\t\t\t<!-- 收货人信息 -->\n\t\t\t<view class=\"form-section\">\n\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t<text class=\"label\">收货人 <text class=\"required\">*</text></text>\n\t\t\t\t\t<input\n\t\t\t\t\t\tclass=\"input\"\n\t\t\t\t\t\tv-model=\"form.name\"\n\t\t\t\t\t\tplaceholder=\"请输入收货人姓名\"\n\t\t\t\t\t\tmaxlength=\"20\"\n\t\t\t\t\t/>\n\t\t\t\t</view>\n\n\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t<text class=\"label\">手机号 <text class=\"required\">*</text></text>\n\t\t\t\t\t<input\n\t\t\t\t\t\tclass=\"input\"\n\t\t\t\t\t\tv-model=\"form.phone\"\n\t\t\t\t\t\tplaceholder=\"请输入手机号\"\n\t\t\t\t\t\ttype=\"number\"\n\t\t\t\t\t\tmaxlength=\"11\"\n\t\t\t\t\t/>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 地区选择 -->\n\t\t\t<view class=\"form-section\">\n\t\t\t\t<view class=\"form-item\" @click=\"selectRegion\">\n\t\t\t\t\t<text class=\"label\">所在地区 <text class=\"required\">*</text></text>\n\t\t\t\t\t<view class=\"region-selector\">\n\t\t\t\t\t\t<text class=\"region-text\" v-if=\"regionText\">{{ regionText }}</text>\n\t\t\t\t\t\t<text class=\"placeholder\" v-else>请选择省市区</text>\n\t\t\t\t\t\t<text class=\"arrow\">></text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 详细地址 -->\n\t\t\t<view class=\"form-section\">\n\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t<text class=\"label\">详细地址 <text class=\"required\">*</text></text>\n\t\t\t\t\t<textarea\n\t\t\t\t\t\tclass=\"textarea\"\n\t\t\t\t\t\tv-model=\"form.detail\"\n\t\t\t\t\t\tplaceholder=\"请输入详细地址，如街道、楼牌号等\"\n\t\t\t\t\t\tmaxlength=\"200\"\n\t\t\t\t\t\tauto-height\n\t\t\t\t\t/>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 默认地址 -->\n\t\t\t<view class=\"form-section\">\n\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t<view class=\"switch-item\">\n\t\t\t\t\t\t<text class=\"label\">设为默认地址</text>\n\t\t\t\t\t\t<switch\n\t\t\t\t\t\t\t:checked=\"form.is_default\"\n\t\t\t\t\t\t\t@change=\"onDefaultChange\"\n\t\t\t\t\t\t\tcolor=\"#667eea\"\n\t\t\t\t\t\t/>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 提交按钮 -->\n\t\t\t<view class=\"submit-section\">\n\t\t\t\t<button\n\t\t\t\t\tclass=\"submit-btn\"\n\t\t\t\t\t:disabled=\"!canSubmit || submitting\"\n\t\t\t\t\t@click=\"submitForm\"\n\t\t\t\t>\n\t\t\t\t\t{{ submitting ? '保存中...' : (isEdit ? '保存修改' : '保存地址') }}\n\t\t\t\t</button>\n\t\t\t</view>\n\t\t</form>\n\n\t\t<!-- 地区选择器 -->\n\t\t<region-picker\n\t\t\t:visible=\"showRegionPicker\"\n\t\t\t:value=\"regionValue\"\n\t\t\t@confirm=\"onRegionConfirm\"\n\t\t\t@close=\"showRegionPicker = false\"\n\t\t/>\n\t</view>\n</template>\n\n<script>\nimport { addressAPI } from '@/api/index.js'\nimport RegionPicker from '@/components/region-picker/region-picker.vue'\n\nexport default {\n\tcomponents: {\n\t\tRegionPicker\n\t},\n\tdata() {\n\t\treturn {\n\t\t\taddressId: 0,\n\t\t\tisEdit: false,\n\t\t\tsubmitting: false,\n\t\t\tshowRegionPicker: false,\n\t\t\tform: {\n\t\t\t\tname: '',\n\t\t\t\tphone: '',\n\t\t\t\tprovince: '',\n\t\t\t\tcity: '',\n\t\t\t\tdistrict: '',\n\t\t\t\tdetail: '',\n\t\t\t\tis_default: false\n\t\t\t}\n\t\t}\n\t},\n\tcomputed: {\n\t\tregionText() {\n\t\t\tif (this.form.province && this.form.city && this.form.district) {\n\t\t\t\treturn `${this.form.province} ${this.form.city} ${this.form.district}`\n\t\t\t}\n\t\t\treturn ''\n\t\t},\n\t\tregionValue() {\n\t\t\treturn {\n\t\t\t\tprovince: this.form.province,\n\t\t\t\tcity: this.form.city,\n\t\t\t\tdistrict: this.form.district\n\t\t\t}\n\t\t},\n\t\tcanSubmit() {\n\t\t\treturn this.form.name.trim() &&\n\t\t\t\t   this.form.phone.trim() &&\n\t\t\t\t   this.form.province &&\n\t\t\t\t   this.form.city &&\n\t\t\t\t   this.form.district &&\n\t\t\t\t   this.form.detail.trim()\n\t\t}\n\t},\n\tonLoad(options) {\n\t\tif (options.id) {\n\t\t\tthis.addressId = parseInt(options.id)\n\t\t\tthis.isEdit = true\n\t\t\tthis.loadAddress()\n\t\t}\n\t},\n\tmethods: {\n\t\t// 加载地址信息（编辑模式）\n\t\tasync loadAddress() {\n\t\t\ttry {\n\t\t\t\tconst response = await addressAPI.getDetail(this.addressId)\n\t\t\t\tconst address = response.data\n\n\t\t\t\tthis.form = {\n\t\t\t\t\tname: address.name,\n\t\t\t\t\tphone: address.phone,\n\t\t\t\t\tprovince: address.province,\n\t\t\t\t\tcity: address.city,\n\t\t\t\t\tdistrict: address.district,\n\t\t\t\t\tdetail: address.detail,\n\t\t\t\t\tis_default: address.is_default\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('获取地址信息失败:', error)\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '获取地址信息失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t})\n\t\t\t\tuni.navigateBack()\n\t\t\t}\n\t\t},\n\n\t\t// 选择地区\n\t\tselectRegion() {\n\t\t\tthis.showRegionPicker = true\n\t\t},\n\n\t\t// 地区选择确认\n\t\tonRegionConfirm(region) {\n\t\t\tthis.form.province = region.province\n\t\t\tthis.form.city = region.city\n\t\t\tthis.form.district = region.district\n\t\t},\n\n\t\t// 默认地址开关变化\n\t\tonDefaultChange(e) {\n\t\t\tthis.form.is_default = e.detail.value\n\t\t},\n\n\t\t// 提交表单\n\t\tasync submitForm() {\n\t\t\tif (!this.canSubmit || this.submitting) return\n\n\t\t\t// 验证手机号\n\t\t\tif (!/^1[3-9]\\d{9}$/.test(this.form.phone)) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请输入正确的手机号',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t})\n\t\t\t\treturn\n\t\t\t}\n\n\t\t\tthis.submitting = true\n\n\t\t\ttry {\n\t\t\t\tconst formData = { ...this.form }\n\n\t\t\t\tif (this.isEdit) {\n\t\t\t\t\tawait addressAPI.update(this.addressId, formData)\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '修改成功',\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t})\n\t\t\t\t} else {\n\t\t\t\t\tawait addressAPI.create(formData)\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '添加成功',\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t})\n\t\t\t\t}\n\n\t\t\t\t// 返回上一页\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tuni.navigateBack()\n\t\t\t\t}, 1500)\n\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('保存地址失败:', error)\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: error.message || '保存失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t})\n\t\t\t} finally {\n\t\t\t\tthis.submitting = false\n\t\t\t}\n\t\t}\n\t}\n}\n</script>\n\n<style scoped>\n.address-edit-container {\n\tbackground: #f8f8f8;\n\tmin-height: 100vh;\n\tpadding: 10px;\n}\n\n.form-section {\n\tbackground: white;\n\tborder-radius: 12px;\n\tmargin-bottom: 10px;\n\toverflow: hidden;\n}\n\n.form-item {\n\tpadding: 20px;\n\tborder-bottom: 1px solid #f0f0f0;\n}\n\n.form-item:last-child {\n\tborder-bottom: none;\n}\n\n.label {\n\tdisplay: block;\n\tfont-size: 16px;\n\tcolor: #333;\n\tmargin-bottom: 10px;\n}\n\n.required {\n\tcolor: #ff4757;\n}\n\n.input, .textarea {\n\twidth: 100%;\n\tfont-size: 16px;\n\tcolor: #333;\n\tbackground: transparent;\n\tborder: none;\n\toutline: none;\n}\n\n.input::placeholder, .textarea::placeholder {\n\tcolor: #999;\n}\n\n.region-selector {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tpadding: 10px 0;\n}\n\n.region-text {\n\tcolor: #333;\n\tfont-size: 16px;\n}\n\n.placeholder {\n\tcolor: #999;\n\tfont-size: 16px;\n}\n\n.arrow {\n\tcolor: #999;\n\tfont-size: 16px;\n}\n\n.switch-item {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n}\n\n.submit-section {\n\tpadding: 20px 0;\n}\n\n.submit-btn {\n\twidth: 100%;\n\tbackground: #667eea;\n\tcolor: white;\n\tborder: none;\n\tborder-radius: 25px;\n\tpadding: 15px;\n\tfont-size: 16px;\n\tfont-weight: bold;\n}\n\n.submit-btn:disabled {\n\tbackground: #ccc;\n}\n</style>\n", "import MiniProgramPage from 'D:/app/miniprogram/SIYU/pages/address/edit.vue'\nwx.createPage(MiniProgramPage)"], "names": ["addressAPI", "uni"], "mappings": ";;;AA2FA,MAAO,eAAc,MAAW;AAEhC,MAAK,YAAU;AAAA,EACd,YAAY;AAAA,IACX;AAAA,EACA;AAAA,EACD,OAAO;AACN,WAAO;AAAA,MACN,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,YAAY;AAAA,MACZ,kBAAkB;AAAA,MAClB,MAAM;AAAA,QACL,MAAM;AAAA,QACN,OAAO;AAAA,QACP,UAAU;AAAA,QACV,MAAM;AAAA,QACN,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,YAAY;AAAA,MACb;AAAA,IACD;AAAA,EACA;AAAA,EACD,UAAU;AAAA,IACT,aAAa;AACZ,UAAI,KAAK,KAAK,YAAY,KAAK,KAAK,QAAQ,KAAK,KAAK,UAAU;AAC/D,eAAO,GAAG,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,QAAQ;AAAA,MACrE;AACA,aAAO;AAAA,IACP;AAAA,IACD,cAAc;AACb,aAAO;AAAA,QACN,UAAU,KAAK,KAAK;AAAA,QACpB,MAAM,KAAK,KAAK;AAAA,QAChB,UAAU,KAAK,KAAK;AAAA,MACrB;AAAA,IACA;AAAA,IACD,YAAY;AACX,aAAO,KAAK,KAAK,KAAK,KAAK,KACvB,KAAK,KAAK,MAAM,KAAK,KACrB,KAAK,KAAK,YACV,KAAK,KAAK,QACV,KAAK,KAAK,YACV,KAAK,KAAK,OAAO,KAAK;AAAA,IAC3B;AAAA,EACA;AAAA,EACD,OAAO,SAAS;AACf,QAAI,QAAQ,IAAI;AACf,WAAK,YAAY,SAAS,QAAQ,EAAE;AACpC,WAAK,SAAS;AACd,WAAK,YAAY;AAAA,IAClB;AAAA,EACA;AAAA,EACD,SAAS;AAAA;AAAA,IAER,MAAM,cAAc;AACnB,UAAI;AACH,cAAM,WAAW,MAAMA,UAAAA,WAAW,UAAU,KAAK,SAAS;AAC1D,cAAM,UAAU,SAAS;AAEzB,aAAK,OAAO;AAAA,UACX,MAAM,QAAQ;AAAA,UACd,OAAO,QAAQ;AAAA,UACf,UAAU,QAAQ;AAAA,UAClB,MAAM,QAAQ;AAAA,UACd,UAAU,QAAQ;AAAA,UAClB,QAAQ,QAAQ;AAAA,UAChB,YAAY,QAAQ;AAAA,QACrB;AAAA,MACC,SAAO,OAAO;AACfC,sBAAAA,MAAA,MAAA,SAAA,iCAAc,aAAa,KAAK;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,SACN;AACDA,sBAAAA,MAAI,aAAa;AAAA,MAClB;AAAA,IACA;AAAA;AAAA,IAGD,eAAe;AACd,WAAK,mBAAmB;AAAA,IACxB;AAAA;AAAA,IAGD,gBAAgB,QAAQ;AACvB,WAAK,KAAK,WAAW,OAAO;AAC5B,WAAK,KAAK,OAAO,OAAO;AACxB,WAAK,KAAK,WAAW,OAAO;AAAA,IAC5B;AAAA;AAAA,IAGD,gBAAgB,GAAG;AAClB,WAAK,KAAK,aAAa,EAAE,OAAO;AAAA,IAChC;AAAA;AAAA,IAGD,MAAM,aAAa;AAClB,UAAI,CAAC,KAAK,aAAa,KAAK;AAAY;AAGxC,UAAI,CAAC,gBAAgB,KAAK,KAAK,KAAK,KAAK,GAAG;AAC3CA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,SACN;AACD;AAAA,MACD;AAEA,WAAK,aAAa;AAElB,UAAI;AACH,cAAM,WAAW,EAAE,GAAG,KAAK,KAAK;AAEhC,YAAI,KAAK,QAAQ;AAChB,gBAAMD,UAAU,WAAC,OAAO,KAAK,WAAW,QAAQ;AAChDC,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,WACN;AAAA,eACK;AACN,gBAAMD,UAAU,WAAC,OAAO,QAAQ;AAChCC,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,WACN;AAAA,QACF;AAGA,mBAAW,MAAM;AAChBA,wBAAAA,MAAI,aAAa;AAAA,QACjB,GAAE,IAAI;AAAA,MAEN,SAAO,OAAO;AACfA,sBAAAA,MAAA,MAAA,SAAA,iCAAc,WAAW,KAAK;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO,MAAM,WAAW;AAAA,UACxB,MAAM;AAAA,SACN;AAAA,MACF,UAAU;AACT,aAAK,aAAa;AAAA,MACnB;AAAA,IACD;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC1OA,GAAG,WAAW,eAAe;"}