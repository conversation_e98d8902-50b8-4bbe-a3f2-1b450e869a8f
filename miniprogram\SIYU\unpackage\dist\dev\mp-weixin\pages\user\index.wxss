
.user-container.data-v-79e6a490 {
	background: #f8f8f8;
	min-height: 100vh;
}
.user-card.data-v-79e6a490 {
	margin-bottom: 15px;
}
.user-bg.data-v-79e6a490 {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 25px 20px;
	color: #ffffff;
}
.user-info.data-v-79e6a490 {
	display: flex;
	align-items: center;
	margin-bottom: 25px;
}
.avatar.data-v-79e6a490 {
	width: 60px;
	height: 60px;
	border-radius: 30px;
	margin-right: 15px;
	background: rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
}
.avatar-image.data-v-79e6a490 {
	width: 100%;
	height: 100%;
	border-radius: 30px;
}
.avatar-emoji.data-v-79e6a490 {
	font-size: 32px;
}
.action-arrow.data-v-79e6a490 {
	font-size: 20px;
	color: rgba(255, 255, 255, 0.8);
}
.user-details.data-v-79e6a490 {
	flex: 1;
}
.nickname.data-v-79e6a490 {
	display: block;
	font-size: 18px;
	font-weight: 500;
	margin-bottom: 5px;
}
.user-id.data-v-79e6a490 {
	display: block;
	font-size: 12px;
	opacity: 0.8;
}
.user-desc.data-v-79e6a490 {
	display: block;
	font-size: 12px;
	opacity: 0.8;
	color: rgba(255, 255, 255, 0.7);
}
.user-stats.data-v-79e6a490 {
	display: flex;
	justify-content: space-around;
}
.stat-item.data-v-79e6a490 {
	display: flex;
	flex-direction: column;
	align-items: center;
}
.stat-number.data-v-79e6a490 {
	font-size: 20px;
	font-weight: bold;
	margin-bottom: 5px;
}
.stat-label.data-v-79e6a490 {
	font-size: 12px;
	opacity: 0.8;
}
.section.data-v-79e6a490 {
	background: #ffffff;
	margin-bottom: 15px;
}
.section-header.data-v-79e6a490 {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 15px 20px;
	border-bottom: 1px solid #f5f5f5;
}
.section-title.data-v-79e6a490 {
	font-size: 16px;
	font-weight: 500;
	color: #333;
}
.section-more.data-v-79e6a490 {
	display: flex;
	align-items: center;
}
.more-text.data-v-79e6a490 {
	font-size: 12px;
	color: #999;
	margin-right: 5px;
}
.order-types.data-v-79e6a490 {
	display: flex;
	justify-content: space-around;
	padding: 20px;
}
.order-type.data-v-79e6a490 {
	display: flex;
	flex-direction: column;
	align-items: center;
	position: relative;
}
.type-text.data-v-79e6a490 {
	font-size: 12px;
	color: #333;
	margin-top: 8px;
}
.badge.data-v-79e6a490 {
	position: absolute;
	top: -5px;
	right: -10px;
	background: #f56c6c;
	color: #ffffff;
	font-size: 10px;
	padding: 2px 6px;
	border-radius: 10px;
	min-width: 16px;
	text-align: center;
}
.menu-list.data-v-79e6a490 {
	/* 菜单列表样式 */
}
.menu-item.data-v-79e6a490 {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 15px 20px;
	border-bottom: 1px solid #f5f5f5;
}
.menu-item.data-v-79e6a490:last-child {
	border-bottom: none;
}
.menu-left.data-v-79e6a490 {
	display: flex;
	align-items: center;
}
.menu-text.data-v-79e6a490 {
	font-size: 14px;
	color: #333;
	margin-left: 12px;
}
.logout-btn.data-v-79e6a490 {
	margin: 20px;
	background: #f56c6c;
	border-radius: 8px;
	padding: 15px;
	text-align: center;
}
.logout-text.data-v-79e6a490 {
	color: #ffffff;
	font-size: 16px;
	font-weight: 500;
}

/* 头像昵称编辑弹窗样式 */
.profile-modal-overlay.data-v-79e6a490 {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	z-index: 1000;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 40rpx;
}
.profile-modal.data-v-79e6a490 {
	background: white;
	border-radius: 20rpx;
	width: 100%;
	max-width: 600rpx;
	max-height: 80vh;
	overflow: hidden;
}
.modal-header.data-v-79e6a490 {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 40rpx 40rpx 20rpx;
	border-bottom: 1rpx solid #f0f0f0;
}
.modal-title.data-v-79e6a490 {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}
.modal-close.data-v-79e6a490 {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}
.modal-content.data-v-79e6a490 {
	padding: 40rpx;
}
.modal-desc.data-v-79e6a490 {
	font-size: 28rpx;
	color: #666;
	line-height: 1.6;
	margin-bottom: 40rpx;
}
.avatar-section.data-v-79e6a490,
.nickname-section.data-v-79e6a490 {
	margin-bottom: 40rpx;
}
.section-label.data-v-79e6a490 {
	display: block;
	font-size: 28rpx;
	color: #333;
	margin-bottom: 20rpx;
}
.avatar-button.data-v-79e6a490 {
	display: flex;
	align-items: center;
	justify-content: space-between;
	background: #f8f9fa;
	border: none;
	border-radius: 16rpx;
	padding: 20rpx;
	width: 100%;
}
.avatar-preview.data-v-79e6a490 {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	background: #ddd;
}
.nickname-input.data-v-79e6a490 {
	width: 100%;
	height: 80rpx;
	background: #f8f9fa;
	border: none;
	border-radius: 16rpx;
	padding: 0 30rpx;
	font-size: 28rpx;
	color: #333;
}
.modal-footer.data-v-79e6a490 {
	padding: 20rpx 40rpx 40rpx;
}
.save-button.data-v-79e6a490 {
	width: 100%;
	height: 80rpx;
	background: #007aff;
	color: white;
	border: none;
	border-radius: 40rpx;
	font-size: 32rpx;
	font-weight: 500;
}
