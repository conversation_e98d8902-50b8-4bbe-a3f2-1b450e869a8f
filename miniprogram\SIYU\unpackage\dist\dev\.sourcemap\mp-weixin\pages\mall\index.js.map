{"version": 3, "file": "index.js", "sources": ["pages/mall/index.vue", "../../../Program Files/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvbWFsbC9pbmRleC52dWU"], "sourcesContent": ["<template>\n\t<view class=\"mall-container\">\n\t\t<!-- 搜索栏 -->\n\t\t<view class=\"search-bar\">\n\t\t\t<view class=\"search-input\">\n\t\t\t\t<uni-icons type=\"search\" size=\"18\" color=\"#999\"></uni-icons>\n\t\t\t\t<input\n\t\t\t\t\ttype=\"text\"\n\t\t\t\t\tplaceholder=\"搜索商品\"\n\t\t\t\t\tv-model=\"searchKeyword\"\n\t\t\t\t\t@confirm=\"handleSearch\"\n\t\t\t\t/>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 分类导航 -->\n\t\t<scroll-view class=\"category-nav\" scroll-x=\"true\" show-scrollbar=\"false\">\n\t\t\t<view class=\"category-list\">\n\t\t\t\t<view\n\t\t\t\t\tclass=\"category-item\"\n\t\t\t\t\t:class=\"{ active: currentCategory === item.id }\"\n\t\t\t\t\tv-for=\"item in categories\"\n\t\t\t\t\t:key=\"item.id\"\n\t\t\t\t\t@click=\"selectCategory(item.id)\"\n\t\t\t\t>\n\t\t\t\t\t<text class=\"category-name\">{{ item.name }}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</scroll-view>\n\n\t\t<!-- 商品列表 -->\n\t\t<view class=\"product-list\">\n\t\t\t<view\n\t\t\t\tclass=\"product-item\"\n\t\t\t\tv-for=\"item in products\"\n\t\t\t\t:key=\"item.id\"\n\t\t\t\t@click=\"goToDetail(item.id)\"\n\t\t\t>\n\t\t\t\t<view class=\"product-image\">\n\t\t\t\t\t<!-- 如果有真实图片，显示图片；否则显示emoji -->\n\t\t\t\t\t<image v-if=\"item.image\"\n\t\t\t\t\t\t   :src=\"item.image\"\n\t\t\t\t\t\t   class=\"product-img\"\n\t\t\t\t\t\t   mode=\"aspectFill\" />\n\t\t\t\t\t<text v-else class=\"product-emoji\">{{ item.emoji }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"product-info\">\n\t\t\t\t\t<text class=\"product-name\">{{ item.name }}</text>\n\t\t\t\t\t<text class=\"product-desc\">{{ item.description }}</text>\n\t\t\t\t\t<view class=\"product-price\">\n\t\t\t\t\t\t<text class=\"points-price\">{{ item.points_price }}积分</text>\n\t\t\t\t\t\t<text class=\"market-price\" v-if=\"item.market_price\">¥{{ item.market_price }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 加载更多 -->\n\t\t<view class=\"load-more\" v-if=\"hasMore\">\n\t\t\t<uni-load-more :status=\"loadStatus\"></uni-load-more>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nimport { productAPI } from '@/api/index.js'\nimport { getImageUrl } from '@/utils/request.js'\n\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tsearchKeyword: '',\n\t\t\tcurrentCategory: 0,\n\t\t\tcategories: [\n\t\t\t\t{ id: 0, name: '全部' }\n\t\t\t],\n\t\t\tproducts: [],\n\t\t\tpage: 1,\n\t\t\tpageSize: 10,\n\t\t\thasMore: true,\n\t\t\tloadStatus: 'more'\n\t\t}\n\t},\n\tonLoad() {\n\t\tthis.loadCategories();\n\t\tthis.loadProducts();\n\t},\n\tonReachBottom() {\n\t\tif (this.hasMore) {\n\t\t\tthis.loadMore();\n\t\t}\n\t},\n\tonPullDownRefresh() {\n\t\tthis.refreshProducts();\n\t},\n\tmethods: {\n\t\t// 加载商品分类\n\t\tasync loadCategories() {\n\t\t\ttry {\n\t\t\t\tconst response = await productAPI.getCategories();\n\t\t\t\tconst categories = response.data || [];\n\n\t\t\t\t// 添加\"全部\"选项\n\t\t\t\tthis.categories = [\n\t\t\t\t\t{ id: 0, name: '全部' },\n\t\t\t\t\t...categories\n\t\t\t\t];\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('获取商品分类失败:', error);\n\t\t\t\t// 使用默认分类作为降级方案\n\t\t\t\tthis.categories = [\n\t\t\t\t\t{ id: 0, name: '全部' },\n\t\t\t\t\t{ id: 1, name: '数码产品' },\n\t\t\t\t\t{ id: 2, name: '生活用品' },\n\t\t\t\t\t{ id: 3, name: '办公用品' },\n\t\t\t\t\t{ id: 4, name: '服装配饰' }\n\t\t\t\t];\n\t\t\t}\n\t\t},\n\n\t\t// 搜索商品\n\t\thandleSearch() {\n\t\t\tthis.page = 1;\n\t\t\tthis.products = [];\n\t\t\tthis.loadProducts();\n\t\t},\n\n\t\t// 选择分类\n\t\tselectCategory(categoryId) {\n\t\t\tthis.currentCategory = categoryId;\n\t\t\tthis.page = 1;\n\t\t\tthis.products = [];\n\t\t\tthis.loadProducts();\n\t\t},\n\n\t\t// 加载商品列表\n\t\tasync loadProducts() {\n\t\t\tthis.loadStatus = 'loading';\n\n\t\t\ttry {\n\t\t\t\tconst params = {\n\t\t\t\t\tpage: this.page,\n\t\t\t\t\tlimit: this.pageSize\n\t\t\t\t}\n\n\t\t\t\t// 只有当值存在时才添加参数\n\t\t\t\tif (this.currentCategory) {\n\t\t\t\t\tparams.category_id = this.currentCategory\n\t\t\t\t}\n\t\t\t\tif (this.searchKeyword) {\n\t\t\t\t\tparams.keyword = this.searchKeyword\n\t\t\t\t}\n\n\t\t\t\tconst response = await productAPI.getList(params)\n\n\t\t\t\tconst productList = response.data.list || []\n\n\t\t\t\t// 处理商品图片URL\n\t\t\t\tproductList.forEach(product => {\n\t\t\t\t\tif (product.image) {\n\t\t\t\t\t\tproduct.image = getImageUrl(product.image)\n\t\t\t\t\t}\n\t\t\t\t\tif (product.images && Array.isArray(product.images)) {\n\t\t\t\t\t\tproduct.images = product.images.map(img => getImageUrl(img))\n\t\t\t\t\t}\n\t\t\t\t})\n\n\t\t\t\tif (this.page === 1) {\n\t\t\t\t\tthis.products = productList;\n\t\t\t\t} else {\n\t\t\t\t\tthis.products = [...this.products, ...productList];\n\t\t\t\t}\n\n\t\t\t\tthis.hasMore = productList.length === this.pageSize;\n\t\t\t\tthis.loadStatus = this.hasMore ? 'more' : 'noMore';\n\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('获取商品列表失败:', error)\n\t\t\t\t// 使用模拟数据作为降级方案\n\t\t\t\tconst mockProducts = this.generateMockProducts();\n\n\t\t\t\tif (this.page === 1) {\n\t\t\t\t\tthis.products = mockProducts;\n\t\t\t\t} else {\n\t\t\t\t\tthis.products = [...this.products, ...mockProducts];\n\t\t\t\t}\n\n\t\t\t\tthis.hasMore = mockProducts.length === this.pageSize;\n\t\t\t\tthis.loadStatus = this.hasMore ? 'more' : 'noMore';\n\t\t\t}\n\n\t\t\tuni.stopPullDownRefresh();\n\t\t},\n\n\t\t// 加载更多\n\t\tloadMore() {\n\t\t\tthis.page++;\n\t\t\tthis.loadProducts();\n\t\t},\n\n\t\t// 刷新商品\n\t\trefreshProducts() {\n\t\t\tthis.page = 1;\n\t\t\tthis.products = [];\n\t\t\tthis.loadProducts();\n\t\t},\n\n\t\t// 生成模拟商品数据\n\t\tgenerateMockProducts() {\n\t\t\tconst products = [];\n\t\t\tconst emojis = ['☕', '🎧', '🔋', '📱', '🖱️', '⌨️', '💻', '📺', '🎮', '📷', '⌚', '🎒', '👕', '👟', '🧸', '📚'];\n\t\t\tconst names = ['精美水杯', '蓝牙耳机', '充电宝', '手机支架', '无线鼠标', '键盘膜', '笔记本电脑', '智能电视', '游戏手柄', '数码相机', '智能手表', '时尚背包', '舒适T恤', '运动鞋', '毛绒玩具', '精装图书'];\n\n\t\t\tfor (let i = 0; i < this.pageSize; i++) {\n\t\t\t\tconst id = (this.page - 1) * this.pageSize + i + 1;\n\t\t\t\tconst index = (id - 1) % emojis.length;\n\t\t\t\tproducts.push({\n\t\t\t\t\tid: id,\n\t\t\t\t\tname: names[index] || `商品${id}`,\n\t\t\t\t\tdescription: '这是一个很棒的商品，值得拥有',\n\t\t\t\t\temoji: emojis[index],\n\t\t\t\t\tpoints_price: Math.floor(Math.random() * 1000) + 100,\n\t\t\t\t\tmarket_price: (Math.random() * 100 + 10).toFixed(2),\n\t\t\t\t\tcategory_id: this.currentCategory || Math.floor(Math.random() * 4) + 1\n\t\t\t\t});\n\t\t\t}\n\t\t\treturn products;\n\t\t},\n\n\t\t// 跳转到商品详情\n\t\tgoToDetail(productId) {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: `/pages/mall/detail?id=${productId}`\n\t\t\t});\n\t\t}\n\t}\n}\n</script>\n\n<style scoped>\n.mall-container {\n\tbackground: #f8f8f8;\n\tmin-height: 100vh;\n}\n\n.search-bar {\n\tbackground: #ffffff;\n\tpadding: 10px 15px;\n\tborder-bottom: 1px solid #f0f0f0;\n}\n\n.search-input {\n\tdisplay: flex;\n\talign-items: center;\n\tbackground: #f5f5f5;\n\tborder-radius: 20px;\n\tpadding: 8px 15px;\n}\n\n.search-input input {\n\tflex: 1;\n\tmargin-left: 10px;\n\tfont-size: 14px;\n\tborder: none;\n\tbackground: transparent;\n}\n\n.category-nav {\n\tbackground: #ffffff;\n\twhite-space: nowrap;\n\tborder-bottom: 1px solid #f0f0f0;\n}\n\n.category-list {\n\tdisplay: flex;\n\tpadding: 0 15px;\n}\n\n.category-item {\n\tpadding: 15px 20px;\n\twhite-space: nowrap;\n}\n\n.category-item.active .category-name {\n\tcolor: #667eea;\n\tfont-weight: 500;\n}\n\n.category-name {\n\tfont-size: 14px;\n\tcolor: #333;\n}\n\n.product-list {\n\tpadding: 15px;\n}\n\n.product-item {\n\tbackground: #ffffff;\n\tborder-radius: 8px;\n\tmargin-bottom: 15px;\n\toverflow: hidden;\n\tbox-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.product-image {\n\twidth: 100%;\n\theight: 200px;\n\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.product-emoji {\n\tfont-size: 64px;\n}\n\n.product-img {\n\twidth: 100%;\n\theight: 100%;\n\tborder-radius: 8px;\n}\n\n.product-info {\n\tpadding: 15px;\n}\n\n.product-name {\n\tdisplay: block;\n\tfont-size: 16px;\n\tfont-weight: 500;\n\tcolor: #333;\n\tmargin-bottom: 5px;\n}\n\n.product-desc {\n\tdisplay: block;\n\tfont-size: 12px;\n\tcolor: #999;\n\tmargin-bottom: 10px;\n}\n\n.product-price {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n}\n\n.points-price {\n\tfont-size: 18px;\n\tfont-weight: bold;\n\tcolor: #667eea;\n}\n\n.market-price {\n\tfont-size: 12px;\n\tcolor: #999;\n\ttext-decoration: line-through;\n}\n\n.load-more {\n\tpadding: 20px;\n}\n</style>\n", "import MiniProgramPage from 'D:/app/miniprogram/SIYU/pages/mall/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["productAPI", "uni", "getImageUrl"], "mappings": ";;;;AAoEA,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,eAAe;AAAA,MACf,iBAAiB;AAAA,MACjB,YAAY;AAAA,QACX,EAAE,IAAI,GAAG,MAAM,KAAK;AAAA,MACpB;AAAA,MACD,UAAU,CAAE;AAAA,MACZ,MAAM;AAAA,MACN,UAAU;AAAA,MACV,SAAS;AAAA,MACT,YAAY;AAAA,IACb;AAAA,EACA;AAAA,EACD,SAAS;AACR,SAAK,eAAc;AACnB,SAAK,aAAY;AAAA,EACjB;AAAA,EACD,gBAAgB;AACf,QAAI,KAAK,SAAS;AACjB,WAAK,SAAQ;AAAA,IACd;AAAA,EACA;AAAA,EACD,oBAAoB;AACnB,SAAK,gBAAe;AAAA,EACpB;AAAA,EACD,SAAS;AAAA;AAAA,IAER,MAAM,iBAAiB;AACtB,UAAI;AACH,cAAM,WAAW,MAAMA,qBAAW;AAClC,cAAM,aAAa,SAAS,QAAQ;AAGpC,aAAK,aAAa;AAAA,UACjB,EAAE,IAAI,GAAG,MAAM,KAAM;AAAA,UACrB,GAAG;AAAA;MAEH,SAAO,OAAO;AACfC,sBAAA,MAAA,MAAA,SAAA,+BAAc,aAAa,KAAK;AAEhC,aAAK,aAAa;AAAA,UACjB,EAAE,IAAI,GAAG,MAAM,KAAM;AAAA,UACrB,EAAE,IAAI,GAAG,MAAM,OAAQ;AAAA,UACvB,EAAE,IAAI,GAAG,MAAM,OAAQ;AAAA,UACvB,EAAE,IAAI,GAAG,MAAM,OAAQ;AAAA,UACvB,EAAE,IAAI,GAAG,MAAM,OAAO;AAAA;MAExB;AAAA,IACA;AAAA;AAAA,IAGD,eAAe;AACd,WAAK,OAAO;AACZ,WAAK,WAAW;AAChB,WAAK,aAAY;AAAA,IACjB;AAAA;AAAA,IAGD,eAAe,YAAY;AAC1B,WAAK,kBAAkB;AACvB,WAAK,OAAO;AACZ,WAAK,WAAW;AAChB,WAAK,aAAY;AAAA,IACjB;AAAA;AAAA,IAGD,MAAM,eAAe;AACpB,WAAK,aAAa;AAElB,UAAI;AACH,cAAM,SAAS;AAAA,UACd,MAAM,KAAK;AAAA,UACX,OAAO,KAAK;AAAA,QACb;AAGA,YAAI,KAAK,iBAAiB;AACzB,iBAAO,cAAc,KAAK;AAAA,QAC3B;AACA,YAAI,KAAK,eAAe;AACvB,iBAAO,UAAU,KAAK;AAAA,QACvB;AAEA,cAAM,WAAW,MAAMD,qBAAW,QAAQ,MAAM;AAEhD,cAAM,cAAc,SAAS,KAAK,QAAQ,CAAC;AAG3C,oBAAY,QAAQ,aAAW;AAC9B,cAAI,QAAQ,OAAO;AAClB,oBAAQ,QAAQE,0BAAY,QAAQ,KAAK;AAAA,UAC1C;AACA,cAAI,QAAQ,UAAU,MAAM,QAAQ,QAAQ,MAAM,GAAG;AACpD,oBAAQ,SAAS,QAAQ,OAAO,IAAI,SAAOA,cAAAA,YAAY,GAAG,CAAC;AAAA,UAC5D;AAAA,SACA;AAED,YAAI,KAAK,SAAS,GAAG;AACpB,eAAK,WAAW;AAAA,eACV;AACN,eAAK,WAAW,CAAC,GAAG,KAAK,UAAU,GAAG,WAAW;AAAA,QAClD;AAEA,aAAK,UAAU,YAAY,WAAW,KAAK;AAC3C,aAAK,aAAa,KAAK,UAAU,SAAS;AAAA,MAEzC,SAAO,OAAO;AACfD,sBAAAA,MAAA,MAAA,SAAA,+BAAc,aAAa,KAAK;AAEhC,cAAM,eAAe,KAAK;AAE1B,YAAI,KAAK,SAAS,GAAG;AACpB,eAAK,WAAW;AAAA,eACV;AACN,eAAK,WAAW,CAAC,GAAG,KAAK,UAAU,GAAG,YAAY;AAAA,QACnD;AAEA,aAAK,UAAU,aAAa,WAAW,KAAK;AAC5C,aAAK,aAAa,KAAK,UAAU,SAAS;AAAA,MAC3C;AAEAA,oBAAG,MAAC,oBAAmB;AAAA,IACvB;AAAA;AAAA,IAGD,WAAW;AACV,WAAK;AACL,WAAK,aAAY;AAAA,IACjB;AAAA;AAAA,IAGD,kBAAkB;AACjB,WAAK,OAAO;AACZ,WAAK,WAAW;AAChB,WAAK,aAAY;AAAA,IACjB;AAAA;AAAA,IAGD,uBAAuB;AACtB,YAAM,WAAW,CAAA;AACjB,YAAM,SAAS,CAAC,KAAK,MAAM,MAAM,MAAM,OAAO,MAAM,MAAM,MAAM,MAAM,MAAM,KAAK,MAAM,MAAM,MAAM,MAAM,IAAI;AAC7G,YAAM,QAAQ,CAAC,QAAQ,QAAQ,OAAO,QAAQ,QAAQ,OAAO,SAAS,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,OAAO,QAAQ,MAAM;AAE3I,eAAS,IAAI,GAAG,IAAI,KAAK,UAAU,KAAK;AACvC,cAAM,MAAM,KAAK,OAAO,KAAK,KAAK,WAAW,IAAI;AACjD,cAAM,SAAS,KAAK,KAAK,OAAO;AAChC,iBAAS,KAAK;AAAA,UACb;AAAA,UACA,MAAM,MAAM,KAAK,KAAK,KAAK,EAAE;AAAA,UAC7B,aAAa;AAAA,UACb,OAAO,OAAO,KAAK;AAAA,UACnB,cAAc,KAAK,MAAM,KAAK,OAAS,IAAE,GAAI,IAAI;AAAA,UACjD,eAAe,KAAK,OAAM,IAAK,MAAM,IAAI,QAAQ,CAAC;AAAA,UAClD,aAAa,KAAK,mBAAmB,KAAK,MAAM,KAAK,OAAM,IAAK,CAAC,IAAI;AAAA,QACtE,CAAC;AAAA,MACF;AACA,aAAO;AAAA,IACP;AAAA;AAAA,IAGD,WAAW,WAAW;AACrBA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK,yBAAyB,SAAS;AAAA,MACxC,CAAC;AAAA,IACF;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3OA,GAAG,WAAW,eAAe;"}