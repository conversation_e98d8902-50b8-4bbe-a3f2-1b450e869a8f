"use strict";
const common_vendor = require("../../common/vendor.js");
const api_index = require("../../api/index.js");
const _sfc_main = {
  data() {
    return {
      currentStatus: "",
      statusTabs: [
        { label: "全部", value: "" },
        { label: "待付款", value: "pending" },
        { label: "处理中", value: "processing" },
        { label: "已发货", value: "shipped" },
        { label: "已完成", value: "completed" }
      ],
      orderList: []
    };
  },
  onLoad(options) {
    if (options.status) {
      this.currentStatus = options.status;
    }
    this.loadOrders();
  },
  methods: {
    // 切换状态
    switchStatus(status) {
      this.currentStatus = status;
      this.loadOrders();
    },
    // 加载订单列表
    async loadOrders() {
      try {
        const token = common_vendor.index.getStorageSync("token");
        if (!token) {
          common_vendor.index.showToast({
            title: "请先登录",
            icon: "none"
          });
          common_vendor.index.navigateTo({
            url: "/pages/login/login"
          });
          return;
        }
        const response = await api_index.orderAPI.getList({
          status: this.currentStatus,
          page: 1,
          limit: 20
        });
        this.orderList = response.data.list || [];
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/user/orders.vue:114", "获取订单列表失败:", error);
        this.orderList = this.generateMockOrders();
      }
    },
    // 生成模拟订单数据
    generateMockOrders() {
      const orders = [];
      const statuses = ["pending", "processing", "shipped", "completed"];
      const emojis = ["☕", "🎧", "🔋", "📱", "🖱️", "⌨️"];
      const names = ["精美水杯", "蓝牙耳机", "充电宝", "手机支架", "无线鼠标", "键盘膜"];
      for (let i = 1; i <= 10; i++) {
        const status = this.currentStatus || statuses[Math.floor(Math.random() * statuses.length)];
        const productCount = Math.floor(Math.random() * 3) + 1;
        const products = [];
        let totalPoints = 0;
        let totalQuantity = 0;
        for (let j = 0; j < productCount; j++) {
          const index = Math.floor(Math.random() * emojis.length);
          const quantity = Math.floor(Math.random() * 2) + 1;
          const points = Math.floor(Math.random() * 500) + 100;
          products.push({
            id: j + 1,
            name: names[index],
            emoji: emojis[index],
            spec: "默认规格",
            quantity,
            points_price: points
          });
          totalPoints += points * quantity;
          totalQuantity += quantity;
        }
        orders.push({
          id: i,
          order_no: "ORD" + Date.now() + i,
          status,
          products,
          total_points: totalPoints,
          total_quantity: totalQuantity,
          created_at: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1e3).toISOString()
        });
      }
      return orders.filter((order) => !this.currentStatus || order.status === this.currentStatus);
    },
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        "pending": "待付款",
        "processing": "处理中",
        "shipped": "已发货",
        "completed": "已完成",
        "cancelled": "已取消"
      };
      return statusMap[status] || "未知状态";
    },
    // 跳转到订单详情
    goToOrderDetail(orderId) {
      common_vendor.index.navigateTo({
        url: `/pages/user/order-detail?id=${orderId}`
      });
    },
    // 取消订单
    async cancelOrder(orderId) {
      try {
        await common_vendor.index.showModal({
          title: "确认取消",
          content: "确定要取消这个订单吗？",
          confirmText: "确认",
          cancelText: "取消"
        });
        await api_index.orderAPI.cancel(orderId);
        common_vendor.index.showToast({
          title: "订单已取消",
          icon: "success"
        });
        this.loadOrders();
      } catch (error) {
        if (error.errMsg && error.errMsg.includes("cancel")) {
          return;
        }
        common_vendor.index.__f__("error", "at pages/user/orders.vue:209", "取消订单失败:", error);
        common_vendor.index.showToast({
          title: "取消失败",
          icon: "none"
        });
      }
    },
    // 确认收货
    async confirmOrder(orderId) {
      try {
        await common_vendor.index.showModal({
          title: "确认收货",
          content: "确认已收到商品吗？",
          confirmText: "确认",
          cancelText: "取消"
        });
        await api_index.orderAPI.confirm(orderId);
        common_vendor.index.showToast({
          title: "确认收货成功",
          icon: "success"
        });
        this.loadOrders();
      } catch (error) {
        if (error.errMsg && error.errMsg.includes("cancel")) {
          return;
        }
        common_vendor.index.__f__("error", "at pages/user/orders.vue:241", "确认收货失败:", error);
        common_vendor.index.showToast({
          title: "确认失败",
          icon: "none"
        });
      }
    },
    // 评价订单
    reviewOrder(orderId) {
      common_vendor.index.showToast({
        title: "评价功能开发中",
        icon: "none"
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.f($data.statusTabs, (item, k0, i0) => {
      return {
        a: common_vendor.t(item.label),
        b: $data.currentStatus === item.value ? 1 : "",
        c: item.value,
        d: common_vendor.o(($event) => $options.switchStatus(item.value), item.value)
      };
    }),
    b: common_vendor.f($data.orderList, (order, k0, i0) => {
      return common_vendor.e({
        a: common_vendor.t(order.order_no),
        b: common_vendor.t($options.getStatusText(order.status)),
        c: common_vendor.n("status-" + order.status),
        d: common_vendor.f(order.products, (product, k1, i1) => {
          return {
            a: common_vendor.t(product.emoji),
            b: common_vendor.t(product.name),
            c: common_vendor.t(product.spec || "默认规格"),
            d: common_vendor.t(product.quantity),
            e: common_vendor.t(product.points_price),
            f: product.id
          };
        }),
        e: common_vendor.t(order.total_quantity),
        f: common_vendor.t(order.total_points),
        g: order.status === "pending"
      }, order.status === "pending" ? {
        h: common_vendor.o(($event) => $options.cancelOrder(order.id), order.id)
      } : {}, {
        i: order.status === "shipped"
      }, order.status === "shipped" ? {
        j: common_vendor.o(($event) => $options.confirmOrder(order.id), order.id)
      } : {}, {
        k: order.status === "completed"
      }, order.status === "completed" ? {
        l: common_vendor.o(($event) => $options.reviewOrder(order.id), order.id)
      } : {}, {
        m: order.id,
        n: common_vendor.o(($event) => $options.goToOrderDetail(order.id), order.id)
      });
    }),
    c: $data.orderList.length === 0
  }, $data.orderList.length === 0 ? {} : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-efd435e4"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/user/orders.js.map
