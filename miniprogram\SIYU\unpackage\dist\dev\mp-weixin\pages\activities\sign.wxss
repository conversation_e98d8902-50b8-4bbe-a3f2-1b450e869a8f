
.sign-container.data-v-c8158183 {
	background: #f8f8f8;
	min-height: 100vh;
	padding: 15px;
}
.sign-card.data-v-c8158183 {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 12px;
	padding: 20px;
	margin-bottom: 15px;
	color: #ffffff;
}
.card-header.data-v-c8158183 {
	text-align: center;
	margin-bottom: 20px;
}
.title.data-v-c8158183 {
	display: block;
	font-size: 20px;
	font-weight: bold;
	margin-bottom: 5px;
}
.subtitle.data-v-c8158183 {
	display: block;
	font-size: 12px;
	opacity: 0.8;
}
.sign-calendar.data-v-c8158183 {
	background: rgba(255, 255, 255, 0.1);
	border-radius: 8px;
	padding: 15px;
	margin-bottom: 20px;
}
.calendar-header.data-v-c8158183 {
	text-align: center;
	margin-bottom: 15px;
}
.month-text.data-v-c8158183 {
	font-size: 16px;
	font-weight: 500;
}
.week-header.data-v-c8158183 {
	display: grid;
	grid-template-columns: repeat(7, 1fr);
	gap: 5px;
	margin-bottom: 10px;
}
.week-day.data-v-c8158183 {
	text-align: center;
	font-size: 12px;
	opacity: 0.8;
}
.calendar-days.data-v-c8158183 {
	display: grid;
	grid-template-columns: repeat(7, 1fr);
	gap: 5px;
}
.day-item.data-v-c8158183 {
	aspect-ratio: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	border-radius: 4px;
	position: relative;
}
.day-item.signed.data-v-c8158183 {
	background: rgba(103, 194, 58, 0.3);
}
.day-item.today.data-v-c8158183 {
	background: rgba(255, 255, 255, 0.2);
	border: 1px solid rgba(255, 255, 255, 0.5);
}
.day-item.future.data-v-c8158183 {
	opacity: 0.5;
}
.day-number.data-v-c8158183 {
	font-size: 12px;
}
.sign-status.data-v-c8158183 {
	position: absolute;
	bottom: 2px;
	font-size: 8px;
}
.sign-action.data-v-c8158183 {
	text-align: center;
}
.sign-btn.data-v-c8158183 {
	width: 200px;
	height: 44px;
	background: #ffffff;
	border-radius: 22px;
	border: none;
	margin-bottom: 15px;
}
.sign-btn.signed.data-v-c8158183 {
	background: rgba(255, 255, 255, 0.3);
}
.btn-text.data-v-c8158183 {
	font-size: 16px;
	font-weight: 500;
	color: #667eea;
}
.sign-btn.signed .btn-text.data-v-c8158183 {
	color: rgba(255, 255, 255, 0.8);
}
.sign-info.data-v-c8158183 {
	display: flex;
	justify-content: space-around;
}
.info-text.data-v-c8158183 {
	font-size: 12px;
	opacity: 0.8;
}
.reward-rules.data-v-c8158183,
.sign-records.data-v-c8158183 {
	background: #ffffff;
	border-radius: 12px;
	padding: 20px;
	margin-bottom: 15px;
}
.section-title.data-v-c8158183 {
	margin-bottom: 15px;
}
.title-text.data-v-c8158183 {
	font-size: 16px;
	font-weight: 500;
	color: #333;
}
.rules-list.data-v-c8158183,
.records-list.data-v-c8158183 {
	/* 列表样式 */
}
.rule-item.data-v-c8158183,
.record-item.data-v-c8158183 {
	display: flex;
	align-items: center;
	padding: 12px 0;
	border-bottom: 1px solid #f5f5f5;
}
.rule-item.data-v-c8158183:last-child,
.record-item.data-v-c8158183:last-child {
	border-bottom: none;
}
.rule-icon.data-v-c8158183 {
	width: 40px;
	height: 40px;
	background: rgba(102, 126, 234, 0.1);
	border-radius: 20px;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 15px;
}
.day-text.data-v-c8158183 {
	font-size: 12px;
	color: #667eea;
	font-weight: 500;
}
.rule-info.data-v-c8158183,
.record-info.data-v-c8158183 {
	flex: 1;
}
.rule-title.data-v-c8158183,
.record-title.data-v-c8158183 {
	display: block;
	font-size: 14px;
	color: #333;
	margin-bottom: 4px;
}
.rule-desc.data-v-c8158183,
.record-desc.data-v-c8158183 {
	display: block;
	font-size: 12px;
	color: #999;
}
.rule-reward.data-v-c8158183,
.record-points.data-v-c8158183 {
	font-size: 14px;
	font-weight: 500;
	color: #67c23a;
}
.record-date.data-v-c8158183 {
	width: 60px;
	margin-right: 15px;
}
.date-text.data-v-c8158183 {
	font-size: 12px;
	color: #999;
}
