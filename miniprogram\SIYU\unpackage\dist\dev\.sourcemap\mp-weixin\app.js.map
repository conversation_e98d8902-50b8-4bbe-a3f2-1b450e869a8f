{"version": 3, "file": "app.js", "sources": ["App.vue", "main.js"], "sourcesContent": ["<script>\r\n\timport { authAPI } from '@/api/index.js'\r\n\r\n\texport default {\r\n\t\tonLaunch: function() {\r\n\t\t\tconsole.log('App Launch')\r\n\t\t\t// 检查登录状态\r\n\t\t\tthis.checkLoginStatus()\r\n\t\t},\r\n\t\tonShow: function() {\r\n\t\t\tconsole.log('App Show')\r\n\t\t},\r\n\t\tonHide: function() {\r\n\t\t\tconsole.log('App Hide')\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 检查登录状态\r\n\t\t\tasync checkLoginStatus() {\r\n\t\t\t\tconst token = uni.getStorageSync('token')\r\n\t\t\t\tconst userInfo = uni.getStorageSync('userInfo')\r\n\r\n\t\t\t\tif (!token || !userInfo) {\r\n\t\t\t\t\t// 未登录，但不强制跳转到登录页\r\n\t\t\t\t\t// 用户可以浏览商城，需要登录时再提示\r\n\t\t\t\t\tconsole.log('用户未登录，可以浏览但功能受限')\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\r\n\t\t\t\ttry {\r\n\t\t\t\t\t// 验证token是否有效\r\n\t\t\t\t\tconst result = await authAPI.verifyToken(token)\r\n\t\t\t\t\tif (result.data && result.data.valid) {\r\n\t\t\t\t\t\tconsole.log('用户已登录:', userInfo.nickname || userInfo.nickName)\r\n\t\t\t\t\t\t// 更新用户信息\r\n\t\t\t\t\t\tif (result.data.user) {\r\n\t\t\t\t\t\t\tuni.setStorageSync('userInfo', result.data.user)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tconsole.log('token无效，清除本地数据')\r\n\t\t\t\t\t\tthis.clearLoginData()\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('验证token失败:', error)\r\n\t\t\t\t\t// 验证失败时不清除数据，可能是网络问题\r\n\t\t\t\t\tconsole.log('网络异常，保持本地登录状态')\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 清除登录数据\r\n\t\t\tclearLoginData() {\r\n\t\t\t\tuni.removeStorageSync('token')\r\n\t\t\t\tuni.removeStorageSync('userInfo')\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t/* 全局样式 */\r\n\tpage {\r\n\t\tfont-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;\r\n\t\tfont-size: 14px;\r\n\t\tcolor: #303133;\r\n\t\tbackground-color: #f8f8f8;\r\n\t}\r\n</style>\r\n", "import App from './App'\n\n// #ifndef VUE3\nimport Vue from 'vue'\nimport './uni.promisify.adaptor'\nVue.config.productionTip = false\nApp.mpType = 'app'\nconst app = new Vue({\n  ...App\n})\napp.$mount()\n// #endif\n\n// #ifdef VUE3\nimport { createSSRApp } from 'vue'\nexport function createApp() {\n  const app = createSSRApp(App)\n  return {\n    app\n  }\n}\n// #endif"], "names": ["uni", "authAPI", "createSSRApp", "App"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAGC,MAAK,YAAU;AAAA,EACd,UAAU,WAAW;AACpBA,kBAAAA,MAAA,MAAA,OAAA,gBAAY,YAAY;AAExB,SAAK,iBAAiB;AAAA,EACtB;AAAA,EACD,QAAQ,WAAW;AAClBA,kBAAAA,MAAY,MAAA,OAAA,iBAAA,UAAU;AAAA,EACtB;AAAA,EACD,QAAQ,WAAW;AAClBA,kBAAAA,MAAY,MAAA,OAAA,iBAAA,UAAU;AAAA,EACtB;AAAA,EACD,SAAS;AAAA;AAAA,IAER,MAAM,mBAAmB;AACxB,YAAM,QAAQA,cAAAA,MAAI,eAAe,OAAO;AACxC,YAAM,WAAWA,cAAAA,MAAI,eAAe,UAAU;AAE9C,UAAI,CAAC,SAAS,CAAC,UAAU;AAGxBA,sBAAAA,oCAAY,iBAAiB;AAC7B;AAAA,MACD;AAEA,UAAI;AAEH,cAAM,SAAS,MAAMC,kBAAQ,YAAY,KAAK;AAC9C,YAAI,OAAO,QAAQ,OAAO,KAAK,OAAO;AACrCD,4DAAY,UAAU,SAAS,YAAY,SAAS,QAAQ;AAE5D,cAAI,OAAO,KAAK,MAAM;AACrBA,0BAAAA,MAAI,eAAe,YAAY,OAAO,KAAK,IAAI;AAAA,UAChD;AAAA,eACM;AACNA,wBAAAA,MAAY,MAAA,OAAA,iBAAA,gBAAgB;AAC5B,eAAK,eAAe;AAAA,QACrB;AAAA,MACC,SAAO,OAAO;AACfA,sBAAAA,MAAA,MAAA,SAAA,iBAAc,cAAc,KAAK;AAEjCA,sBAAAA,oCAAY,eAAe;AAAA,MAC5B;AAAA,IACA;AAAA;AAAA,IAGD,iBAAiB;AAChBA,oBAAG,MAAC,kBAAkB,OAAO;AAC7BA,oBAAG,MAAC,kBAAkB,UAAU;AAAA,IACjC;AAAA,EACD;AACD;ACvCM,SAAS,YAAY;AAC1B,QAAM,MAAME,cAAY,aAACC,SAAG;AAC5B,SAAO;AAAA,IACL;AAAA,EACD;AACH;;;"}