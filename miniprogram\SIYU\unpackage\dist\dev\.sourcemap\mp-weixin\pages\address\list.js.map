{"version": 3, "file": "list.js", "sources": ["pages/address/list.vue", "../../../Program Files/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvYWRkcmVzcy9saXN0LnZ1ZQ"], "sourcesContent": ["<template>\n\t<view class=\"address-list-container\">\n\t\t<!-- 地址列表 -->\n\t\t<view class=\"address-list\" v-if=\"addresses.length > 0\">\n\t\t\t<view \n\t\t\t\tclass=\"address-item\" \n\t\t\t\tv-for=\"address in addresses\" \n\t\t\t\t:key=\"address.id\"\n\t\t\t\t@click=\"selectAddress(address)\"\n\t\t\t>\n\t\t\t\t<view class=\"address-content\">\n\t\t\t\t\t<view class=\"address-header\">\n\t\t\t\t\t\t<view class=\"name-phone\">\n\t\t\t\t\t\t\t<text class=\"name\">{{ address.name }}</text>\n\t\t\t\t\t\t\t<text class=\"phone\">{{ address.phone }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"default-badge\" v-if=\"address.is_default\">\n\t\t\t\t\t\t\t<text>默认</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<text class=\"address-detail\">{{ address.full_address }}</text>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"address-actions\" @click.stop=\"\">\n\t\t\t\t\t<view class=\"action-btn\" @click=\"editAddress(address)\">\n\t\t\t\t\t\t<text class=\"iconfont icon-edit\"></text>\n\t\t\t\t\t\t<text>编辑</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"action-btn\" @click=\"deleteAddress(address)\" v-if=\"!address.is_default\">\n\t\t\t\t\t\t<text class=\"iconfont icon-delete\"></text>\n\t\t\t\t\t\t<text>删除</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"action-btn\" @click=\"setDefault(address)\" v-if=\"!address.is_default\">\n\t\t\t\t\t\t<text class=\"iconfont icon-star\"></text>\n\t\t\t\t\t\t<text>设为默认</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 空状态 -->\n\t\t<view class=\"empty-state\" v-else>\n\t\t\t<text class=\"empty-icon\">📍</text>\n\t\t\t<text class=\"empty-text\">暂无收货地址</text>\n\t\t\t<text class=\"empty-desc\">添加收货地址，享受便捷购物体验</text>\n\t\t</view>\n\n\t\t<!-- 添加地址按钮 -->\n\t\t<view class=\"add-address-btn\" @click=\"addAddress\">\n\t\t\t<text class=\"btn-icon\">+</text>\n\t\t\t<text class=\"btn-text\">添加新地址</text>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nimport { addressAPI } from '@/api/index.js'\n\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\taddresses: [],\n\t\t\tloading: false,\n\t\t\tfromOrder: false // 是否从订单页面跳转过来\n\t\t}\n\t},\n\tonLoad(options) {\n\t\tthis.fromOrder = options.from === 'order'\n\t\tthis.loadAddresses()\n\t},\n\tonShow() {\n\t\t// 从添加/编辑页面返回时刷新列表\n\t\tthis.loadAddresses()\n\t},\n\tmethods: {\n\t\t// 加载地址列表\n\t\tasync loadAddresses() {\n\t\t\tif (this.loading) return\n\t\t\t\n\t\t\tthis.loading = true\n\t\t\ttry {\n\t\t\t\tconst response = await addressAPI.getList()\n\t\t\t\tthis.addresses = response.data || []\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('获取地址列表失败:', error)\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: error.message || '获取地址列表失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t})\n\t\t\t} finally {\n\t\t\t\tthis.loading = false\n\t\t\t}\n\t\t},\n\n\t\t// 选择地址（从订单页面跳转过来时）\n\t\tselectAddress(address) {\n\t\t\tif (this.fromOrder) {\n\t\t\t\t// 返回到订单页面并传递选中的地址\n\t\t\t\tconst pages = getCurrentPages()\n\t\t\t\tconst prevPage = pages[pages.length - 2]\n\t\t\t\t\n\t\t\t\tif (prevPage) {\n\t\t\t\t\tprevPage.$vm.selectedAddress = address\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tuni.navigateBack()\n\t\t\t}\n\t\t},\n\n\t\t// 添加地址\n\t\taddAddress() {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: '/pages/address/edit'\n\t\t\t})\n\t\t},\n\n\t\t// 编辑地址\n\t\teditAddress(address) {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: `/pages/address/edit?id=${address.id}`\n\t\t\t})\n\t\t},\n\n\t\t// 删除地址\n\t\tasync deleteAddress(address) {\n\t\t\ttry {\n\t\t\t\tawait uni.showModal({\n\t\t\t\t\ttitle: '确认删除',\n\t\t\t\t\tcontent: '确定要删除这个地址吗？',\n\t\t\t\t\tconfirmText: '删除',\n\t\t\t\t\tconfirmColor: '#ff4757'\n\t\t\t\t})\n\n\t\t\t\tawait addressAPI.delete(address.id)\n\t\t\t\t\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '删除成功',\n\t\t\t\t\ticon: 'success'\n\t\t\t\t})\n\n\t\t\t\t// 刷新列表\n\t\t\t\tthis.loadAddresses()\n\n\t\t\t} catch (error) {\n\t\t\t\tif (error.errMsg && error.errMsg.includes('cancel')) {\n\t\t\t\t\treturn // 用户取消\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tconsole.error('删除地址失败:', error)\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: error.message || '删除失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t})\n\t\t\t}\n\t\t},\n\n\t\t// 设为默认地址\n\t\tasync setDefault(address) {\n\t\t\ttry {\n\t\t\t\tawait addressAPI.setDefault(address.id)\n\t\t\t\t\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '设置成功',\n\t\t\t\t\ticon: 'success'\n\t\t\t\t})\n\n\t\t\t\t// 刷新列表\n\t\t\t\tthis.loadAddresses()\n\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('设置默认地址失败:', error)\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: error.message || '设置失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\t}\n}\n</script>\n\n<style scoped>\n.address-list-container {\n\tbackground: #f8f8f8;\n\tmin-height: 100vh;\n\tpadding: 10px;\n}\n\n.address-list {\n\tmargin-bottom: 80px;\n}\n\n.address-item {\n\tbackground: white;\n\tborder-radius: 12px;\n\tmargin-bottom: 10px;\n\tpadding: 20px;\n\tposition: relative;\n\tbox-shadow: 0 2px 8px rgba(0,0,0,0.1);\n}\n\n.address-content {\n\tmargin-bottom: 15px;\n}\n\n.address-header {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tmargin-bottom: 10px;\n}\n\n.name-phone {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.name {\n\tfont-size: 16px;\n\tfont-weight: bold;\n\tcolor: #333;\n\tmargin-right: 15px;\n}\n\n.phone {\n\tfont-size: 14px;\n\tcolor: #666;\n}\n\n.default-badge {\n\tbackground: #667eea;\n\tcolor: white;\n\tpadding: 4px 8px;\n\tborder-radius: 4px;\n\tfont-size: 12px;\n}\n\n.address-detail {\n\tfont-size: 14px;\n\tcolor: #666;\n\tline-height: 1.4;\n}\n\n.address-actions {\n\tdisplay: flex;\n\tjustify-content: flex-end;\n\tgap: 20px;\n}\n\n.action-btn {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tpadding: 8px;\n\tmin-width: 50px;\n}\n\n.action-btn text:first-child {\n\tfont-size: 18px;\n\tmargin-bottom: 4px;\n}\n\n.action-btn text:last-child {\n\tfont-size: 12px;\n\tcolor: #666;\n}\n\n.empty-state {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: center;\n\tpadding: 80px 20px;\n\ttext-align: center;\n}\n\n.empty-icon {\n\tfont-size: 60px;\n\tmargin-bottom: 20px;\n}\n\n.empty-text {\n\tfont-size: 16px;\n\tcolor: #333;\n\tmargin-bottom: 8px;\n}\n\n.empty-desc {\n\tfont-size: 14px;\n\tcolor: #999;\n}\n\n.add-address-btn {\n\tposition: fixed;\n\tbottom: 30px;\n\tleft: 20px;\n\tright: 20px;\n\tbackground: #667eea;\n\tcolor: white;\n\tpadding: 15px;\n\tborder-radius: 25px;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tbox-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);\n}\n\n.btn-icon {\n\tfont-size: 20px;\n\tmargin-right: 8px;\n}\n\n.btn-text {\n\tfont-size: 16px;\n\tfont-weight: bold;\n}\n\n/* 图标字体样式 */\n.iconfont {\n\tfont-family: 'iconfont';\n}\n\n.icon-edit::before { content: '✏️'; }\n.icon-delete::before { content: '🗑️'; }\n.icon-star::before { content: '⭐'; }\n</style>\n", "import MiniProgramPage from 'D:/app/miniprogram/SIYU/pages/address/list.vue'\nwx.createPage(MiniProgramPage)"], "names": ["addressAPI", "uni"], "mappings": ";;;AA0DA,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,WAAW,CAAE;AAAA,MACb,SAAS;AAAA,MACT,WAAW;AAAA;AAAA,IACZ;AAAA,EACA;AAAA,EACD,OAAO,SAAS;AACf,SAAK,YAAY,QAAQ,SAAS;AAClC,SAAK,cAAc;AAAA,EACnB;AAAA,EACD,SAAS;AAER,SAAK,cAAc;AAAA,EACnB;AAAA,EACD,SAAS;AAAA;AAAA,IAER,MAAM,gBAAgB;AACrB,UAAI,KAAK;AAAS;AAElB,WAAK,UAAU;AACf,UAAI;AACH,cAAM,WAAW,MAAMA,UAAU,WAAC,QAAQ;AAC1C,aAAK,YAAY,SAAS,QAAQ,CAAC;AAAA,MAClC,SAAO,OAAO;AACfC,sBAAAA,MAAA,MAAA,SAAA,gCAAc,aAAa,KAAK;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO,MAAM,WAAW;AAAA,UACxB,MAAM;AAAA,SACN;AAAA,MACF,UAAU;AACT,aAAK,UAAU;AAAA,MAChB;AAAA,IACA;AAAA;AAAA,IAGD,cAAc,SAAS;AACtB,UAAI,KAAK,WAAW;AAEnB,cAAM,QAAQ,gBAAgB;AAC9B,cAAM,WAAW,MAAM,MAAM,SAAS,CAAC;AAEvC,YAAI,UAAU;AACb,mBAAS,IAAI,kBAAkB;AAAA,QAChC;AAEAA,sBAAAA,MAAI,aAAa;AAAA,MAClB;AAAA,IACA;AAAA;AAAA,IAGD,aAAa;AACZA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,OACL;AAAA,IACD;AAAA;AAAA,IAGD,YAAY,SAAS;AACpBA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK,0BAA0B,QAAQ,EAAE;AAAA,OACzC;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,cAAc,SAAS;AAC5B,UAAI;AACH,cAAMA,cAAAA,MAAI,UAAU;AAAA,UACnB,OAAO;AAAA,UACP,SAAS;AAAA,UACT,aAAa;AAAA,UACb,cAAc;AAAA,SACd;AAED,cAAMD,qBAAW,OAAO,QAAQ,EAAE;AAElCC,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,SACN;AAGD,aAAK,cAAc;AAAA,MAElB,SAAO,OAAO;AACf,YAAI,MAAM,UAAU,MAAM,OAAO,SAAS,QAAQ,GAAG;AACpD;AAAA,QACD;AAEAA,sBAAAA,MAAA,MAAA,SAAA,iCAAc,WAAW,KAAK;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO,MAAM,WAAW;AAAA,UACxB,MAAM;AAAA,SACN;AAAA,MACF;AAAA,IACA;AAAA;AAAA,IAGD,MAAM,WAAW,SAAS;AACzB,UAAI;AACH,cAAMD,qBAAW,WAAW,QAAQ,EAAE;AAEtCC,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,SACN;AAGD,aAAK,cAAc;AAAA,MAElB,SAAO,OAAO;AACfA,sBAAAA,MAAA,MAAA,SAAA,iCAAc,aAAa,KAAK;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO,MAAM,WAAW;AAAA,UACxB,MAAM;AAAA,SACN;AAAA,MACF;AAAA,IACD;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjLA,GAAG,WAAW,eAAe;"}