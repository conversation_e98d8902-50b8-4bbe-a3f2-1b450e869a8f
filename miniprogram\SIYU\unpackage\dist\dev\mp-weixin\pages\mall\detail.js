"use strict";
const common_vendor = require("../../common/vendor.js");
const api_index = require("../../api/index.js");
const utils_request = require("../../utils/request.js");
const SvgIcon = () => "../../components/svg-icon/svg-icon.js";
const _sfc_main = {
  components: {
    SvgIcon
  },
  data() {
    return {
      productId: null,
      product: {
        id: 1,
        name: "商品名称",
        description: "这是一个很棒的商品，值得拥有。",
        emoji: "🎁",
        points_price: 100,
        market_price: "50.00",
        stock: 99,
        sold: 10,
        rating: "4.8",
        tags: ["热门", "推荐"]
      },
      isFavorite: false,
      exchanging: false,
      userPoints: 0,
      exchangeRules: [
        "积分兑换商品不支持退换货",
        "兑换成功后将在3-7个工作日内发货",
        "请确保收货地址准确无误",
        "如有质量问题请联系客服处理",
        "积分兑换商品不参与其他优惠活动"
      ]
    };
  },
  computed: {
    canExchange() {
      return this.product.stock > 0 && this.userPoints >= this.product.points_price;
    },
    exchangeButtonText() {
      if (this.product.stock <= 0) {
        return "库存不足";
      }
      if (this.userPoints < this.product.points_price) {
        return "积分不足";
      }
      return "立即兑换";
    }
  },
  onLoad(options) {
    if (options.id) {
      this.productId = options.id;
      this.loadProductDetail();
    }
    this.loadUserPoints();
    this.checkFavoriteStatus();
  },
  onShareAppMessage() {
    const shareImage = this.product.images && this.product.images.length > 0 ? this.product.images[0] : this.product.image || "";
    return {
      title: this.product.name,
      path: `/pages/mall/detail?id=${this.productId}`,
      imageUrl: shareImage
    };
  },
  methods: {
    // 加载商品详情
    async loadProductDetail() {
      try {
        const response = await api_index.productAPI.getDetail(this.productId);
        this.product = response.data;
        if (this.product.image) {
          this.product.image = utils_request.getImageUrl(this.product.image);
        }
        if (this.product.images && Array.isArray(this.product.images)) {
          this.product.images = this.product.images.map((img) => utils_request.getImageUrl(img));
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/mall/detail.vue:183", "获取商品详情失败:", error);
        const emojis = ["🎁", "☕", "🎧", "🔋", "📱", "🖱️", "⌨️", "💻", "📺", "🎮"];
        const names = ["精美礼品", "精美水杯", "蓝牙耳机", "充电宝", "手机支架", "无线鼠标", "键盘膜", "笔记本电脑", "智能电视", "游戏手柄"];
        const index = (this.productId - 1) % emojis.length;
        this.product = {
          id: this.productId,
          name: names[index] || `商品${this.productId}`,
          description: "这是一个很棒的商品，采用优质材料制作，工艺精良，性价比极高。适合日常使用，是您生活的好帮手。",
          emoji: emojis[index],
          points_price: Math.floor(Math.random() * 500) + 100,
          market_price: (Math.random() * 100 + 20).toFixed(2),
          stock: Math.floor(Math.random() * 100) + 10,
          sold: Math.floor(Math.random() * 50),
          rating: (Math.random() * 1 + 4).toFixed(1),
          tags: ["热门", "推荐", "限时"]
        };
      }
    },
    // 加载用户积分
    async loadUserPoints() {
      try {
        const token = common_vendor.index.getStorageSync("token");
        if (!token) {
          this.userPoints = 0;
          return;
        }
        const response = await api_index.pointsAPI.getBalance();
        this.userPoints = response.data.available_points || 0;
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/mall/detail.vue:217", "获取积分余额失败:", error);
        this.userPoints = 1580;
      }
    },
    // 检查收藏状态
    async checkFavoriteStatus() {
      try {
        this.isFavorite = false;
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/mall/detail.vue:229", "检查收藏状态失败:", error);
      }
    },
    // 预览图片
    previewImages(current, type = "main") {
      let images = [];
      let currentIndex = current;
      if (type === "detail") {
        images = this.product.images || [];
        currentIndex = current;
      } else {
        if (this.product.image) {
          images.push(this.product.image);
        }
        if (this.product.images && this.product.images.length > 0) {
          images = images.concat(this.product.images);
        }
        currentIndex = 0;
      }
      if (images.length === 0) {
        common_vendor.index.showToast({
          title: "暂无图片",
          icon: "none"
        });
        return;
      }
      common_vendor.index.previewImage({
        urls: images,
        current: images[currentIndex] || images[0]
      });
    },
    // 切换收藏状态
    async toggleFavorite() {
      try {
        const token = common_vendor.index.getStorageSync("token");
        if (!token) {
          common_vendor.index.showToast({
            title: "请先登录",
            icon: "none"
          });
          common_vendor.index.navigateTo({
            url: "/pages/login/login"
          });
          return;
        }
        if (this.isFavorite) {
          this.isFavorite = false;
          common_vendor.index.showToast({
            title: "已取消收藏",
            icon: "success"
          });
        } else {
          this.isFavorite = true;
          common_vendor.index.showToast({
            title: "收藏成功",
            icon: "success"
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/mall/detail.vue:306", "收藏操作失败:", error);
        common_vendor.index.showToast({
          title: "操作失败",
          icon: "none"
        });
      }
    },
    // 分享商品
    shareProduct() {
      common_vendor.index.showShareMenu({
        withShareTicket: true
      });
    },
    // 处理兑换
    async handleExchange() {
      if (!this.canExchange || this.exchanging)
        return;
      const token = common_vendor.index.getStorageSync("token");
      if (!token) {
        common_vendor.index.showToast({
          title: "请先登录",
          icon: "none"
        });
        common_vendor.index.navigateTo({
          url: "/pages/login/login"
        });
        return;
      }
      common_vendor.index.navigateTo({
        url: `/pages/order/confirm?productId=${this.productId}&quantity=1&type=exchange`
      });
    },
    // 处理图片加载错误
    handleImageError() {
      common_vendor.index.__f__("log", "at pages/mall/detail.vue:346", "图片加载失败，显示emoji");
    }
  }
};
if (!Array) {
  const _easycom_svg_icon2 = common_vendor.resolveComponent("svg-icon");
  _easycom_svg_icon2();
}
const _easycom_svg_icon = () => "../../components/svg-icon/svg-icon.js";
if (!Math) {
  _easycom_svg_icon();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.product.image
  }, $data.product.image ? {
    b: $data.product.image,
    c: common_vendor.o((...args) => $options.handleImageError && $options.handleImageError(...args))
  } : {
    d: common_vendor.t($data.product.emoji)
  }, {
    e: common_vendor.o(($event) => $options.previewImages(0)),
    f: common_vendor.t($data.product.name),
    g: common_vendor.t($data.product.description || "暂无详细描述"),
    h: common_vendor.t($data.product.points_price),
    i: $data.product.market_price
  }, $data.product.market_price ? {
    j: common_vendor.t($data.product.market_price)
  } : {}, {
    k: $data.product.tags && $data.product.tags.length
  }, $data.product.tags && $data.product.tags.length ? {
    l: common_vendor.f($data.product.tags, (tag, k0, i0) => {
      return {
        a: common_vendor.t(tag),
        b: tag
      };
    })
  } : {}, {
    m: common_vendor.t($data.product.stock || 0),
    n: common_vendor.t($data.product.sold || 0),
    o: common_vendor.t($data.product.rating || "5.0"),
    p: $data.product.images && $data.product.images.length > 0
  }, $data.product.images && $data.product.images.length > 0 ? {
    q: common_vendor.f($data.product.images, (img, index, i0) => {
      return {
        a: img,
        b: index,
        c: common_vendor.o(($event) => $options.previewImages(index, "detail"), index)
      };
    })
  } : {}, {
    r: common_vendor.f($data.exchangeRules, (rule, index, i0) => {
      return {
        a: common_vendor.t(index + 1),
        b: common_vendor.t(rule),
        c: index
      };
    }),
    s: common_vendor.p({
      name: $data.isFavorite ? "heart-filled" : "heart",
      size: 20,
      color: $data.isFavorite ? "#f56c6c" : "#999"
    }),
    t: common_vendor.t($data.isFavorite ? "已收藏" : "收藏"),
    v: common_vendor.o((...args) => $options.toggleFavorite && $options.toggleFavorite(...args)),
    w: common_vendor.p({
      name: "share",
      size: 20,
      color: "#999"
    }),
    x: common_vendor.o((...args) => $options.shareProduct && $options.shareProduct(...args)),
    y: common_vendor.t($options.exchangeButtonText),
    z: common_vendor.o((...args) => $options.handleExchange && $options.handleExchange(...args)),
    A: !$options.canExchange,
    B: $data.exchanging
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-da39d73f"]]);
_sfc_main.__runtimeHooks = 2;
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/mall/detail.js.map
