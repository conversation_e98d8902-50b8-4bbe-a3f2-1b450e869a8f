{"version": 3, "file": "region-picker.js", "sources": ["components/region-picker/region-picker.vue", "../../../Program Files/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovYXBwL21pbmlwcm9ncmFtL1NJWVUvY29tcG9uZW50cy9yZWdpb24tcGlja2VyL3JlZ2lvbi1waWNrZXIudnVl"], "sourcesContent": ["<template>\n\t<view class=\"region-picker\">\n\t\t<!-- 遮罩层 -->\n\t\t<view class=\"mask\" v-if=\"visible\" @click=\"close\"></view>\n\n\t\t<!-- 选择器主体 -->\n\t\t<view class=\"picker-container\" :class=\"{ 'show': visible }\">\n\t\t\t<!-- 头部 -->\n\t\t\t<view class=\"picker-header\">\n\t\t\t\t<text class=\"cancel-btn\" @click=\"close\">取消</text>\n\t\t\t\t<text class=\"title\">选择地区</text>\n\t\t\t\t<text class=\"confirm-btn\" @click=\"confirm\">确定</text>\n\t\t\t</view>\n\n\t\t\t<!-- 标签栏 -->\n\t\t\t<view class=\"tabs\">\n\t\t\t\t<view\n\t\t\t\t\tclass=\"tab-item\"\n\t\t\t\t\t:class=\"{ 'active': currentTab === 0 }\"\n\t\t\t\t\t@click=\"switchTab(0)\"\n\t\t\t\t>\n\t\t\t\t\t<text>{{ selectedProvince ? selectedProvince.name : '请选择' }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view\n\t\t\t\t\tclass=\"tab-item\"\n\t\t\t\t\t:class=\"{ 'active': currentTab === 1 }\"\n\t\t\t\t\t@click=\"switchTab(1)\"\n\t\t\t\t\tv-if=\"selectedProvince\"\n\t\t\t\t>\n\t\t\t\t\t<text>{{ selectedCity ? selectedCity.name : '请选择' }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view\n\t\t\t\t\tclass=\"tab-item\"\n\t\t\t\t\t:class=\"{ 'active': currentTab === 2 }\"\n\t\t\t\t\t@click=\"switchTab(2)\"\n\t\t\t\t\tv-if=\"selectedCity\"\n\t\t\t\t>\n\t\t\t\t\t<text>{{ selectedDistrict ? selectedDistrict.name : '请选择' }}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 列表内容 -->\n\t\t\t<scroll-view class=\"picker-content\" scroll-y>\n\t\t\t\t<!-- 省份列表 -->\n\t\t\t\t<view v-if=\"currentTab === 0\">\n\t\t\t\t\t<view\n\t\t\t\t\t\tclass=\"list-item\"\n\t\t\t\t\t\t:class=\"{ 'selected': item.code === (selectedProvince && selectedProvince.code) }\"\n\t\t\t\t\t\tv-for=\"item in provinces\"\n\t\t\t\t\t\t:key=\"item.code\"\n\t\t\t\t\t\t@click=\"selectProvince(item)\"\n\t\t\t\t\t>\n\t\t\t\t\t\t<text class=\"item-name\">{{ item.name }}</text>\n\t\t\t\t\t\t<text class=\"check-icon\" v-if=\"item.code === (selectedProvince && selectedProvince.code)\">✓</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 城市列表 -->\n\t\t\t\t<view v-if=\"currentTab === 1\">\n\t\t\t\t\t<view\n\t\t\t\t\t\tclass=\"list-item\"\n\t\t\t\t\t\t:class=\"{ 'selected': item.code === (selectedCity && selectedCity.code) }\"\n\t\t\t\t\t\tv-for=\"item in cities\"\n\t\t\t\t\t\t:key=\"item.code\"\n\t\t\t\t\t\t@click=\"selectCity(item)\"\n\t\t\t\t\t>\n\t\t\t\t\t\t<text class=\"item-name\">{{ item.name }}</text>\n\t\t\t\t\t\t<text class=\"check-icon\" v-if=\"item.code === (selectedCity && selectedCity.code)\">✓</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 区县列表 -->\n\t\t\t\t<view v-if=\"currentTab === 2\">\n\t\t\t\t\t<view\n\t\t\t\t\t\tclass=\"list-item\"\n\t\t\t\t\t\t:class=\"{ 'selected': item.code === (selectedDistrict && selectedDistrict.code) }\"\n\t\t\t\t\t\tv-for=\"item in districts\"\n\t\t\t\t\t\t:key=\"item.code\"\n\t\t\t\t\t\t@click=\"selectDistrict(item)\"\n\t\t\t\t\t>\n\t\t\t\t\t\t<text class=\"item-name\">{{ item.name }}</text>\n\t\t\t\t\t\t<text class=\"check-icon\" v-if=\"item.code === (selectedDistrict && selectedDistrict.code)\">✓</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</scroll-view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nimport { regions, getCitiesByProvince, getDistrictsByCity } from '@/static/data/regions.js'\n\nexport default {\n\tname: 'RegionPicker',\n\tprops: {\n\t\tvisible: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: false\n\t\t},\n\t\tvalue: {\n\t\t\ttype: Object,\n\t\t\tdefault: () => ({})\n\t\t}\n\t},\n\tdata() {\n\t\treturn {\n\t\t\tcurrentTab: 0,\n\t\t\tselectedProvince: null,\n\t\t\tselectedCity: null,\n\t\t\tselectedDistrict: null,\n\t\t\tprovinces: regions,\n\t\t\tcities: [],\n\t\t\tdistricts: []\n\t\t}\n\t},\n\twatch: {\n\t\tvisible(newVal) {\n\t\t\tif (newVal) {\n\t\t\t\tthis.initData()\n\t\t\t}\n\t\t},\n\t\tvalue: {\n\t\t\thandler(newVal) {\n\t\t\t\tif (newVal && newVal.province) {\n\t\t\t\t\tthis.initFromValue(newVal)\n\t\t\t\t}\n\t\t\t},\n\t\t\timmediate: true\n\t\t}\n\t},\n\tmethods: {\n\t\t// 初始化数据\n\t\tinitData() {\n\t\t\tif (this.value && this.value.province) {\n\t\t\t\tthis.initFromValue(this.value)\n\t\t\t} else {\n\t\t\t\tthis.currentTab = 0\n\t\t\t\tthis.selectedProvince = null\n\t\t\t\tthis.selectedCity = null\n\t\t\t\tthis.selectedDistrict = null\n\t\t\t\tthis.cities = []\n\t\t\t\tthis.districts = []\n\t\t\t}\n\t\t},\n\n\t\t// 从传入值初始化\n\t\tinitFromValue(value) {\n\t\t\t// 查找省份\n\t\t\tthis.selectedProvince = this.provinces.find(p => p.name === value.province)\n\t\t\tif (this.selectedProvince) {\n\t\t\t\tthis.cities = getCitiesByProvince(this.selectedProvince.code)\n\n\t\t\t\t// 查找城市\n\t\t\t\tif (value.city) {\n\t\t\t\t\tthis.selectedCity = this.cities.find(c => c.name === value.city)\n\t\t\t\t\tif (this.selectedCity) {\n\t\t\t\t\t\tthis.districts = getDistrictsByCity(this.selectedCity.code)\n\n\t\t\t\t\t\t// 查找区县\n\t\t\t\t\t\tif (value.district) {\n\t\t\t\t\t\t\tthis.selectedDistrict = this.districts.find(d => d.name === value.district)\n\t\t\t\t\t\t\tthis.currentTab = 2\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tthis.currentTab = 1\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.currentTab = 1\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tthis.currentTab = 0\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\n\t\t// 切换标签\n\t\tswitchTab(index) {\n\t\t\tthis.currentTab = index\n\t\t},\n\n\t\t// 选择省份\n\t\tselectProvince(province) {\n\t\t\tthis.selectedProvince = province\n\t\t\tthis.selectedCity = null\n\t\t\tthis.selectedDistrict = null\n\t\t\tthis.cities = getCitiesByProvince(province.code)\n\t\t\tthis.districts = []\n\t\t\tthis.currentTab = 1\n\t\t},\n\n\t\t// 选择城市\n\t\tselectCity(city) {\n\t\t\tthis.selectedCity = city\n\t\t\tthis.selectedDistrict = null\n\t\t\tthis.districts = getDistrictsByCity(city.code)\n\t\t\tthis.currentTab = 2\n\t\t},\n\n\t\t// 选择区县\n\t\tselectDistrict(district) {\n\t\t\tthis.selectedDistrict = district\n\t\t\t// 自动确认\n\t\t\tsetTimeout(() => {\n\t\t\t\tthis.confirm()\n\t\t\t}, 300)\n\t\t},\n\n\t\t// 确认选择\n\t\tconfirm() {\n\t\t\tif (!this.selectedProvince || !this.selectedCity || !this.selectedDistrict) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请选择完整的地区信息',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t})\n\t\t\t\treturn\n\t\t\t}\n\n\t\t\tconst result = {\n\t\t\t\tprovince: this.selectedProvince.name,\n\t\t\t\tcity: this.selectedCity.name,\n\t\t\t\tdistrict: this.selectedDistrict.name,\n\t\t\t\tprovinceCode: this.selectedProvince.code,\n\t\t\t\tcityCode: this.selectedCity.code,\n\t\t\t\tdistrictCode: this.selectedDistrict.code\n\t\t\t}\n\n\t\t\tthis.$emit('confirm', result)\n\t\t\tthis.close()\n\t\t},\n\n\t\t// 关闭选择器\n\t\tclose() {\n\t\t\tthis.$emit('close')\n\t\t}\n\t}\n}\n</script>\n\n<style scoped>\n.region-picker {\n\tposition: relative;\n}\n\n.mask {\n\tposition: fixed;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tbottom: 0;\n\tbackground: rgba(0, 0, 0, 0.5);\n\tz-index: 999;\n}\n\n.picker-container {\n\tposition: fixed;\n\tbottom: 0;\n\tleft: 0;\n\tright: 0;\n\tbackground: white;\n\tborder-radius: 20px 20px 0 0;\n\tmax-height: 70vh;\n\ttransform: translateY(100%);\n\ttransition: transform 0.3s ease;\n\tz-index: 1000;\n}\n\n.picker-container.show {\n\ttransform: translateY(0);\n}\n\n.picker-header {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tpadding: 20px;\n\tborder-bottom: 1px solid #f0f0f0;\n}\n\n.cancel-btn, .confirm-btn {\n\tfont-size: 16px;\n\tcolor: #667eea;\n}\n\n.title {\n\tfont-size: 18px;\n\tfont-weight: bold;\n\tcolor: #333;\n}\n\n.tabs {\n\tdisplay: flex;\n\tborder-bottom: 1px solid #f0f0f0;\n}\n\n.tab-item {\n\tflex: 1;\n\tpadding: 15px;\n\ttext-align: center;\n\tborder-bottom: 2px solid transparent;\n\tcolor: #666;\n}\n\n.tab-item.active {\n\tcolor: #667eea;\n\tborder-bottom-color: #667eea;\n}\n\n.picker-content {\n\theight: 400px;\n}\n\n.list-item {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tpadding: 15px 20px;\n\tborder-bottom: 1px solid #f8f8f8;\n}\n\n.list-item.selected {\n\tbackground: #f8f9ff;\n}\n\n.item-name {\n\tfont-size: 16px;\n\tcolor: #333;\n}\n\n.list-item.selected .item-name {\n\tcolor: #667eea;\n}\n\n.check-icon {\n\tcolor: #667eea;\n\tfont-size: 18px;\n\tfont-weight: bold;\n}\n</style>\n", "import Component from 'D:/app/miniprogram/SIYU/components/region-picker/region-picker.vue'\nwx.createComponent(Component)"], "names": ["regions", "getCitiesByProvince", "getDistrictsByCity", "uni"], "mappings": ";;;AA4FA,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,OAAO;AAAA,IACN,SAAS;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,IACD,OAAO;AAAA,MACN,MAAM;AAAA,MACN,SAAS,OAAO,CAAA;AAAA,IACjB;AAAA,EACA;AAAA,EACD,OAAO;AACN,WAAO;AAAA,MACN,YAAY;AAAA,MACZ,kBAAkB;AAAA,MAClB,cAAc;AAAA,MACd,kBAAkB;AAAA,MAClB,WAAWA,oBAAO;AAAA,MAClB,QAAQ,CAAE;AAAA,MACV,WAAW,CAAC;AAAA,IACb;AAAA,EACA;AAAA,EACD,OAAO;AAAA,IACN,QAAQ,QAAQ;AACf,UAAI,QAAQ;AACX,aAAK,SAAS;AAAA,MACf;AAAA,IACA;AAAA,IACD,OAAO;AAAA,MACN,QAAQ,QAAQ;AACf,YAAI,UAAU,OAAO,UAAU;AAC9B,eAAK,cAAc,MAAM;AAAA,QAC1B;AAAA,MACA;AAAA,MACD,WAAW;AAAA,IACZ;AAAA,EACA;AAAA,EACD,SAAS;AAAA;AAAA,IAER,WAAW;AACV,UAAI,KAAK,SAAS,KAAK,MAAM,UAAU;AACtC,aAAK,cAAc,KAAK,KAAK;AAAA,aACvB;AACN,aAAK,aAAa;AAClB,aAAK,mBAAmB;AACxB,aAAK,eAAe;AACpB,aAAK,mBAAmB;AACxB,aAAK,SAAS,CAAC;AACf,aAAK,YAAY,CAAC;AAAA,MACnB;AAAA,IACA;AAAA;AAAA,IAGD,cAAc,OAAO;AAEpB,WAAK,mBAAmB,KAAK,UAAU,KAAK,OAAK,EAAE,SAAS,MAAM,QAAQ;AAC1E,UAAI,KAAK,kBAAkB;AAC1B,aAAK,SAASC,oBAAAA,oBAAoB,KAAK,iBAAiB,IAAI;AAG5D,YAAI,MAAM,MAAM;AACf,eAAK,eAAe,KAAK,OAAO,KAAK,OAAK,EAAE,SAAS,MAAM,IAAI;AAC/D,cAAI,KAAK,cAAc;AACtB,iBAAK,YAAYC,oBAAAA,mBAAmB,KAAK,aAAa,IAAI;AAG1D,gBAAI,MAAM,UAAU;AACnB,mBAAK,mBAAmB,KAAK,UAAU,KAAK,OAAK,EAAE,SAAS,MAAM,QAAQ;AAC1E,mBAAK,aAAa;AAAA,mBACZ;AACN,mBAAK,aAAa;AAAA,YACnB;AAAA,iBACM;AACN,iBAAK,aAAa;AAAA,UACnB;AAAA,eACM;AACN,eAAK,aAAa;AAAA,QACnB;AAAA,MACD;AAAA,IACA;AAAA;AAAA,IAGD,UAAU,OAAO;AAChB,WAAK,aAAa;AAAA,IAClB;AAAA;AAAA,IAGD,eAAe,UAAU;AACxB,WAAK,mBAAmB;AACxB,WAAK,eAAe;AACpB,WAAK,mBAAmB;AACxB,WAAK,SAASD,wCAAoB,SAAS,IAAI;AAC/C,WAAK,YAAY,CAAC;AAClB,WAAK,aAAa;AAAA,IAClB;AAAA;AAAA,IAGD,WAAW,MAAM;AAChB,WAAK,eAAe;AACpB,WAAK,mBAAmB;AACxB,WAAK,YAAYC,uCAAmB,KAAK,IAAI;AAC7C,WAAK,aAAa;AAAA,IAClB;AAAA;AAAA,IAGD,eAAe,UAAU;AACxB,WAAK,mBAAmB;AAExB,iBAAW,MAAM;AAChB,aAAK,QAAQ;AAAA,MACb,GAAE,GAAG;AAAA,IACN;AAAA;AAAA,IAGD,UAAU;AACT,UAAI,CAAC,KAAK,oBAAoB,CAAC,KAAK,gBAAgB,CAAC,KAAK,kBAAkB;AAC3EC,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,SACN;AACD;AAAA,MACD;AAEA,YAAM,SAAS;AAAA,QACd,UAAU,KAAK,iBAAiB;AAAA,QAChC,MAAM,KAAK,aAAa;AAAA,QACxB,UAAU,KAAK,iBAAiB;AAAA,QAChC,cAAc,KAAK,iBAAiB;AAAA,QACpC,UAAU,KAAK,aAAa;AAAA,QAC5B,cAAc,KAAK,iBAAiB;AAAA,MACrC;AAEA,WAAK,MAAM,WAAW,MAAM;AAC5B,WAAK,MAAM;AAAA,IACX;AAAA;AAAA,IAGD,QAAQ;AACP,WAAK,MAAM,OAAO;AAAA,IACnB;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzOA,GAAG,gBAAgB,SAAS;"}