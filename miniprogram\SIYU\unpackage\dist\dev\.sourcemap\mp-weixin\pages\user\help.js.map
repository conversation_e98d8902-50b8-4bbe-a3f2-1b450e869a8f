{"version": 3, "file": "help.js", "sources": ["pages/user/help.vue", "../../../Program Files/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvdXNlci9oZWxwLnZ1ZQ"], "sourcesContent": ["<template>\n\t<view class=\"help-container\">\n\t\t<!-- 搜索框 -->\n\t\t<view class=\"search-section\">\n\t\t\t<view class=\"search-box\">\n\t\t\t\t<uni-icons type=\"search\" size=\"16\" color=\"#999\"></uni-icons>\n\t\t\t\t<input \n\t\t\t\t\tclass=\"search-input\" \n\t\t\t\t\tplaceholder=\"搜索帮助内容\"\n\t\t\t\t\tv-model=\"searchKeyword\"\n\t\t\t\t\t@input=\"onSearch\"\n\t\t\t\t/>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 常见问题 -->\n\t\t<view class=\"section\">\n\t\t\t<view class=\"section-title\">常见问题</view>\n\t\t\t<view class=\"faq-list\">\n\t\t\t\t<view \n\t\t\t\t\tv-for=\"(item, index) in filteredFaqList\" \n\t\t\t\t\t:key=\"index\"\n\t\t\t\t\tclass=\"faq-item\"\n\t\t\t\t\t@click=\"toggleFaq(index)\"\n\t\t\t\t>\n\t\t\t\t\t<view class=\"faq-question\">\n\t\t\t\t\t\t<text class=\"question-text\">{{ item.question }}</text>\n\t\t\t\t\t\t<uni-icons \n\t\t\t\t\t\t\t:type=\"item.expanded ? 'up' : 'down'\" \n\t\t\t\t\t\t\tsize=\"14\" \n\t\t\t\t\t\t\tcolor=\"#999\"\n\t\t\t\t\t\t></uni-icons>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view v-if=\"item.expanded\" class=\"faq-answer\">\n\t\t\t\t\t\t<text class=\"answer-text\">{{ item.answer }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 联系我们 -->\n\t\t<view class=\"section\">\n\t\t\t<view class=\"section-title\">联系我们</view>\n\t\t\t<view class=\"contact-list\">\n\t\t\t\t<view class=\"contact-item\" @click=\"callService\">\n\t\t\t\t\t<view class=\"contact-left\">\n\t\t\t\t\t\t<uni-icons type=\"phone\" size=\"20\" color=\"#667eea\"></uni-icons>\n\t\t\t\t\t\t<text class=\"contact-text\">客服电话</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<text class=\"contact-value\">************</text>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"contact-item\" @click=\"copyEmail\">\n\t\t\t\t\t<view class=\"contact-left\">\n\t\t\t\t\t\t<uni-icons type=\"email\" size=\"20\" color=\"#e74c3c\"></uni-icons>\n\t\t\t\t\t\t<text class=\"contact-text\">客服邮箱</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<text class=\"contact-value\"><EMAIL></text>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"contact-item\">\n\t\t\t\t\t<view class=\"contact-left\">\n\t\t\t\t\t\t<uni-icons type=\"clock\" size=\"20\" color=\"#f39c12\"></uni-icons>\n\t\t\t\t\t\t<text class=\"contact-text\">服务时间</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<text class=\"contact-value\">9:00-18:00</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 意见反馈 -->\n\t\t<view class=\"section\">\n\t\t\t<button class=\"feedback-btn\" @click=\"goToFeedback\">\n\t\t\t\t<uni-icons type=\"compose\" size=\"18\" color=\"white\"></uni-icons>\n\t\t\t\t<text class=\"feedback-text\">意见反馈</text>\n\t\t\t</button>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tsearchKeyword: '',\n\t\t\tfaqList: [\n\t\t\t\t{\n\t\t\t\t\tquestion: '如何使用积分兑换商品？',\n\t\t\t\t\tanswer: '在商品详情页点击\"立即兑换\"，选择收货地址，确认订单即可完成兑换。',\n\t\t\t\t\texpanded: false\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tquestion: '积分有有效期吗？',\n\t\t\t\t\tanswer: '积分永久有效，不会过期。您可以随时使用积分兑换心仪的商品。',\n\t\t\t\t\texpanded: false\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tquestion: '如何获得更多积分？',\n\t\t\t\t\tanswer: '您可以通过每日签到、完成任务、邀请好友等方式获得积分奖励。',\n\t\t\t\t\texpanded: false\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tquestion: '订单发货后多久能收到？',\n\t\t\t\t\tanswer: '一般情况下，订单发货后3-7个工作日内可以收到商品，具体时间根据地区而定。',\n\t\t\t\t\texpanded: false\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tquestion: '可以退换货吗？',\n\t\t\t\t\tanswer: '商品如有质量问题，支持7天无理由退换货。请联系客服处理。',\n\t\t\t\t\texpanded: false\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\tquestion: '如何修改收货地址？',\n\t\t\t\t\tanswer: '在\"我的-收货地址\"中可以添加、编辑或删除收货地址。',\n\t\t\t\t\texpanded: false\n\t\t\t\t}\n\t\t\t]\n\t\t}\n\t},\n\t\n\tcomputed: {\n\t\tfilteredFaqList() {\n\t\t\tif (!this.searchKeyword) {\n\t\t\t\treturn this.faqList\n\t\t\t}\n\t\t\treturn this.faqList.filter(item => \n\t\t\t\titem.question.includes(this.searchKeyword) || \n\t\t\t\titem.answer.includes(this.searchKeyword)\n\t\t\t)\n\t\t}\n\t},\n\t\n\tmethods: {\n\t\t// 搜索\n\t\tonSearch() {\n\t\t\t// 搜索逻辑已在computed中处理\n\t\t},\n\t\t\n\t\t// 切换FAQ展开状态\n\t\ttoggleFaq(index) {\n\t\t\tthis.faqList[index].expanded = !this.faqList[index].expanded\n\t\t},\n\t\t\n\t\t// 拨打客服电话\n\t\tcallService() {\n\t\t\tuni.makePhoneCall({\n\t\t\t\tphoneNumber: '************'\n\t\t\t})\n\t\t},\n\t\t\n\t\t// 复制邮箱\n\t\tcopyEmail() {\n\t\t\tuni.setClipboardData({\n\t\t\t\tdata: '<EMAIL>',\n\t\t\t\tsuccess: () => {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '邮箱已复制',\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t})\n\t\t},\n\t\t\n\t\t// 意见反馈\n\t\tgoToFeedback() {\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '功能开发中',\n\t\t\t\ticon: 'none'\n\t\t\t})\n\t\t}\n\t}\n}\n</script>\n\n<style scoped>\n.help-container {\n\tmin-height: 100vh;\n\tbackground-color: #f5f5f5;\n}\n\n/* 搜索区域 */\n.search-section {\n\tbackground: white;\n\tpadding: 30rpx;\n\tmargin-bottom: 20rpx;\n}\n\n.search-box {\n\tdisplay: flex;\n\talign-items: center;\n\tbackground: #f8f9fa;\n\tborder-radius: 50rpx;\n\tpadding: 20rpx 30rpx;\n}\n\n.search-input {\n\tflex: 1;\n\tmargin-left: 20rpx;\n\tfont-size: 28rpx;\n\tcolor: #333;\n}\n\n/* 通用区域样式 */\n.section {\n\tbackground: white;\n\tmargin-bottom: 20rpx;\n\tpadding: 30rpx;\n}\n\n.section-title {\n\tfont-size: 32rpx;\n\tfont-weight: 600;\n\tcolor: #333;\n\tmargin-bottom: 30rpx;\n}\n\n/* FAQ样式 */\n.faq-list {\n\t\n}\n\n.faq-item {\n\tborder-bottom: 1rpx solid #f0f0f0;\n\tpadding: 30rpx 0;\n}\n\n.faq-item:last-child {\n\tborder-bottom: none;\n}\n\n.faq-question {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tcursor: pointer;\n}\n\n.question-text {\n\tfont-size: 28rpx;\n\tcolor: #333;\n\tfont-weight: 500;\n}\n\n.faq-answer {\n\tmargin-top: 20rpx;\n\tpadding-top: 20rpx;\n\tborder-top: 1rpx solid #f8f9fa;\n}\n\n.answer-text {\n\tfont-size: 26rpx;\n\tcolor: #666;\n\tline-height: 1.6;\n}\n\n/* 联系我们样式 */\n.contact-list {\n\t\n}\n\n.contact-item {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tpadding: 30rpx 0;\n\tborder-bottom: 1rpx solid #f0f0f0;\n}\n\n.contact-item:last-child {\n\tborder-bottom: none;\n}\n\n.contact-left {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.contact-text {\n\tfont-size: 28rpx;\n\tcolor: #333;\n\tmargin-left: 20rpx;\n}\n\n.contact-value {\n\tfont-size: 26rpx;\n\tcolor: #667eea;\n}\n\n/* 反馈按钮 */\n.feedback-btn {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n\tcolor: white;\n\tborder: none;\n\tborder-radius: 50rpx;\n\tpadding: 30rpx;\n\twidth: 100%;\n}\n\n.feedback-text {\n\tmargin-left: 10rpx;\n\tfont-size: 28rpx;\n}\n</style>\n", "import MiniProgramPage from 'D:/app/miniprogram/SIYU/pages/user/help.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AAiFA,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,eAAe;AAAA,MACf,SAAS;AAAA,QACR;AAAA,UACC,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,UAAU;AAAA,QACV;AAAA,QACD;AAAA,UACC,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,UAAU;AAAA,QACV;AAAA,QACD;AAAA,UACC,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,UAAU;AAAA,QACV;AAAA,QACD;AAAA,UACC,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,UAAU;AAAA,QACV;AAAA,QACD;AAAA,UACC,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,UAAU;AAAA,QACV;AAAA,QACD;AAAA,UACC,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,UAAU;AAAA,QACX;AAAA,MACD;AAAA,IACD;AAAA,EACA;AAAA,EAED,UAAU;AAAA,IACT,kBAAkB;AACjB,UAAI,CAAC,KAAK,eAAe;AACxB,eAAO,KAAK;AAAA,MACb;AACA,aAAO,KAAK,QAAQ;AAAA,QAAO,UAC1B,KAAK,SAAS,SAAS,KAAK,aAAa,KACzC,KAAK,OAAO,SAAS,KAAK,aAAa;AAAA,MACxC;AAAA,IACD;AAAA,EACA;AAAA,EAED,SAAS;AAAA;AAAA,IAER,WAAW;AAAA,IAEV;AAAA;AAAA,IAGD,UAAU,OAAO;AAChB,WAAK,QAAQ,KAAK,EAAE,WAAW,CAAC,KAAK,QAAQ,KAAK,EAAE;AAAA,IACpD;AAAA;AAAA,IAGD,cAAc;AACbA,oBAAAA,MAAI,cAAc;AAAA,QACjB,aAAa;AAAA,OACb;AAAA,IACD;AAAA;AAAA,IAGD,YAAY;AACXA,oBAAAA,MAAI,iBAAiB;AAAA,QACpB,MAAM;AAAA,QACN,SAAS,MAAM;AACdA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,WACN;AAAA,QACF;AAAA,OACA;AAAA,IACD;AAAA;AAAA,IAGD,eAAe;AACdA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,OACN;AAAA,IACF;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC1KA,GAAG,WAAW,eAAe;"}